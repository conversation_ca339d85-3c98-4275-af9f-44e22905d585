# Technology Stack

## Build System
- **Gradle** with Kotl<PERSON> DSL (build.gradle.kts)
- **Android Gradle Plugin** 8.11.1
- **Kotlin** 2.0.21

## Core Technologies
- **Android SDK**: Target SDK 36, Min SDK 24
- **Kotlin**: Primary programming language
- **Jetpack Compose**: Modern UI toolkit with Compose Compiler
- **Material 3**: Design system for consistent UI

## Key Dependencies
- **AndroidX Core KTX**: Core Android extensions
- **Lifecycle Runtime KTX**: Lifecycle-aware components
- **Activity Compose**: Compose integration for activities
- **Compose BOM**: Bill of materials for Compose versions
- **Material 3**: Material Design 3 components

## Testing Framework
- **JUnit 4**: Unit testing
- **AndroidX Test**: Android instrumentation tests
- **Espresso**: UI testing
- **Compose UI Test**: Compose-specific UI testing

## Common Commands

### Build & Run
```bash
# Clean build
./gradlew clean

# Build debug APK
./gradlew assembleDebug

# Build release APK
./gradlew assembleRelease

# Install debug build
./gradlew installDebug

# Run unit tests
./gradlew test

# Run instrumentation tests
./gradlew connectedAndroidTest
```

### Development
```bash
# Check dependencies
./gradlew dependencies

# Lint check
./gradlew lint

# Generate build reports
./gradlew build --scan
```

## Code Style
- Use Kotlin coding conventions
- Follow Material 3 design guidelines
- Implement Compose best practices
- Use AndroidX libraries over legacy support libraries