# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Communication Preferences
- 总是用中文和我交流

## 项目概述
这是一个名为"lovers"的情侣Android应用，使用Kotlin + Jetpack Compose开发，面向Android 24+。应用提供浪漫的情侣互动功能，包括多种主题风格和情侣同步功能。

## 开发环境设置和常用命令

### 构建和运行
```bash
# 清理项目
./gradlew clean

# 构建debug版本
./gradlew assembleDebug  

# 构建release版本
./gradlew assembleRelease

# 安装debug版本到设备
./gradlew installDebug
```

### 测试
```bash
# 运行单元测试
./gradlew test

# 运行UI测试（需要设备或模拟器）
./gradlew connectedAndroidTest

# 运行特定测试类
./gradlew test --tests "com.xue.love.ExampleUnitTest"
```

### 代码质量检查
```bash
# 检查代码风格（如果配置了ktlint）
./gradlew ktlintCheck

# 自动修复代码风格
./gradlew ktlintFormat
```

## 项目架构

### 技术栈
- **Language**: Kotlin
- **UI Framework**: Jetpack Compose
- **Architecture**: MVVM + Repository Pattern ✅
- **Dependency Injection**: Hilt ✅
- **数据库**: Room ✅
- **网络**: Retrofit + OkHttp (准备中)  
- **安全**: 生物识别、SQLCipher加密数据库 (准备中)

### 包结构
```
com.xue.love/
├── CoupleApplication.kt        # Application类 (已配置Hilt)
├── MainActivity.kt             # 主Activity (已配置Hilt)
├── data/                       # 数据层 ✅
│   ├── local/                  # 本地数据源 ✅
│   │   ├── dao/               # Room DAO接口 (6个)
│   │   ├── entity/            # 数据实体类 (6个)
│   │   └── database/          # 数据库配置
│   ├── remote/                 # 远程数据源 (待实现)
│   └── repository/             # 数据仓库 ✅
├── di/                         # 依赖注入 ✅
│   └── AppModule.kt           # Hilt模块配置
├── domain/                     # 业务逻辑层 (待实现)
└── ui/                         # UI层
    ├── components/             # 可复用组件
    ├── navigation/             # 导航管理
    ├── screens/                # 各个屏幕 (已集成ViewModel)
    └── theme/                  # 主题系统
```

### 核心特性

#### 🏗️ MVVM架构 (已实现)
- **ViewModel**: 使用Hilt注入，管理UI状态和业务逻辑
- **Repository**: 统一数据访问接口，封装本地和远程数据源
- **LiveData/StateFlow**: 响应式数据流，自动更新UI
- **依赖注入**: Hilt管理依赖关系，支持测试和模块化

#### 🗄️ Room数据库 (已实现)
- **6个核心实体**: User、ChatMessage、UserStats、Achievement、CoupleProfile、AlbumPhoto
- **完整DAO层**: 支持CRUD、分页查询、统计分析、响应式数据观察
- **类型转换器**: 支持复杂数据类型存储
- **数据库版本管理**: fallbackToDestructiveMigration配置

#### 📊 数据管理
- **实时同步**: Flow观察数据变化，UI自动刷新
- **统计系统**: 用户行为追踪、亲密度计算、互动统计
- **成就系统**: 游戏化体验，进度追踪和解锁机制
- **相册管理**: 图片元数据存储，支持私密相册功能

#### 主题系统 (ui/theme/)
- **ThemeManager**: 集中管理主题状态，支持4种主题（甜蜜粉、激情红、神秘紫、优雅黑）
- **动态主题切换**: 支持深色模式和情侣主题同步功能
- **渐变色系统**: 每个主题都有对应的渐变色和透明度变体

#### 🎯 ViewModel集成 (已实现)
- **HomeViewModel**: 主页数据管理，实时统计和亲密度跟踪
- **ChatViewModel**: 聊天消息管理，支持多种消息类型和特效
- **ProfileViewModel**: 用户资料管理，设置同步和成就系统

#### 导航系统 (ui/navigation/)
- 使用**AnimatedContent**实现流畅的页面切换动画
- 3个主要页面：主页(Home)、聊天(Chat)、个人资料(Profile)
- **NavigationState**数据类管理导航状态

#### UI组件系统 (ui/components/)  
- **CoupleBottomNavigation**: 底部导航栏
- **CoupleFloatingActionButton**: 浮动操作按钮
- **RomanticBackground**: 浪漫背景组件
- **HeartParticleEffect**: 爱心粒子效果
- **PairingSuccessAnimation**: 配对成功动画

## 开发注意事项

### 版本配置
- **compileSdk**: 36
- **minSdk**: 24  
- **targetSdk**: 36
- **Kotlin**: 2.0.21
- **Compose BOM**: 2024.09.00

### 构建配置
- **Gradle Plugin**: 8.11.1
- **Hilt**: 2.51.1 (已配置)
- **Room**: 2.6.1 (已配置)
- **Navigation Compose**: 2.8.5
- **KAPT**: 使用`kotlin("kapt")`避免版本冲突

### gradlew权限
gradlew文件已设置执行权限：
```bash
chmod +x gradlew
```

### 依赖管理
项目使用**Gradle Version Catalogs** (libs.versions.toml)管理依赖，已配置：
- ✅ Hilt (依赖注入)
- ✅ Room (数据库)  
- 🔄 Retrofit (网络请求) - 待实现
- 🔄 CameraX (相机功能) - 待实现
- 🔄 Biometric (生物识别) - 待实现

## 重要文件说明

### 核心应用文件
- **MainActivity.kt**: 应用入口，已配置Hilt，包含加载屏幕和主要内容布局
- **CoupleApplication.kt**: Application类，已配置@HiltAndroidApp
- **ThemeManager.kt**: 主题状态管理的核心类，使用单例模式
- **CoupleNavigation.kt**: 负责页面间导航和动画

### 数据层核心文件
- **CoupleDatabase.kt**: Room数据库主类，管理6个实体和DAO
- **CoupleRepository.kt**: 数据仓库，统一数据访问接口
- **AppModule.kt**: Hilt依赖注入模块，提供数据库和Repository实例

### ViewModel文件
- **HomeViewModel.kt**: 主页业务逻辑，集成Repository进行数据管理
- **ChatViewModel.kt**: 聊天功能，消息存储和实时更新
- **ProfileViewModel.kt**: 用户资料，设置管理和成就系统

### 配置文件
- **libs.versions.toml**: 集中管理所有依赖版本
- **build.gradle.kts**: 模块构建配置，已配置Hilt和Room

## 🚀 开发环境安装指南

### 步骤1: 安装Java Development Kit
```bash
# 更新包管理器
sudo apt update

# 安装OpenJDK 17
sudo apt install openjdk-17-jdk

# 验证安装
java -version
javac -version
```

### 步骤2: 设置环境变量
```bash
# 查找Java安装路径
sudo find /usr -name "java" -type f 2>/dev/null | grep openjdk

# 添加到~/.bashrc或~/.profile
echo 'export JAVA_HOME=/usr/lib/jvm/java-17-openjdk-amd64' >> ~/.bashrc
echo 'export PATH=$PATH:$JAVA_HOME/bin' >> ~/.bashrc

# 重新加载环境变量
source ~/.bashrc

# 验证环境变量
echo $JAVA_HOME
```

### 步骤3: 验证构建环境
```bash
# 进入项目目录
cd /home/<USER>/桌面/WorkSpace/lovers

# 测试Gradle构建
./gradlew build --no-daemon

# 如果成功，尝试debug构建
./gradlew assembleDebug
```

### 必需环境
```bash
# Java Development Kit (建议使用JDK 17)
sudo apt update
sudo apt install openjdk-17-jdk

# Android SDK (通过Android Studio)
# 下载并安装Android Studio
# 配置Android SDK路径

# 环境变量设置
export JAVA_HOME=/usr/lib/jvm/java-17-openjdk-amd64
export ANDROID_HOME=$HOME/Android/Sdk
export PATH=$PATH:$ANDROID_HOME/platform-tools:$ANDROID_HOME/tools
```

### 可选工具
```bash
# Git (版本控制)
sudo apt install git

# ADB工具 (设备调试)
sudo apt install android-tools-adb android-tools-fastboot
```

## ⚠️ 当前状态

### ✅ 已完成
- MVVM架构设计和实现 
- Room数据库完整实现（6个实体，6个DAO，Repository模式）
- 手动依赖注入系统（AppContainer替代Hilt）
- ViewModel与Repository集成
- Gradle构建配置和环境设置
- 项目成功构建和测试通过
- Java 17开发环境配置

### 🔄 当前架构状态
由于Hilt 2.48-2.51版本与Android Gradle Plugin 8.7+存在JavaPoet兼容性问题，项目暂时采用手动依赖注入：
- 使用`AppContainer`类提供Repository和Database实例
- 创建`ViewModelFactory`手动创建ViewModel实例
- 移除所有`@HiltAndroidApp`、`@AndroidEntryPoint`、`@HiltViewModel`注解
- 保持MVVM架构完整性，只是依赖注入方式改变

### 📋 构建验证结果
- ✅ Debug构建：成功生成APK (11.8MB)
- ✅ Release构建：编译通过
- ✅ 单元测试：全部通过
- ⚠️ 警告：存在已弃用API的警告（不影响功能）

### 🛠️ 技术实现亮点
- **完整的Room数据库**: 6个实体类，响应式Flow查询，类型转换器
- **MVVM架构**: ViewModel-Repository-DAO分层架构
- **主题系统**: 4种情侣主题，支持深色模式
- **Jetpack Compose UI**: 现代化Android UI框架
- **响应式编程**: 使用Flow和StateFlow进行数据绑定

### 🔄 需要环境支持  
- ✅ Java 17环境安装完成
- ✅ JAVA_HOME环境变量设置完成
- 🔄 Android SDK配置(可选，已自动下载API 34)
- 🔄 IDE配置(Android Studio推荐)