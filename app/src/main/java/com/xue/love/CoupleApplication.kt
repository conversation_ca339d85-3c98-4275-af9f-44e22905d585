package com.xue.love

import android.app.Application
import com.xue.love.di.AppContainer
// 暂时禁用Hilt
// import dagger.hilt.android.HiltAndroidApp

/**
 * 情侣APP的Application类
 */
// 暂时禁用Hilt
// @HiltAndroidApp  
class CoupleApplication : Application() {
    
    // 应用级依赖容器
    lateinit var container: AppContainer
    
    override fun onCreate() {
        super.onCreate()
        
        // 初始化依赖容器
        container = AppContainer(this)
        
        // 初始化应用配置
        initializeApp()
    }
    
    private fun initializeApp() {
        // 这里可以添加应用初始化逻辑
        // 例如：崩溃报告、分析工具等
    }
}