package com.xue.love

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
// 暂时禁用Hilt
// import dagger.hilt.android.AndroidEntryPoint
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.EaseInOut
import androidx.compose.animation.core.EaseOut
import androidx.compose.animation.core.FastOutSlowInEasing
import androidx.compose.animation.core.Animatable
import androidx.compose.animation.core.tween
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideInVertically
import androidx.compose.animation.slideOutVertically
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.xue.love.ui.components.CoupleBottomNavigation
import com.xue.love.ui.components.CoupleFloatingActionButton
import com.xue.love.ui.navigation.CoupleNavigation
import com.xue.love.ui.theme.*
import kotlinx.coroutines.delay

// 暂时禁用Hilt
// @AndroidEntryPoint
class MainActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        setContent {
            CoupleAppWithTheme {
                MainActivityContent()
            }
        }
    }
}

@Composable
fun CoupleAppWithTheme(
    content: @Composable () -> Unit
) {
    val themeManager = rememberThemeManager()
    val currentTheme by themeManager.currentTheme.collectAsState()
    val isDarkMode by themeManager.isDarkMode.collectAsState()
    
    CoupleAppThemeWithSelection(
        themeType = currentTheme,
        darkTheme = isDarkMode
    ) {
        content()
    }
}

@Composable
fun MainActivityContent() {
    var isLoading by remember { mutableStateOf(true) }
    
    LaunchedEffect(Unit) {
        // 模拟加载过程
        delay(1500)
        isLoading = false
    }
    
    if (isLoading) {
        MainLoadingScreen()
    } else {
        CoupleAppContent()
    }
}

@Composable
fun CoupleAppContent() {
    var selectedTab by remember { mutableIntStateOf(0) }
    // 在聊天界面自动隐藏导航栏
    val isBottomBarVisible = selectedTab != 1
    
    Scaffold(
        modifier = Modifier.fillMaxSize(),
        bottomBar = {
            AnimatedVisibility(
                visible = isBottomBarVisible,
                enter = slideInVertically(
                    initialOffsetY = { it },
                    animationSpec = tween(
                        durationMillis = 400,
                        easing = FastOutSlowInEasing
                    )
                ) + fadeIn(
                    animationSpec = tween(
                        durationMillis = 300,
                        easing = EaseOut
                    )
                ),
                exit = slideOutVertically(
                    targetOffsetY = { it },
                    animationSpec = tween(
                        durationMillis = 350,
                        easing = EaseInOut
                    )
                ) + fadeOut(
                    animationSpec = tween(
                        durationMillis = 250,
                        easing = EaseInOut
                    )
                )
            ) {
                CoupleBottomNavigation(
                    selectedTab = selectedTab,
                    onTabSelected = { selectedTab = it }
                )
            }
        },
        floatingActionButton = {
            // 只在主页显示浮动操作按钮
            if (selectedTab == 0) {
                CoupleFloatingActionButton(
                    onClick = {
                        // TODO: 实现发送爱心震动功能
                    },
                    icon = ImageVector.vectorResource(R.drawable.ic_heart_filled),
                    contentDescription = "发送爱心震动"
                )
            }
        }
    ) { innerPadding ->
        Box(modifier = Modifier.fillMaxSize()) {
            CoupleNavigation(
                selectedTab = selectedTab,
                paddingValues = innerPadding,
                onTabSelected = { selectedTab = it }
            )
        }
    }
}

/**
 * 主Activity加载画面
 */
@Composable
private fun MainLoadingScreen() {
    val alpha = remember { Animatable(0f) }
    val scale = remember { Animatable(0.8f) }
    
    LaunchedEffect(Unit) {
        alpha.animateTo(1f, tween(500))
        scale.animateTo(1f, tween(500))
    }
    
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(
                Brush.radialGradient(
                    colors = listOf(
                        MaterialTheme.colorScheme.primary.copy(alpha = 0.1f),
                        Color.Transparent
                    )
                )
            )
            .background(MaterialTheme.colorScheme.background),
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier
                .alpha(alpha.value)
                .scale(scale.value)
        ) {
            // 加载图标
            Icon(
                imageVector = ImageVector.vectorResource(R.drawable.ic_couple_love),
                contentDescription = "加载中",
                tint = MaterialTheme.colorScheme.primary,
                modifier = Modifier.size(64.dp)
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            Text(
                text = "正在进入你们的世界...",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onBackground.copy(alpha = 0.7f)
            )
        }
    }
}

@Preview(showBackground = true)
@Composable
fun CoupleAppContentPreview() {
    CoupleAppThemeWithSelection {
        CoupleAppContent()
    }
}