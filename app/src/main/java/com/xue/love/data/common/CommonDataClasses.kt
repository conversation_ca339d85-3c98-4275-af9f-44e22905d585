package com.xue.love.data.common

/**
 * 通用数据类定义
 * 避免在多个DAO中重复定义相同的数据类
 */

// 基础统计数据类
data class CategoryCount(
    val category: String,
    val count: Int
)

data class MoodCount(
    val mood: String,
    val count: Int
)

data class SymptomCount(
    val symptom: String,
    val count: Int
)

data class LocationCount(
    val location: String,
    val count: Int
)

data class ClimaxCount(
    val climaxType: String,
    val count: Int
)

data class MonthStatistics(
    val month: String,
    val count: Int,
    val averageSatisfaction: Float
)

data class GameStatistics(
    val gameType: String,
    val playCount: Int,
    val winCount: Int,
    val averageScore: Float
)

data class ChallengeStatistics(
    val totalChallenges: Int,
    val completedChallenges: Int,
    val completionRate: Float
)

// 预测和分析相关数据类
data class OvulationPrediction(
    val predictedDate: Long,
    val confidence: Float,
    val cycleDay: Int
)

data class PeriodPrediction(
    val predictedStartDate: Long,
    val predictedEndDate: Long,
    val confidence: Float
)