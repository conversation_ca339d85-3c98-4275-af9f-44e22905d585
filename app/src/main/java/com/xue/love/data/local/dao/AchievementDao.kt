package com.xue.love.data.local.dao

import androidx.room.*
import com.xue.love.data.local.entity.AchievementEntity
import kotlinx.coroutines.flow.Flow

@Dao
interface AchievementDao {
    
    @Query("SELECT * FROM achievements ORDER BY createdAt ASC")
    fun getAllAchievementsFlow(): Flow<List<AchievementEntity>>
    
    @Query("SELECT * FROM achievements WHERE category = :category ORDER BY createdAt ASC")
    fun getAchievementsByCategory(category: String): Flow<List<AchievementEntity>>
    
    @Query("SELECT * FROM achievements WHERE isUnlocked = 1 ORDER BY unlockedAt DESC")
    fun getUnlockedAchievements(): Flow<List<AchievementEntity>>
    
    @Query("SELECT * FROM achievements WHERE isUnlocked = 0 ORDER BY currentValue DESC")
    fun getLockedAchievements(): Flow<List<AchievementEntity>>
    
    @Query("SELECT * FROM achievements WHERE id = :achievementId")
    suspend fun getAchievementById(achievementId: String): AchievementEntity?
    
    @Query("SELECT COUNT(*) FROM achievements WHERE isUnlocked = 1")
    fun getUnlockedAchievementCountFlow(): Flow<Int>
    
    @Query("SELECT COUNT(*) FROM achievements")
    fun getTotalAchievementCountFlow(): Flow<Int>
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertAchievement(achievement: AchievementEntity)
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertAchievements(achievements: List<AchievementEntity>)
    
    @Update
    suspend fun updateAchievement(achievement: AchievementEntity)
    
    @Query("UPDATE achievements SET currentValue = :currentValue WHERE id = :achievementId")
    suspend fun updateProgress(achievementId: String, currentValue: Int)
    
    @Query("UPDATE achievements SET isUnlocked = 1, unlockedAt = :unlockedAt WHERE id = :achievementId")
    suspend fun unlockAchievement(achievementId: String, unlockedAt: Long = System.currentTimeMillis())
    
    @Delete
    suspend fun deleteAchievement(achievement: AchievementEntity)
    
    @Query("DELETE FROM achievements WHERE id = :achievementId")
    suspend fun deleteAchievementById(achievementId: String)
    
    @Query("DELETE FROM achievements")
    suspend fun deleteAllAchievements()
}