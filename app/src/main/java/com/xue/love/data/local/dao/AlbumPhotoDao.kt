package com.xue.love.data.local.dao

import androidx.room.*
import com.xue.love.data.local.entity.AlbumPhotoEntity
import kotlinx.coroutines.flow.Flow

@Dao
interface AlbumPhotoDao {
    
    @Query("SELECT * FROM album_photos WHERE coupleId = :coupleId ORDER BY takenAt DESC")
    fun getPhotosFlow(coupleId: String): Flow<List<AlbumPhotoEntity>>
    
    @Query("SELECT * FROM album_photos WHERE coupleId = :coupleId AND isPrivate = 0 ORDER BY takenAt DESC")
    fun getPublicPhotosFlow(coupleId: String): Flow<List<AlbumPhotoEntity>>
    
    @Query("SELECT * FROM album_photos WHERE coupleId = :coupleId AND isPrivate = 1 ORDER BY takenAt DESC")
    fun getPrivatePhotosFlow(coupleId: String): Flow<List<AlbumPhotoEntity>>
    
    @Query("SELECT * FROM album_photos WHERE coupleId = :coupleId AND isFavorite = 1 ORDER BY takenAt DESC")
    fun getFavoritePhotosFlow(coupleId: String): Flow<List<AlbumPhotoEntity>>
    
    @Query("SELECT * FROM album_photos WHERE id = :photoId")
    suspend fun getPhotoById(photoId: String): AlbumPhotoEntity?
    
    @Query("SELECT * FROM album_photos WHERE coupleId = :coupleId ORDER BY takenAt DESC LIMIT :limit OFFSET :offset")
    suspend fun getPhotosPaged(coupleId: String, limit: Int, offset: Int): List<AlbumPhotoEntity>
    
    @Query("SELECT COUNT(*) FROM album_photos WHERE coupleId = :coupleId")
    fun getPhotoCountFlow(coupleId: String): Flow<Int>
    
    @Query("SELECT COUNT(*) FROM album_photos WHERE coupleId = :coupleId AND isPrivate = 0")
    fun getPublicPhotoCountFlow(coupleId: String): Flow<Int>
    
    @Query("SELECT COUNT(*) FROM album_photos WHERE coupleId = :coupleId AND isPrivate = 1")
    fun getPrivatePhotoCountFlow(coupleId: String): Flow<Int>
    
    @Query("SELECT * FROM album_photos WHERE coupleId = :coupleId AND tags LIKE '%' || :tag || '%' ORDER BY takenAt DESC")
    suspend fun getPhotosByTag(coupleId: String, tag: String): List<AlbumPhotoEntity>
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertPhoto(photo: AlbumPhotoEntity)
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertPhotos(photos: List<AlbumPhotoEntity>)
    
    @Update
    suspend fun updatePhoto(photo: AlbumPhotoEntity)
    
    @Query("UPDATE album_photos SET caption = :caption WHERE id = :photoId")
    suspend fun updateCaption(photoId: String, caption: String)
    
    @Query("UPDATE album_photos SET tags = :tags WHERE id = :photoId")
    suspend fun updateTags(photoId: String, tags: String)
    
    @Query("UPDATE album_photos SET isFavorite = :isFavorite WHERE id = :photoId")
    suspend fun updateFavorite(photoId: String, isFavorite: Boolean)
    
    @Query("UPDATE album_photos SET isPrivate = :isPrivate WHERE id = :photoId")
    suspend fun updatePrivacy(photoId: String, isPrivate: Boolean)
    
    @Query("UPDATE album_photos SET remoteUrl = :remoteUrl WHERE id = :photoId")
    suspend fun updateRemoteUrl(photoId: String, remoteUrl: String)
    
    @Delete
    suspend fun deletePhoto(photo: AlbumPhotoEntity)
    
    @Query("DELETE FROM album_photos WHERE id = :photoId")
    suspend fun deletePhotoById(photoId: String)
    
    @Query("DELETE FROM album_photos WHERE coupleId = :coupleId")
    suspend fun deletePhotosByCoupleId(coupleId: String)
    
    @Query("DELETE FROM album_photos")
    suspend fun deleteAllPhotos()
}