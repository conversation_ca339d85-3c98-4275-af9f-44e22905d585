package com.xue.love.data.local.dao

import androidx.room.*
import com.xue.love.data.local.entity.ChatMessageEntity
import kotlinx.coroutines.flow.Flow

@Dao
interface ChatMessageDao {
    
    @Query("SELECT * FROM chat_messages ORDER BY timestamp DESC")
    fun getAllMessagesFlow(): Flow<List<ChatMessageEntity>>
    
    @Query("SELECT * FROM chat_messages WHERE (senderId = :userId1 AND receiverId = :userId2) OR (senderId = :userId2 AND receiverId = :userId1) ORDER BY timestamp ASC")
    fun getConversationFlow(userId1: String, userId2: String): Flow<List<ChatMessageEntity>>
    
    @Query("SELECT * FROM chat_messages WHERE (senderId = :userId1 AND receiverId = :userId2) OR (senderId = :userId2 AND receiverId = :userId1) ORDER BY timestamp ASC LIMIT :limit OFFSET :offset")
    suspend fun getConversationPaged(userId1: String, userId2: String, limit: Int, offset: Int): List<ChatMessageEntity>
    
    @Query("SELECT * FROM chat_messages WHERE id = :messageId")
    suspend fun getMessageById(messageId: String): ChatMessageEntity?
    
    @Query("SELECT * FROM chat_messages WHERE receiverId = :userId AND isRead = 0")
    suspend fun getUnreadMessages(userId: String): List<ChatMessageEntity>
    
    @Query("SELECT COUNT(*) FROM chat_messages WHERE receiverId = :userId AND isRead = 0")
    fun getUnreadMessageCountFlow(userId: String): Flow<Int>
    
    @Query("SELECT * FROM chat_messages WHERE messageType = :messageType ORDER BY timestamp DESC")
    suspend fun getMessagesByType(messageType: String): List<ChatMessageEntity>
    
    @Query("SELECT COUNT(*) FROM chat_messages WHERE senderId = :userId AND messageType = :messageType")
    suspend fun getMessageCountByType(userId: String, messageType: String): Int
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertMessage(message: ChatMessageEntity)
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertMessages(messages: List<ChatMessageEntity>)
    
    @Update
    suspend fun updateMessage(message: ChatMessageEntity)
    
    @Query("UPDATE chat_messages SET isRead = 1 WHERE id = :messageId")
    suspend fun markAsRead(messageId: String)
    
    @Query("UPDATE chat_messages SET isRead = 1 WHERE receiverId = :userId")
    suspend fun markAllAsRead(userId: String)
    
    @Delete
    suspend fun deleteMessage(message: ChatMessageEntity)
    
    @Query("DELETE FROM chat_messages WHERE id = :messageId")
    suspend fun deleteMessageById(messageId: String)
    
    @Query("DELETE FROM chat_messages WHERE timestamp < :beforeTimestamp")
    suspend fun deleteOldMessages(beforeTimestamp: Long)
    
    @Query("DELETE FROM chat_messages")
    suspend fun deleteAllMessages()
}