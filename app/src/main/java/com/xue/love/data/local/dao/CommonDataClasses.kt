package com.xue.love.data.local.dao

// 统一的统计数据类，避免重复定义

// 基础计数类
data class CategoryCount(val category: String, val count: Int)
data class MoodCount(val mood: String, val count: Int)
data class LocationCount(val location: String, val count: Int)
data class DifficultyCount(val difficulty: String, val count: Int)
data class TypeCount(val type: String, val count: Int)
data class SymptomCount(val symptom: String, val count: Int)
data class FlowIntensityCount(val flowIntensity: String, val count: Int)

// 月度统计类
data class MonthStatistics(
    val month: String,
    val totalCount: Int,
    val completedCount: Int,
    val avgSatisfaction: Float?,
    val totalPoints: Int
)

data class CycleMonthStatistics(
    val month: String,
    val totalCount: Int,
    val completedCount: Int,
    val avgSatisfaction: Float?,
    val totalPoints: Int
)

data class ChallengeMonthStatistics(
    val month: String,
    val totalCount: Int,
    val completedCount: Int,
    val avgSatisfaction: Float?,
    val totalPoints: Int
)

// 周统计类
data class ChallengeWeekStatistics(
    val week: String,
    val totalCount: Int,
    val completedCount: Int,
    val avgSatisfaction: Float?
)

// 性能分析类
data class CategoryPerformance(
    val category: String,
    val totalCount: Int,
    val completedCount: Int,
    val avgSatisfaction: Float?,
    val avgImpact: Float?
)

// 月度计数类
data class MonthlyCount(
    val month: String,
    val count: Int
)

// 游戏相关数据类
data class GameTypeCount(val gameType: String, val count: Int)
data class GameNameCount(val gameName: String, val count: Int)
data class GameTypePerformance(
    val gameType: String,
    val avgScore: Float,
    val avgCompatibility: Float,
    val avgSatisfaction: Float,
    val gameCount: Int
)
data class GameMonthStatistics(
    val month: String,
    val gameCount: Int,
    val avgScore: Float,
    val avgSatisfaction: Float,
    val totalDuration: Int
)
data class GameTrendData(
    val score: Int,
    val compatibilityScore: Float,
    val overallSatisfaction: Int,
    val startTime: Long
)

// 生理周期相关数据类
data class FlowLevelCount(val flowLevel: String, val count: Int)
data class ContraceptionCount(val contraception: String, val count: Int)
data class MenstrualCycleMonthStatistics(
    val month: String,
    val cycleCount: Int,
    val avgCycleDuration: Float,
    val avgPeriodDuration: Float,
    val avgCramps: Float
)
data class HealthTrendData(
    val cycleDuration: Int,
    val periodDuration: Int,
    val cramps: Int,
    val moodSwings: Int,
    val cycleStartDate: Long
)

// 亲密记录相关数据类
data class ClimaxCount(val climaxAchieved: String, val count: Int)
data class DayStatistics(val date: String, val count: Int, val avgSatisfaction: Float)
data class WeekStatistics(val week: String, val count: Int, val avgSatisfaction: Float)
data class IntimateMonthStatistics(val month: String, val count: Int, val avgSatisfaction: Float?)