package com.xue.love.data.local.dao

import androidx.room.*
import com.xue.love.data.local.entity.CoupleProfileEntity
import kotlinx.coroutines.flow.Flow

@Dao
interface CoupleProfileDao {
    
    @Query("SELECT * FROM couple_profile WHERE userId = :userId LIMIT 1")
    suspend fun getCoupleProfile(userId: String): CoupleProfileEntity?
    
    @Query("SELECT * FROM couple_profile WHERE userId = :userId LIMIT 1")
    fun getCoupleProfileFlow(userId: String): Flow<CoupleProfileEntity?>
    
    @Query("SELECT * FROM couple_profile")
    fun getAllCoupleProfilesFlow(): Flow<List<CoupleProfileEntity>>
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertCoupleProfile(profile: CoupleProfileEntity)
    
    @Update
    suspend fun updateCoupleProfile(profile: CoupleProfileEntity)
    
    @Query("UPDATE couple_profile SET nickname = :nickname, updatedAt = :updatedAt WHERE userId = :userId")
    suspend fun updateNickname(userId: String, nickname: String, updatedAt: Long = System.currentTimeMillis())
    
    @Query("UPDATE couple_profile SET partnerNickname = :partnerNickname, updatedAt = :updatedAt WHERE userId = :userId")
    suspend fun updatePartnerNickname(userId: String, partnerNickname: String, updatedAt: Long = System.currentTimeMillis())
    
    @Query("UPDATE couple_profile SET coupleAvatar = :avatarUrl, updatedAt = :updatedAt WHERE userId = :userId")
    suspend fun updateCoupleAvatar(userId: String, avatarUrl: String, updatedAt: Long = System.currentTimeMillis())
    
    @Query("UPDATE couple_profile SET loveLevel = :loveLevel, updatedAt = :updatedAt WHERE userId = :userId")
    suspend fun updateLoveLevel(userId: String, loveLevel: String, updatedAt: Long = System.currentTimeMillis())
    
    @Query("UPDATE couple_profile SET theme = :theme, updatedAt = :updatedAt WHERE userId = :userId")
    suspend fun updateTheme(userId: String, theme: String, updatedAt: Long = System.currentTimeMillis())
    
    @Query("UPDATE couple_profile SET isDarkMode = :isDarkMode, updatedAt = :updatedAt WHERE userId = :userId")
    suspend fun updateDarkMode(userId: String, isDarkMode: Boolean, updatedAt: Long = System.currentTimeMillis())
    
    @Query("UPDATE couple_profile SET isPrivacyMode = :isPrivacyMode, updatedAt = :updatedAt WHERE userId = :userId")
    suspend fun updatePrivacyMode(userId: String, isPrivacyMode: Boolean, updatedAt: Long = System.currentTimeMillis())
    
    @Query("UPDATE couple_profile SET isNotificationEnabled = :isEnabled, updatedAt = :updatedAt WHERE userId = :userId")
    suspend fun updateNotificationEnabled(userId: String, isEnabled: Boolean, updatedAt: Long = System.currentTimeMillis())
    
    @Query("UPDATE couple_profile SET biometricEnabled = :isEnabled, updatedAt = :updatedAt WHERE userId = :userId")
    suspend fun updateBiometricEnabled(userId: String, isEnabled: Boolean, updatedAt: Long = System.currentTimeMillis())
    
    @Query("UPDATE couple_profile SET lastSyncAt = :timestamp, updatedAt = :updatedAt WHERE userId = :userId")
    suspend fun updateLastSync(userId: String, timestamp: Long, updatedAt: Long = System.currentTimeMillis())
    
    @Delete
    suspend fun deleteCoupleProfile(profile: CoupleProfileEntity)
    
    @Query("DELETE FROM couple_profile WHERE userId = :userId")
    suspend fun deleteCoupleProfileById(userId: String)
    
    @Query("DELETE FROM couple_profile")
    suspend fun deleteAllCoupleProfiles()
}