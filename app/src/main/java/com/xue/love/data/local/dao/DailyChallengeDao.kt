package com.xue.love.data.local.dao

import androidx.room.*
import com.xue.love.data.local.entity.DailyChallengeEntity
import kotlinx.coroutines.flow.Flow

@Dao
interface DailyChallengeDao {
    
    // 基本CRUD操作
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertChallenge(challenge: DailyChallengeEntity)
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertChallenges(challenges: List<DailyChallengeEntity>)
    
    @Update
    suspend fun updateChallenge(challenge: DailyChallengeEntity)
    
    @Delete
    suspend fun deleteChallenge(challenge: DailyChallengeEntity)
    
    @Query("DELETE FROM daily_challenges WHERE id = :challengeId")
    suspend fun deleteChallengeById(challengeId: String)
    
    @Query("DELETE FROM daily_challenges")
    suspend fun deleteAllChallenges()
    
    // 查询操作
    @Query("SELECT * FROM daily_challenges WHERE id = :challengeId")
    suspend fun getChallengeById(challengeId: String): DailyChallengeEntity?
    
    @Query("SELECT * FROM daily_challenges WHERE userId = :userId ORDER BY challengeDate DESC")
    fun getChallengesByUser(userId: String): Flow<List<DailyChallengeEntity>>
    
    @Query("SELECT * FROM daily_challenges ORDER BY challengeDate DESC")
    fun getAllChallenges(): Flow<List<DailyChallengeEntity>>
    
    // 日期相关查询
    @Query("SELECT * FROM daily_challenges WHERE userId = :userId AND challengeDate >= :startOfDay AND challengeDate < :endOfDay")
    suspend fun getTodayChallenges(userId: String, startOfDay: Long, endOfDay: Long): List<DailyChallengeEntity>
    
    @Query("SELECT * FROM daily_challenges WHERE userId = :userId AND challengeDate BETWEEN :startDate AND :endDate ORDER BY challengeDate DESC")
    fun getChallengesByDateRange(userId: String, startDate: Long, endDate: Long): Flow<List<DailyChallengeEntity>>
    
    @Query("SELECT * FROM daily_challenges WHERE userId = :userId AND challengeDate >= :since ORDER BY challengeDate DESC")
    fun getRecentChallenges(userId: String, since: Long): Flow<List<DailyChallengeEntity>>
    
    // 状态查询
    @Query("SELECT * FROM daily_challenges WHERE userId = :userId AND status = :status ORDER BY challengeDate DESC")
    fun getChallengesByStatus(userId: String, status: String): Flow<List<DailyChallengeEntity>>
    
    @Query("SELECT * FROM daily_challenges WHERE userId = :userId AND status = 'pending' ORDER BY challengeDate ASC")
    fun getPendingChallenges(userId: String): Flow<List<DailyChallengeEntity>>
    
    @Query("SELECT * FROM daily_challenges WHERE userId = :userId AND status = 'completed' ORDER BY challengeDate DESC")
    fun getCompletedChallenges(userId: String): Flow<List<DailyChallengeEntity>>
    
    @Query("SELECT * FROM daily_challenges WHERE userId = :userId AND status = 'active' ORDER BY challengeDate DESC")
    fun getActiveChallenges(userId: String): Flow<List<DailyChallengeEntity>>
    
    // 分类查询
    @Query("SELECT * FROM daily_challenges WHERE userId = :userId AND category = :category ORDER BY challengeDate DESC")
    fun getChallengesByCategory(userId: String, category: String): Flow<List<DailyChallengeEntity>>
    
    @Query("SELECT * FROM daily_challenges WHERE userId = :userId AND type = :type ORDER BY challengeDate DESC")
    fun getChallengesByType(userId: String, type: String): Flow<List<DailyChallengeEntity>>
    
    @Query("SELECT * FROM daily_challenges WHERE userId = :userId AND difficulty = :difficulty ORDER BY challengeDate DESC")
    fun getChallengesByDifficulty(userId: String, difficulty: String): Flow<List<DailyChallengeEntity>>
    
    // 统计查询
    @Query("SELECT COUNT(*) FROM daily_challenges WHERE userId = :userId")
    suspend fun getChallengeCount(userId: String): Int
    
    @Query("SELECT COUNT(*) FROM daily_challenges WHERE userId = :userId AND status = 'completed'")
    suspend fun getCompletedChallengeCount(userId: String): Int
    
    @Query("SELECT COUNT(*) FROM daily_challenges WHERE userId = :userId AND status = :status")
    suspend fun getChallengeCountByStatus(userId: String, status: String): Int
    
    @Query("SELECT COUNT(*) FROM daily_challenges WHERE userId = :userId AND category = :category")
    suspend fun getChallengeCountByCategory(userId: String, category: String): Int
    
    @Query("SELECT AVG(satisfactionLevel) FROM daily_challenges WHERE userId = :userId AND status = 'completed'")
    suspend fun getAverageSatisfaction(userId: String): Float?
    
    @Query("SELECT AVG(relationshipImpact) FROM daily_challenges WHERE userId = :userId AND status = 'completed'")
    suspend fun getAverageRelationshipImpact(userId: String): Float?
    
    @Query("SELECT AVG(personalGrowth) FROM daily_challenges WHERE userId = :userId AND status = 'completed'")
    suspend fun getAveragePersonalGrowth(userId: String): Float?
    
    @Query("SELECT SUM(pointsEarned) FROM daily_challenges WHERE userId = :userId AND status = 'completed'")
    suspend fun getTotalPointsEarned(userId: String): Int?
    
    // 连续挑战统计
    @Query("SELECT MAX(streakCount) FROM daily_challenges WHERE userId = :userId")
    suspend fun getMaxStreak(userId: String): Int?
    
    @Query("SELECT streakCount FROM daily_challenges WHERE userId = :userId ORDER BY challengeDate DESC LIMIT 1")
    suspend fun getCurrentStreak(userId: String): Int?
    
    // 成功率计算
    @Query("""
        SELECT 
            CAST(COUNT(CASE WHEN status = 'completed' THEN 1 END) AS FLOAT) / COUNT(*) * 100 as successRate
        FROM daily_challenges 
        WHERE userId = :userId AND status IN ('completed', 'failed', 'skipped')
    """)
    suspend fun getSuccessRate(userId: String): Float?
    
    // 分类统计
    @Query("SELECT category, COUNT(*) as count FROM daily_challenges WHERE userId = :userId GROUP BY category ORDER BY count DESC")
    suspend fun getCategoryStatistics(userId: String): List<CategoryCount>
    
    @Query("SELECT difficulty, COUNT(*) as count FROM daily_challenges WHERE userId = :userId GROUP BY difficulty ORDER BY count DESC")
    suspend fun getDifficultyStatistics(userId: String): List<DifficultyCount>
    
    @Query("SELECT type, COUNT(*) as count FROM daily_challenges WHERE userId = :userId GROUP BY type ORDER BY count DESC")
    suspend fun getTypeStatistics(userId: String): List<TypeCount>
    
    // 最近完成的挑战
    @Query("SELECT * FROM daily_challenges WHERE userId = :userId AND status = 'completed' ORDER BY completionTime DESC LIMIT :limit")
    suspend fun getRecentCompletedChallenges(userId: String, limit: Int): List<DailyChallengeEntity>
    
    @Query("SELECT * FROM daily_challenges WHERE userId = :userId ORDER BY challengeDate DESC LIMIT 1")
    suspend fun getLatestChallenge(userId: String): DailyChallengeEntity?
    
    // 搜索和过滤
    @Query("SELECT * FROM daily_challenges WHERE userId = :userId AND (title LIKE '%' || :searchTerm || '%' OR description LIKE '%' || :searchTerm || '%' OR objective LIKE '%' || :searchTerm || '%') ORDER BY challengeDate DESC")
    fun searchChallenges(userId: String, searchTerm: String): Flow<List<DailyChallengeEntity>>
    
    @Query("SELECT * FROM daily_challenges WHERE userId = :userId AND satisfactionLevel >= :minSatisfaction ORDER BY challengeDate DESC")
    fun getChallengesBySatisfaction(userId: String, minSatisfaction: Int): Flow<List<DailyChallengeEntity>>
    
    @Query("SELECT * FROM daily_challenges WHERE userId = :userId AND relationshipImpact >= :minImpact ORDER BY challengeDate DESC")
    fun getChallengesByImpact(userId: String, minImpact: Int): Flow<List<DailyChallengeEntity>>
    
    // 收藏和自定义
    @Query("SELECT * FROM daily_challenges WHERE userId = :userId AND isFavorite = 1 ORDER BY challengeDate DESC")
    fun getFavoriteChallenges(userId: String): Flow<List<DailyChallengeEntity>>
    
    @Query("SELECT * FROM daily_challenges WHERE userId = :userId AND isCustom = 1 ORDER BY challengeDate DESC")
    fun getCustomChallenges(userId: String): Flow<List<DailyChallengeEntity>>
    
    @Query("SELECT * FROM daily_challenges WHERE userId = :userId AND source = :source ORDER BY challengeDate DESC")
    fun getChallengesBySource(userId: String, source: String): Flow<List<DailyChallengeEntity>>
    
    // 循环挑战
    @Query("SELECT * FROM daily_challenges WHERE userId = :userId AND isRecurring = 1 ORDER BY challengeDate DESC")
    fun getRecurringChallenges(userId: String): Flow<List<DailyChallengeEntity>>
    
    @Query("SELECT * FROM daily_challenges WHERE userId = :userId AND nextScheduledDate IS NOT NULL AND nextScheduledDate <= :currentTime")
    suspend fun getChallengesDueForRecurrence(userId: String, currentTime: Long): List<DailyChallengeEntity>
    
    // 标签管理
    @Query("SELECT DISTINCT tags FROM daily_challenges WHERE userId = :userId AND tags != ''")
    suspend fun getAllTags(userId: String): List<String>
    
    @Query("SELECT * FROM daily_challenges WHERE userId = :userId AND tags LIKE '%' || :tag || '%' ORDER BY challengeDate DESC")
    fun getChallengesByTag(userId: String, tag: String): Flow<List<DailyChallengeEntity>>
    
    // 亲密度相关
    @Query("SELECT * FROM daily_challenges WHERE userId = :userId AND category = 'intimacy' ORDER BY challengeDate DESC")
    fun getIntimacyChallenges(userId: String): Flow<List<DailyChallengeEntity>>
    
    @Query("SELECT * FROM daily_challenges WHERE userId = :userId AND intimacyLevel >= :minLevel ORDER BY challengeDate DESC")
    fun getChallengesByIntimacyLevel(userId: String, minLevel: Int): Flow<List<DailyChallengeEntity>>
    
    @Query("SELECT AVG(intimacyLevel) FROM daily_challenges WHERE userId = :userId AND status = 'completed' AND category = 'intimacy'")
    suspend fun getAverageIntimacyLevel(userId: String): Float?
    
    // 时间和持续时间
    @Query("SELECT AVG(actualDuration) FROM daily_challenges WHERE userId = :userId AND status = 'completed' AND actualDuration > 0")
    suspend fun getAverageDuration(userId: String): Float?
    
    @Query("SELECT SUM(actualDuration) FROM daily_challenges WHERE userId = :userId AND status = 'completed'")
    suspend fun getTotalTimeSpent(userId: String): Int?
    
    // 月度和周度统计  
    @Query("""
        SELECT 
            strftime('%Y-%m', challengeDate/1000, 'unixepoch') as month,
            COUNT(*) as totalCount,
            SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completedCount,
            AVG(CASE WHEN status = 'completed' THEN satisfactionLevel END) as avgSatisfaction,
            SUM(CASE WHEN status = 'completed' THEN pointsEarned ELSE 0 END) as totalPoints
        FROM daily_challenges 
        WHERE userId = :userId 
        GROUP BY strftime('%Y-%m', challengeDate/1000, 'unixepoch')
        ORDER BY month DESC
        LIMIT :months
    """)
    suspend fun getMonthlyStatistics(userId: String, months: Int = 12): List<ChallengeMonthStatistics>
    
    @Query("""
        SELECT 
            strftime('%Y-%W', challengeDate/1000, 'unixepoch') as week,
            COUNT(*) as totalCount,
            SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completedCount,
            AVG(CASE WHEN status = 'completed' THEN satisfactionLevel END) as avgSatisfaction
        FROM daily_challenges 
        WHERE userId = :userId 
        GROUP BY strftime('%Y-%W', challengeDate/1000, 'unixepoch')
        ORDER BY week DESC
        LIMIT :weeks
    """)
    suspend fun getWeeklyStatistics(userId: String, weeks: Int = 12): List<ChallengeWeekStatistics>
    
    // 性能分析
    @Query("""
        SELECT 
            category,
            COUNT(*) as totalCount,
            SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completedCount,
            AVG(CASE WHEN status = 'completed' THEN satisfactionLevel END) as avgSatisfaction,
            AVG(CASE WHEN status = 'completed' THEN relationshipImpact END) as avgImpact
        FROM daily_challenges 
        WHERE userId = :userId 
        GROUP BY category
        ORDER BY completedCount DESC
    """)
    suspend fun getCategoryPerformance(userId: String): List<CategoryPerformance>
}

