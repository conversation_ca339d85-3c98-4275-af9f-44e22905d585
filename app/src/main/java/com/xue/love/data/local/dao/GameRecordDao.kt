package com.xue.love.data.local.dao

import androidx.room.*
import com.xue.love.data.local.entity.GameRecordEntity
import kotlinx.coroutines.flow.Flow

@Dao
interface GameRecordDao {
    
    // 基本CRUD操作
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertGameRecord(record: GameRecordEntity)
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertGameRecords(records: List<GameRecordEntity>)
    
    @Update
    suspend fun updateGameRecord(record: GameRecordEntity)
    
    @Delete
    suspend fun deleteGameRecord(record: GameRecordEntity)
    
    @Query("DELETE FROM game_records WHERE id = :recordId")
    suspend fun deleteGameRecordById(recordId: String)
    
    @Query("DELETE FROM game_records")
    suspend fun deleteAllGameRecords()
    
    // 查询操作
    @Query("SELECT * FROM game_records WHERE id = :recordId")
    suspend fun getGameRecordById(recordId: String): GameRecordEntity?
    
    @Query("SELECT * FROM game_records WHERE userId = :userId ORDER BY startTime DESC")
    fun getGameRecordsByUser(userId: String): Flow<List<GameRecordEntity>>
    
    @Query("SELECT * FROM game_records ORDER BY startTime DESC")
    fun getAllGameRecords(): Flow<List<GameRecordEntity>>
    
    // 游戏类型查询
    @Query("SELECT * FROM game_records WHERE userId = :userId AND gameType = :gameType ORDER BY startTime DESC")
    fun getRecordsByGameType(userId: String, gameType: String): Flow<List<GameRecordEntity>>
    
    @Query("SELECT * FROM game_records WHERE userId = :userId AND gameName = :gameName ORDER BY startTime DESC")
    fun getRecordsByGameName(userId: String, gameName: String): Flow<List<GameRecordEntity>>
    
    // 状态查询
    @Query("SELECT * FROM game_records WHERE userId = :userId AND status = :status ORDER BY startTime DESC")
    fun getRecordsByStatus(userId: String, status: String): Flow<List<GameRecordEntity>>
    
    @Query("SELECT * FROM game_records WHERE userId = :userId AND status = 'completed' ORDER BY startTime DESC")
    fun getCompletedGames(userId: String): Flow<List<GameRecordEntity>>
    
    @Query("SELECT * FROM game_records WHERE userId = :userId AND status = 'active' ORDER BY startTime DESC")
    fun getActiveGames(userId: String): Flow<List<GameRecordEntity>>
    
    // 统计查询
    @Query("SELECT COUNT(*) FROM game_records WHERE userId = :userId")
    suspend fun getGameCount(userId: String): Int
    
    @Query("SELECT COUNT(*) FROM game_records WHERE userId = :userId AND status = 'completed'")
    suspend fun getCompletedGameCount(userId: String): Int
    
    @Query("SELECT COUNT(*) FROM game_records WHERE userId = :userId AND gameType = :gameType")
    suspend fun getGameCountByType(userId: String, gameType: String): Int
    
    @Query("SELECT AVG(score) FROM game_records WHERE userId = :userId AND status = 'completed'")
    suspend fun getAverageScore(userId: String): Float?
    
    @Query("SELECT AVG(compatibilityScore) FROM game_records WHERE userId = :userId AND status = 'completed' AND compatibilityScore > 0")
    suspend fun getAverageCompatibilityScore(userId: String): Float?
    
    @Query("SELECT AVG(overallSatisfaction) FROM game_records WHERE userId = :userId AND status = 'completed'")
    suspend fun getAverageSatisfaction(userId: String): Float?
    
    @Query("SELECT AVG(duration) FROM game_records WHERE userId = :userId AND status = 'completed' AND duration > 0")
    suspend fun getAverageDuration(userId: String): Float?
    
    @Query("SELECT SUM(duration) FROM game_records WHERE userId = :userId AND status = 'completed'")
    suspend fun getTotalGameTime(userId: String): Int?
    
    // 最佳记录
    @Query("SELECT MAX(score) FROM game_records WHERE userId = :userId AND gameType = :gameType")
    suspend fun getBestScore(userId: String, gameType: String): Int?
    
    @Query("SELECT MAX(compatibilityScore) FROM game_records WHERE userId = :userId")
    suspend fun getBestCompatibilityScore(userId: String): Float?
    
    @Query("SELECT * FROM game_records WHERE userId = :userId AND score = (SELECT MAX(score) FROM game_records WHERE userId = :userId AND gameType = :gameType) AND gameType = :gameType LIMIT 1")
    suspend fun getBestGameRecord(userId: String, gameType: String): GameRecordEntity?
    
    // 游戏类型统计
    @Query("SELECT gameType, COUNT(*) as count FROM game_records WHERE userId = :userId GROUP BY gameType ORDER BY count DESC")
    suspend fun getGameTypeStatistics(userId: String): List<GameTypeCount>
    
    @Query("SELECT gameName, COUNT(*) as count FROM game_records WHERE userId = :userId GROUP BY gameName ORDER BY count DESC")
    suspend fun getGameNameStatistics(userId: String): List<GameNameCount>
    
    @Query("SELECT difficulty, COUNT(*) as count FROM game_records WHERE userId = :userId GROUP BY difficulty ORDER BY count DESC")
    suspend fun getDifficultyStatistics(userId: String): List<DifficultyCount>
    
    // 时间范围查询
    @Query("SELECT * FROM game_records WHERE userId = :userId AND startTime BETWEEN :startTime AND :endTime ORDER BY startTime DESC")
    fun getRecordsByTimeRange(userId: String, startTime: Long, endTime: Long): Flow<List<GameRecordEntity>>
    
    @Query("SELECT * FROM game_records WHERE userId = :userId AND startTime >= :since ORDER BY startTime DESC")
    fun getRecentRecords(userId: String, since: Long): Flow<List<GameRecordEntity>>
    
    // 最近游戏
    @Query("SELECT * FROM game_records WHERE userId = :userId ORDER BY startTime DESC LIMIT 1")
    suspend fun getLatestGame(userId: String): GameRecordEntity?
    
    @Query("SELECT * FROM game_records WHERE userId = :userId ORDER BY startTime DESC LIMIT :limit")
    suspend fun getRecentGames(userId: String, limit: Int): List<GameRecordEntity>
    
    @Query("SELECT * FROM game_records WHERE userId = :userId AND status = 'completed' ORDER BY startTime DESC LIMIT :limit")
    suspend fun getRecentCompletedGames(userId: String, limit: Int): List<GameRecordEntity>
    
    // 搜索和过滤
    @Query("SELECT * FROM game_records WHERE userId = :userId AND (gameName LIKE '%' || :searchTerm || '%' OR gameplayFeedback LIKE '%' || :searchTerm || '%' OR privateNotes LIKE '%' || :searchTerm || '%') ORDER BY startTime DESC")
    fun searchGameRecords(userId: String, searchTerm: String): Flow<List<GameRecordEntity>>
    
    @Query("SELECT * FROM game_records WHERE userId = :userId AND overallSatisfaction >= :minSatisfaction ORDER BY startTime DESC")
    fun getRecordsBySatisfaction(userId: String, minSatisfaction: Int): Flow<List<GameRecordEntity>>
    
    @Query("SELECT * FROM game_records WHERE userId = :userId AND score >= :minScore ORDER BY startTime DESC")
    fun getRecordsByScore(userId: String, minScore: Int): Flow<List<GameRecordEntity>>
    
    // 个人最佳记录
    @Query("SELECT * FROM game_records WHERE userId = :userId AND isPersonalBest = 1 ORDER BY startTime DESC")
    fun getPersonalBests(userId: String): Flow<List<GameRecordEntity>>
    
    @Query("SELECT * FROM game_records WHERE userId = :userId AND wouldPlayAgain = 1 ORDER BY startTime DESC")
    fun getFavoriteGames(userId: String): Flow<List<GameRecordEntity>>
    
    // 标签和分类
    @Query("SELECT DISTINCT tags FROM game_records WHERE userId = :userId AND tags != ''")
    suspend fun getAllTags(userId: String): List<String>
    
    @Query("SELECT * FROM game_records WHERE userId = :userId AND tags LIKE '%' || :tag || '%' ORDER BY startTime DESC")
    fun getRecordsByTag(userId: String, tag: String): Flow<List<GameRecordEntity>>
    
    // 进步分析
    @Query("""
        SELECT 
            gameType,
            AVG(score) as avgScore,
            AVG(compatibilityScore) as avgCompatibility,
            AVG(overallSatisfaction) as avgSatisfaction,
            COUNT(*) as gameCount
        FROM game_records 
        WHERE userId = :userId AND status = 'completed'
        GROUP BY gameType
        ORDER BY gameCount DESC
    """)
    suspend fun getGameTypePerformance(userId: String): List<GameTypePerformance>
    
    // 月度统计
    @Query("""
        SELECT 
            strftime('%Y-%m', startTime/1000, 'unixepoch') as month,
            COUNT(*) as gameCount,
            AVG(score) as avgScore,
            AVG(overallSatisfaction) as avgSatisfaction,
            SUM(duration) as totalDuration
        FROM game_records 
        WHERE userId = :userId AND status = 'completed'
        GROUP BY strftime('%Y-%m', startTime/1000, 'unixepoch')
        ORDER BY month DESC
        LIMIT :months
    """)
    suspend fun getMonthlyStatistics(userId: String, months: Int = 12): List<GameMonthStatistics>
    
    // 趋势分析
    @Query("SELECT score, compatibilityScore, overallSatisfaction, startTime FROM game_records WHERE userId = :userId AND gameType = :gameType AND status = 'completed' ORDER BY startTime DESC LIMIT :limit")
    suspend fun getGameTrendData(userId: String, gameType: String, limit: Int = 10): List<GameTrendData>
}

