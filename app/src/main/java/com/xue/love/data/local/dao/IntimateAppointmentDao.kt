package com.xue.love.data.local.dao

import androidx.room.*
import com.xue.love.data.local.entity.IntimateAppointmentEntity
import kotlinx.coroutines.flow.Flow

@Dao
interface IntimateAppointmentDao {
    
    // 基本CRUD操作
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertAppointment(appointment: IntimateAppointmentEntity)
    
    @Update
    suspend fun updateAppointment(appointment: IntimateAppointmentEntity)
    
    @Delete
    suspend fun deleteAppointment(appointment: IntimateAppointmentEntity)
    
    @Query("DELETE FROM intimate_appointments WHERE id = :appointmentId")
    suspend fun deleteAppointmentById(appointmentId: String)
    
    // 查询操作
    @Query("SELECT * FROM intimate_appointments WHERE id = :appointmentId AND isDeleted = 0")
    suspend fun getAppointmentById(appointmentId: String): IntimateAppointmentEntity?
    
    @Query("SELECT * FROM intimate_appointments WHERE userId = :userId OR partnerId = :userId AND isDeleted = 0 ORDER BY scheduledDateTime DESC")
    fun getAppointmentsByUser(userId: String): Flow<List<IntimateAppointmentEntity>>
    
    @Query("SELECT * FROM intimate_appointments WHERE (userId = :userId AND partnerId = :partnerId) OR (userId = :partnerId AND partnerId = :userId) AND isDeleted = 0 ORDER BY scheduledDateTime DESC")
    fun getAppointmentsByCouple(userId: String, partnerId: String): Flow<List<IntimateAppointmentEntity>>
    
    @Query("SELECT * FROM intimate_appointments WHERE isDeleted = 0 ORDER BY scheduledDateTime DESC")
    fun getAllAppointments(): Flow<List<IntimateAppointmentEntity>>
    
    // 状态查询
    @Query("SELECT * FROM intimate_appointments WHERE (userId = :userId OR partnerId = :userId) AND status = :status AND isDeleted = 0 ORDER BY scheduledDateTime DESC")
    fun getAppointmentsByStatus(userId: String, status: String): Flow<List<IntimateAppointmentEntity>>
    
    @Query("SELECT * FROM intimate_appointments WHERE (userId = :userId OR partnerId = :userId) AND status = 'pending' AND isDeleted = 0 ORDER BY scheduledDateTime ASC")
    fun getPendingAppointments(userId: String): Flow<List<IntimateAppointmentEntity>>
    
    @Query("SELECT * FROM intimate_appointments WHERE (userId = :userId OR partnerId = :userId) AND status = 'accepted' AND isDeleted = 0 ORDER BY scheduledDateTime ASC")
    fun getAcceptedAppointments(userId: String): Flow<List<IntimateAppointmentEntity>>
    
    @Query("SELECT * FROM intimate_appointments WHERE (userId = :userId OR partnerId = :userId) AND status = 'completed' AND isDeleted = 0 ORDER BY scheduledDateTime DESC")
    fun getCompletedAppointments(userId: String): Flow<List<IntimateAppointmentEntity>>
    
    // 时间范围查询
    @Query("SELECT * FROM intimate_appointments WHERE (userId = :userId OR partnerId = :userId) AND scheduledDateTime BETWEEN :startTime AND :endTime AND isDeleted = 0 ORDER BY scheduledDateTime ASC")
    fun getAppointmentsByTimeRange(userId: String, startTime: Long, endTime: Long): Flow<List<IntimateAppointmentEntity>>
    
    @Query("SELECT * FROM intimate_appointments WHERE (userId = :userId OR partnerId = :userId) AND scheduledDateTime BETWEEN :currentTime AND :futureTime AND status IN ('pending', 'accepted') AND isDeleted = 0 ORDER BY scheduledDateTime ASC")
    fun getUpcomingAppointments(userId: String, currentTime: Long, futureTime: Long): Flow<List<IntimateAppointmentEntity>>
    
    @Query("SELECT * FROM intimate_appointments WHERE (userId = :userId OR partnerId = :userId) AND scheduledDateTime BETWEEN :dayStart AND :dayEnd AND isDeleted = 0 ORDER BY scheduledDateTime ASC")
    fun getTodayAppointments(userId: String, dayStart: Long, dayEnd: Long): Flow<List<IntimateAppointmentEntity>>
    
    // 分页查询
    @Query("SELECT * FROM intimate_appointments WHERE (userId = :userId OR partnerId = :userId) AND isDeleted = 0 ORDER BY scheduledDateTime DESC LIMIT :limit OFFSET :offset")
    suspend fun getAppointmentsPage(userId: String, limit: Int, offset: Int): List<IntimateAppointmentEntity>
    
    // 统计查询
    @Query("SELECT COUNT(*) FROM intimate_appointments WHERE (userId = :userId OR partnerId = :userId) AND isDeleted = 0")
    suspend fun getAppointmentCount(userId: String): Int
    
    @Query("SELECT COUNT(*) FROM intimate_appointments WHERE (userId = :userId OR partnerId = :userId) AND status = :status AND isDeleted = 0")
    suspend fun getAppointmentCountByStatus(userId: String, status: String): Int
    
    // 最近记录
    @Query("SELECT * FROM intimate_appointments WHERE (userId = :userId OR partnerId = :userId) AND isDeleted = 0 ORDER BY scheduledDateTime DESC LIMIT 1")
    suspend fun getLatestAppointment(userId: String): IntimateAppointmentEntity?
    
    @Query("SELECT * FROM intimate_appointments WHERE (userId = :userId OR partnerId = :userId) AND isDeleted = 0 ORDER BY scheduledDateTime DESC LIMIT :limit")
    suspend fun getRecentAppointments(userId: String, limit: Int): List<IntimateAppointmentEntity>
    
    // 搜索和过滤
    @Query("SELECT * FROM intimate_appointments WHERE (userId = :userId OR partnerId = :userId) AND (title LIKE '%' || :searchTerm || '%' OR description LIKE '%' || :searchTerm || '%' OR location LIKE '%' || :searchTerm || '%') AND isDeleted = 0 ORDER BY scheduledDateTime DESC")
    fun searchAppointments(userId: String, searchTerm: String): Flow<List<IntimateAppointmentEntity>>
    
    @Query("SELECT * FROM intimate_appointments WHERE (userId = :userId OR partnerId = :userId) AND category = :category AND isDeleted = 0 ORDER BY scheduledDateTime DESC")
    fun getAppointmentsByCategory(userId: String, category: String): Flow<List<IntimateAppointmentEntity>>
    
    @Query("SELECT * FROM intimate_appointments WHERE (userId = :userId OR partnerId = :userId) AND mood = :mood AND isDeleted = 0 ORDER BY scheduledDateTime DESC")
    fun getAppointmentsByMood(userId: String, mood: String): Flow<List<IntimateAppointmentEntity>>
    
    @Query("SELECT * FROM intimate_appointments WHERE (userId = :userId OR partnerId = :userId) AND location LIKE '%' || :location || '%' AND isDeleted = 0 ORDER BY scheduledDateTime DESC")
    fun getAppointmentsByLocation(userId: String, location: String): Flow<List<IntimateAppointmentEntity>>
    
    // 统计分析 - 简化版本，返回基本统计信息
    @Query("SELECT category, COUNT(*) as count FROM intimate_appointments WHERE (userId = :userId OR partnerId = :userId) AND isDeleted = 0 GROUP BY category ORDER BY count DESC")
    suspend fun getCategoryStatistics(userId: String): List<CategoryCount>
    
    @Query("SELECT mood, COUNT(*) as count FROM intimate_appointments WHERE (userId = :userId OR partnerId = :userId) AND isDeleted = 0 GROUP BY mood ORDER BY count DESC")
    suspend fun getMoodStatistics(userId: String): List<MoodCount>
    
    @Query("SELECT location, COUNT(*) as count FROM intimate_appointments WHERE (userId = :userId OR partnerId = :userId) AND location != '' AND isDeleted = 0 GROUP BY location ORDER BY count DESC")
    suspend fun getLocationStatistics(userId: String): List<LocationCount>
    
    // 月度统计
    @Query("SELECT strftime('%Y-%m', datetime(scheduledDateTime/1000, 'unixepoch')) as month, COUNT(*) as count FROM intimate_appointments WHERE (userId = :userId OR partnerId = :userId) AND scheduledDateTime >= :sinceTime AND isDeleted = 0 GROUP BY month ORDER BY month DESC")
    suspend fun getMonthlyStatistics(userId: String, sinceTime: Long): List<MonthlyCount>
    
    // 提醒查询
    @Query("SELECT * FROM intimate_appointments WHERE (userId = :userId OR partnerId = :userId) AND scheduledDateTime BETWEEN :currentTime AND :reminderTime AND reminderSent = 0 AND status IN ('pending', 'accepted') AND isDeleted = 0")
    suspend fun getAppointmentsDueForReminder(userId: String, currentTime: Long, reminderTime: Long): List<IntimateAppointmentEntity>
}

