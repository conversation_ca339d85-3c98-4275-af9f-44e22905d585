package com.xue.love.data.local.dao

import androidx.room.*
import com.xue.love.data.local.entity.IntimateRecordEntity
import kotlinx.coroutines.flow.Flow

@Dao
interface IntimateRecordDao {
    
    // 基本CRUD操作
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertRecord(record: IntimateRecordEntity)
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertRecords(records: List<IntimateRecordEntity>)
    
    @Update
    suspend fun updateRecord(record: IntimateRecordEntity)
    
    @Delete
    suspend fun deleteRecord(record: IntimateRecordEntity)
    
    @Query("DELETE FROM intimate_records WHERE id = :recordId")
    suspend fun deleteRecordById(recordId: String)
    
    @Query("DELETE FROM intimate_records")
    suspend fun deleteAllRecords()
    
    // 查询操作
    @Query("SELECT * FROM intimate_records WHERE id = :recordId")
    suspend fun getRecordById(recordId: String): IntimateRecordEntity?
    
    @Query("SELECT * FROM intimate_records WHERE userId = :userId ORDER BY timestamp DESC")
    fun getRecordsByUser(userId: String): Flow<List<IntimateRecordEntity>>
    
    @Query("SELECT * FROM intimate_records WHERE userId = :userId AND partnerId = :partnerId ORDER BY timestamp DESC")
    fun getRecordsByCouple(userId: String, partnerId: String): Flow<List<IntimateRecordEntity>>
    
    @Query("SELECT * FROM intimate_records ORDER BY timestamp DESC")
    fun getAllRecords(): Flow<List<IntimateRecordEntity>>
    
    // 分页查询
    @Query("SELECT * FROM intimate_records WHERE userId = :userId ORDER BY timestamp DESC LIMIT :limit OFFSET :offset")
    suspend fun getRecordsPage(userId: String, limit: Int, offset: Int): List<IntimateRecordEntity>
    
    // 时间范围查询
    @Query("SELECT * FROM intimate_records WHERE userId = :userId AND timestamp BETWEEN :startTime AND :endTime ORDER BY timestamp DESC")
    fun getRecordsByTimeRange(userId: String, startTime: Long, endTime: Long): Flow<List<IntimateRecordEntity>>
    
    @Query("SELECT * FROM intimate_records WHERE userId = :userId AND timestamp >= :sinceTime ORDER BY timestamp DESC")
    fun getRecordsSince(userId: String, sinceTime: Long): Flow<List<IntimateRecordEntity>>
    
    // 统计查询
    @Query("SELECT COUNT(*) FROM intimate_records WHERE userId = :userId")
    suspend fun getRecordCount(userId: String): Int
    
    @Query("SELECT COUNT(*) FROM intimate_records WHERE userId = :userId AND timestamp >= :sinceTime")
    suspend fun getRecordCountSince(userId: String, sinceTime: Long): Int
    
    @Query("SELECT AVG(satisfaction) FROM intimate_records WHERE userId = :userId")
    suspend fun getAverageSatisfaction(userId: String): Float?
    
    @Query("SELECT AVG(partnerSatisfaction) FROM intimate_records WHERE userId = :userId")
    suspend fun getAveragePartnerSatisfaction(userId: String): Float?
    
    @Query("SELECT SUM(duration) FROM intimate_records WHERE userId = :userId")
    suspend fun getTotalDuration(userId: String): Int?
    
    @Query("SELECT AVG(duration) FROM intimate_records WHERE userId = :userId")
    suspend fun getAverageDuration(userId: String): Float?
    
    // 高级统计
    @Query("SELECT COUNT(*) FROM intimate_records WHERE userId = :userId AND protectionUsed = :protectionUsed")
    suspend fun getRecordCountByProtection(userId: String, protectionUsed: Boolean): Int
    
    @Query("SELECT mood, COUNT(*) as count FROM intimate_records WHERE userId = :userId GROUP BY mood ORDER BY count DESC")
    suspend fun getMoodStatistics(userId: String): List<MoodCount>
    
    @Query("SELECT location, COUNT(*) as count FROM intimate_records WHERE userId = :userId GROUP BY location ORDER BY count DESC")
    suspend fun getLocationStatistics(userId: String): List<LocationCount>
    
    @Query("SELECT climaxAchieved, COUNT(*) as count FROM intimate_records WHERE userId = :userId GROUP BY climaxAchieved")
    suspend fun getClimaxStatistics(userId: String): List<ClimaxCount>
    
    // 最近记录
    @Query("SELECT * FROM intimate_records WHERE userId = :userId ORDER BY timestamp DESC LIMIT 1")
    suspend fun getLatestRecord(userId: String): IntimateRecordEntity?
    
    @Query("SELECT * FROM intimate_records WHERE userId = :userId ORDER BY timestamp DESC LIMIT :limit")
    suspend fun getRecentRecords(userId: String, limit: Int): List<IntimateRecordEntity>
    
    // 搜索和过滤
    @Query("SELECT * FROM intimate_records WHERE userId = :userId AND (tags LIKE '%' || :searchTerm || '%' OR physicalNotes LIKE '%' || :searchTerm || '%' OR emotionalNotes LIKE '%' || :searchTerm || '%') ORDER BY timestamp DESC")
    fun searchRecords(userId: String, searchTerm: String): Flow<List<IntimateRecordEntity>>
    
    @Query("SELECT * FROM intimate_records WHERE userId = :userId AND mood = :mood ORDER BY timestamp DESC")
    fun getRecordsByMood(userId: String, mood: String): Flow<List<IntimateRecordEntity>>
    
    @Query("SELECT * FROM intimate_records WHERE userId = :userId AND location = :location ORDER BY timestamp DESC")
    fun getRecordsByLocation(userId: String, location: String): Flow<List<IntimateRecordEntity>>
    
    @Query("SELECT * FROM intimate_records WHERE userId = :userId AND satisfaction >= :minSatisfaction ORDER BY timestamp DESC")
    fun getRecordsBySatisfaction(userId: String, minSatisfaction: Int): Flow<List<IntimateRecordEntity>>
    
    // 特殊查询
    @Query("SELECT * FROM intimate_records WHERE userId = :userId AND isSpecialOccasion = 1 ORDER BY timestamp DESC")
    fun getSpecialOccasionRecords(userId: String): Flow<List<IntimateRecordEntity>>
    
    @Query("SELECT * FROM intimate_records WHERE userId = :userId AND privacyLevel = :privacyLevel ORDER BY timestamp DESC")
    fun getRecordsByPrivacyLevel(userId: String, privacyLevel: Int): Flow<List<IntimateRecordEntity>>
    
    @Query("SELECT DISTINCT tags FROM intimate_records WHERE userId = :userId AND tags != ''")
    suspend fun getAllTags(userId: String): List<String>
    
    @Query("SELECT DISTINCT mood FROM intimate_records WHERE userId = :userId AND mood != ''")
    suspend fun getAllMoods(userId: String): List<String>
    
    @Query("SELECT DISTINCT location FROM intimate_records WHERE userId = :userId AND location != ''")
    suspend fun getAllLocations(userId: String): List<String>
    
    // 周期性统计
    @Query("""
        SELECT 
            DATE(timestamp/1000, 'unixepoch', 'start of day') as date,
            COUNT(*) as count,
            AVG(satisfaction) as avgSatisfaction
        FROM intimate_records 
        WHERE userId = :userId 
        GROUP BY DATE(timestamp/1000, 'unixepoch', 'start of day')
        ORDER BY date DESC
        LIMIT :days
    """)
    suspend fun getDailyStatistics(userId: String, days: Int = 30): List<DayStatistics>
    
    @Query("""
        SELECT 
            strftime('%Y-%W', timestamp/1000, 'unixepoch') as week,
            COUNT(*) as count,
            AVG(satisfaction) as avgSatisfaction
        FROM intimate_records 
        WHERE userId = :userId 
        GROUP BY strftime('%Y-%W', timestamp/1000, 'unixepoch')
        ORDER BY week DESC
        LIMIT :weeks
    """)
    suspend fun getWeeklyStatistics(userId: String, weeks: Int = 12): List<WeekStatistics>
    
    @Query("""
        SELECT 
            strftime('%Y-%m', timestamp/1000, 'unixepoch') as month,
            COUNT(*) as count,
            AVG(satisfaction) as avgSatisfaction
        FROM intimate_records 
        WHERE userId = :userId 
        GROUP BY strftime('%Y-%m', timestamp/1000, 'unixepoch')
        ORDER BY month DESC
        LIMIT :months
    """)
    suspend fun getMonthlyStatistics(userId: String, months: Int = 12): List<IntimateMonthStatistics>
}

