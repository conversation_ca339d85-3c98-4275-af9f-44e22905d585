package com.xue.love.data.local.dao

import androidx.room.*
import com.xue.love.data.local.entity.MenstrualCycleEntity
import kotlinx.coroutines.flow.Flow

@Dao
interface MenstrualCycleDao {
    
    // 基本CRUD操作
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertCycle(cycle: MenstrualCycleEntity)
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertCycles(cycles: List<MenstrualCycleEntity>)
    
    @Update
    suspend fun updateCycle(cycle: MenstrualCycleEntity)
    
    @Delete
    suspend fun deleteCycle(cycle: MenstrualCycleEntity)
    
    @Query("DELETE FROM menstrual_cycles WHERE id = :cycleId")
    suspend fun deleteCycleById(cycleId: String)
    
    @Query("DELETE FROM menstrual_cycles")
    suspend fun deleteAllCycles()
    
    // 查询操作
    @Query("SELECT * FROM menstrual_cycles WHERE id = :cycleId")
    suspend fun getCycleById(cycleId: String): MenstrualCycleEntity?
    
    @Query("SELECT * FROM menstrual_cycles WHERE userId = :userId ORDER BY cycleStartDate DESC")
    fun getCyclesByUser(userId: String): Flow<List<MenstrualCycleEntity>>
    
    @Query("SELECT * FROM menstrual_cycles ORDER BY cycleStartDate DESC")
    fun getAllCycles(): Flow<List<MenstrualCycleEntity>>
    
    // 当前和最近周期
    @Query("SELECT * FROM menstrual_cycles WHERE userId = :userId ORDER BY cycleStartDate DESC LIMIT 1")
    suspend fun getCurrentCycle(userId: String): MenstrualCycleEntity?
    
    @Query("SELECT * FROM menstrual_cycles WHERE userId = :userId ORDER BY cycleStartDate DESC LIMIT :limit")
    suspend fun getRecentCycles(userId: String, limit: Int): List<MenstrualCycleEntity>
    
    @Query("SELECT * FROM menstrual_cycles WHERE userId = :userId AND isComplete = 1 ORDER BY cycleStartDate DESC LIMIT :limit")
    suspend fun getCompletedCycles(userId: String, limit: Int): List<MenstrualCycleEntity>
    
    // 日期范围查询
    @Query("SELECT * FROM menstrual_cycles WHERE userId = :userId AND cycleStartDate BETWEEN :startDate AND :endDate ORDER BY cycleStartDate DESC")
    fun getCyclesByDateRange(userId: String, startDate: Long, endDate: Long): Flow<List<MenstrualCycleEntity>>
    
    @Query("SELECT * FROM menstrual_cycles WHERE userId = :userId AND periodStartDate BETWEEN :startDate AND :endDate ORDER BY periodStartDate DESC")
    fun getPeriodsByDateRange(userId: String, startDate: Long, endDate: Long): Flow<List<MenstrualCycleEntity>>
    
    // 统计查询
    @Query("SELECT COUNT(*) FROM menstrual_cycles WHERE userId = :userId")
    suspend fun getCycleCount(userId: String): Int
    
    @Query("SELECT COUNT(*) FROM menstrual_cycles WHERE userId = :userId AND isComplete = 1")
    suspend fun getCompletedCycleCount(userId: String): Int
    
    @Query("SELECT AVG(cycleDuration) FROM menstrual_cycles WHERE userId = :userId AND isComplete = 1")
    suspend fun getAverageCycleDuration(userId: String): Float?
    
    @Query("SELECT AVG(periodDuration) FROM menstrual_cycles WHERE userId = :userId AND periodDuration > 0")
    suspend fun getAveragePeriodDuration(userId: String): Float?
    
    @Query("SELECT AVG(cramps) FROM menstrual_cycles WHERE userId = :userId AND cramps > 0")
    suspend fun getAverageCrampLevel(userId: String): Float?
    
    @Query("SELECT AVG(moodSwings) FROM menstrual_cycles WHERE userId = :userId AND moodSwings > 0")
    suspend fun getAverageMoodSwings(userId: String): Float?
    
    // 预测相关
    @Query("SELECT * FROM menstrual_cycles WHERE userId = :userId AND nextPeriodPredicted IS NOT NULL ORDER BY cycleStartDate DESC LIMIT 1")
    suspend fun getLatestPrediction(userId: String): MenstrualCycleEntity?
    
    @Query("SELECT AVG(cycleDuration) FROM menstrual_cycles WHERE userId = :userId AND isComplete = 1 ORDER BY cycleStartDate DESC LIMIT :recentCount")
    suspend fun getRecentAverageCycleDuration(userId: String, recentCount: Int = 3): Float?
    
    // 症状统计
    @Query("SELECT flowLevel, COUNT(*) as count FROM menstrual_cycles WHERE userId = :userId AND flowLevel != '' GROUP BY flowLevel ORDER BY count DESC")
    suspend fun getFlowLevelStatistics(userId: String): List<FlowLevelCount>
    
    @Query("SELECT mood, COUNT(*) as count FROM menstrual_cycles WHERE userId = :userId AND mood != '' GROUP BY mood ORDER BY count DESC")
    suspend fun getMoodStatistics(userId: String): List<MoodCount>
    
    @Query("SELECT contraception, COUNT(*) as count FROM menstrual_cycles WHERE userId = :userId AND contraception != '' GROUP BY contraception ORDER BY count DESC")
    suspend fun getContraceptionStatistics(userId: String): List<ContraceptionCount>
    
    // 周期阶段查询
    @Query("SELECT * FROM menstrual_cycles WHERE userId = :userId AND cyclePhase = :phase ORDER BY cycleStartDate DESC")
    fun getCyclesByPhase(userId: String, phase: String): Flow<List<MenstrualCycleEntity>>
    
    @Query("SELECT * FROM menstrual_cycles WHERE userId = :userId AND cycleStartDate <= :currentDate AND (cycleEndDate IS NULL OR cycleEndDate >= :currentDate)")
    suspend fun getCurrentActiveCycle(userId: String, currentDate: Long): MenstrualCycleEntity?
    
    // 健康相关查询
    @Query("SELECT * FROM menstrual_cycles WHERE userId = :userId AND irregularities != '' ORDER BY cycleStartDate DESC")
    fun getCyclesWithIrregularities(userId: String): Flow<List<MenstrualCycleEntity>>
    
    @Query("SELECT * FROM menstrual_cycles WHERE userId = :userId AND cramps >= :minLevel ORDER BY cycleStartDate DESC")
    fun getCyclesWithHighCramps(userId: String, minLevel: Int): Flow<List<MenstrualCycleEntity>>
    
    @Query("SELECT * FROM menstrual_cycles WHERE userId = :userId AND doctorVisit IS NOT NULL ORDER BY doctorVisit DESC")
    fun getCyclesWithDoctorVisits(userId: String): Flow<List<MenstrualCycleEntity>>
    
    // 搜索和过滤
    @Query("SELECT * FROM menstrual_cycles WHERE userId = :userId AND (notes LIKE '%' || :searchTerm || '%' OR medicalNotes LIKE '%' || :searchTerm || '%') ORDER BY cycleStartDate DESC")
    fun searchCycles(userId: String, searchTerm: String): Flow<List<MenstrualCycleEntity>>
    
    @Query("SELECT * FROM menstrual_cycles WHERE userId = :userId AND flowLevel = :flowLevel ORDER BY cycleStartDate DESC")
    fun getCyclesByFlowLevel(userId: String, flowLevel: String): Flow<List<MenstrualCycleEntity>>
    
    @Query("SELECT * FROM menstrual_cycles WHERE userId = :userId AND mood = :mood ORDER BY cycleStartDate DESC")
    fun getCyclesByMood(userId: String, mood: String): Flow<List<MenstrualCycleEntity>>
    
    // 排卵相关
    @Query("SELECT * FROM menstrual_cycles WHERE userId = :userId AND ovulationDate IS NOT NULL ORDER BY ovulationDate DESC")
    fun getCyclesWithOvulation(userId: String): Flow<List<MenstrualCycleEntity>>
    
    @Query("SELECT AVG(JULIANDAY(ovulationDate/1000, 'unixepoch') - JULIANDAY(periodStartDate/1000, 'unixepoch')) FROM menstrual_cycles WHERE userId = :userId AND ovulationDate IS NOT NULL")
    suspend fun getAverageOvulationDay(userId: String): Float?
    
    // 性生活相关
    @Query("SELECT * FROM menstrual_cycles WHERE userId = :userId AND sexualActivity = 1 ORDER BY cycleStartDate DESC")
    fun getCyclesWithSexualActivity(userId: String): Flow<List<MenstrualCycleEntity>>
    
    @Query("SELECT AVG(sexualComfort) FROM menstrual_cycles WHERE userId = :userId AND sexualActivity = 1")
    suspend fun getAverageSexualComfort(userId: String): Float?
    
    @Query("SELECT AVG(sexualDesire) FROM menstrual_cycles WHERE userId = :userId")
    suspend fun getAverageSexualDesire(userId: String): Float?
    
    // 伴侣关怀统计
    @Query("SELECT AVG(partnerSupport) FROM menstrual_cycles WHERE userId = :userId AND partnerSupport > 0")
    suspend fun getAveragePartnerSupport(userId: String): Float?
    
    @Query("SELECT AVG(communicationQuality) FROM menstrual_cycles WHERE userId = :userId AND communicationQuality > 0")
    suspend fun getAverageCommunicationQuality(userId: String): Float?
    
    // 特殊周期
    @Query("SELECT * FROM menstrual_cycles WHERE userId = :userId AND isSpecialCycle = 1 ORDER BY cycleStartDate DESC")
    fun getSpecialCycles(userId: String): Flow<List<MenstrualCycleEntity>>
    
    @Query("SELECT DISTINCT tags FROM menstrual_cycles WHERE userId = :userId AND tags != ''")
    suspend fun getAllTags(userId: String): List<String>
    
    // 月度/年度统计
    @Query("""
        SELECT 
            strftime('%Y-%m', cycleStartDate/1000, 'unixepoch') as month,
            COUNT(*) as cycleCount,
            AVG(cycleDuration) as avgCycleDuration,
            AVG(periodDuration) as avgPeriodDuration,
            AVG(cramps) as avgCramps
        FROM menstrual_cycles 
        WHERE userId = :userId 
        GROUP BY strftime('%Y-%m', cycleStartDate/1000, 'unixepoch')
        ORDER BY month DESC
        LIMIT :months
    """)
    suspend fun getMonthlyStatistics(userId: String, months: Int = 12): List<MenstrualCycleMonthStatistics>
    
    // 健康趋势分析
    @Query("""
        SELECT 
            cycleDuration,
            periodDuration,
            cramps,
            moodSwings,
            cycleStartDate
        FROM menstrual_cycles 
        WHERE userId = :userId AND isComplete = 1
        ORDER BY cycleStartDate DESC 
        LIMIT :limit
    """)
    suspend fun getHealthTrendData(userId: String, limit: Int = 6): List<HealthTrendData>
}

