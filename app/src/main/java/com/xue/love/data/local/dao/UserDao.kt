package com.xue.love.data.local.dao

import androidx.room.*
import com.xue.love.data.local.entity.UserEntity
import kotlinx.coroutines.flow.Flow

@Dao
interface UserDao {
    
    @Query("SELECT * FROM users WHERE id = :userId")
    suspend fun getUserById(userId: String): UserEntity?
    
    @Query("SELECT * FROM users WHERE isCurrentUser = 1 LIMIT 1")
    suspend fun getCurrentUser(): UserEntity?
    
    @Query("SELECT * FROM users WHERE isCurrentUser = 1 LIMIT 1")
    fun getCurrentUserFlow(): Flow<UserEntity?>
    
    @Query("SELECT * FROM users WHERE isCurrentUser = 0")
    suspend fun getPartners(): List<UserEntity>
    
    @Query("SELECT * FROM users")
    fun getAllUsersFlow(): Flow<List<UserEntity>>
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertUser(user: UserEntity)
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertUsers(users: List<UserEntity>)
    
    @Update
    suspend fun updateUser(user: UserEntity)
    
    @Query("UPDATE users SET nickname = :nickname, updatedAt = :updatedAt WHERE id = :userId")
    suspend fun updateNickname(userId: String, nickname: String, updatedAt: Long = System.currentTimeMillis())
    
    @Query("UPDATE users SET avatar = :avatarUrl, updatedAt = :updatedAt WHERE id = :userId")
    suspend fun updateAvatar(userId: String, avatarUrl: String, updatedAt: Long = System.currentTimeMillis())
    
    @Delete
    suspend fun deleteUser(user: UserEntity)
    
    @Query("DELETE FROM users WHERE id = :userId")
    suspend fun deleteUserById(userId: String)
    
    @Query("DELETE FROM users")
    suspend fun deleteAllUsers()
}