package com.xue.love.data.local.dao

import androidx.room.*
import com.xue.love.data.local.entity.UserStatsEntity
import kotlinx.coroutines.flow.Flow

@Dao
interface UserStatsDao {
    
    @Query("SELECT * FROM user_stats WHERE userId = :userId")
    suspend fun getUserStats(userId: String): UserStatsEntity?
    
    @Query("SELECT * FROM user_stats WHERE userId = :userId")
    fun getUserStatsFlow(userId: String): Flow<UserStatsEntity?>
    
    @Query("SELECT * FROM user_stats")
    fun getAllUserStatsFlow(): Flow<List<UserStatsEntity>>
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertUserStats(stats: UserStatsEntity)
    
    @Update
    suspend fun updateUserStats(stats: UserStatsEntity)
    
    @Query("UPDATE user_stats SET messageCount = messageCount + 1, updatedAt = :updatedAt WHERE userId = :userId")
    suspend fun incrementMessageCount(userId: String, updatedAt: Long = System.currentTimeMillis())
    
    @Query("UPDATE user_stats SET heartCount = heartCount + 1, updatedAt = :updatedAt WHERE userId = :userId")
    suspend fun incrementHeartCount(userId: String, updatedAt: Long = System.currentTimeMillis())
    
    @Query("UPDATE user_stats SET interactionCount = interactionCount + 1, updatedAt = :updatedAt WHERE userId = :userId")
    suspend fun incrementInteractionCount(userId: String, updatedAt: Long = System.currentTimeMillis())
    
    @Query("UPDATE user_stats SET photoCount = photoCount + 1, updatedAt = :updatedAt WHERE userId = :userId")
    suspend fun incrementPhotoCount(userId: String, updatedAt: Long = System.currentTimeMillis())
    
    @Query("UPDATE user_stats SET vibrationCount = vibrationCount + 1, updatedAt = :updatedAt WHERE userId = :userId")
    suspend fun incrementVibrationCount(userId: String, updatedAt: Long = System.currentTimeMillis())
    
    @Query("UPDATE user_stats SET kissCount = kissCount + 1, updatedAt = :updatedAt WHERE userId = :userId")
    suspend fun incrementKissCount(userId: String, updatedAt: Long = System.currentTimeMillis())
    
    @Query("UPDATE user_stats SET intimacyLevel = :level, updatedAt = :updatedAt WHERE userId = :userId")
    suspend fun updateIntimacyLevel(userId: String, level: Float, updatedAt: Long = System.currentTimeMillis())
    
    @Query("UPDATE user_stats SET daysInRelationship = :days, updatedAt = :updatedAt WHERE userId = :userId")
    suspend fun updateDaysInRelationship(userId: String, days: Int, updatedAt: Long = System.currentTimeMillis())
    
    @Query("UPDATE user_stats SET lastActiveAt = :timestamp, updatedAt = :updatedAt WHERE userId = :userId")
    suspend fun updateLastActive(userId: String, timestamp: Long, updatedAt: Long = System.currentTimeMillis())
    
    @Delete
    suspend fun deleteUserStats(stats: UserStatsEntity)
    
    @Query("DELETE FROM user_stats WHERE userId = :userId")
    suspend fun deleteUserStatsById(userId: String)
    
    @Query("DELETE FROM user_stats")
    suspend fun deleteAllUserStats()
}