package com.xue.love.data.local.database

import androidx.room.TypeConverter

/**
 * Room数据库类型转换器
 * 用于处理复杂数据类型的序列化和反序列化
 * 使用简单的字符串拼接，避免依赖Gson
 */
class Converters {
    
    // List<String> 转换器
    @TypeConverter
    fun fromStringList(value: List<String>?): String? {
        return value?.joinToString("||") // 使用||作为分隔符避免冲突
    }
    
    @TypeConverter
    fun toStringList(value: String?): List<String>? {
        return value?.takeIf { it.isNotEmpty() }?.split("||")?.filter { it.isNotEmpty() }
    }
    
    // Long时间戳列表转换器  
    @TypeConverter
    fun fromLongList(value: List<Long>?): String? {
        return value?.joinToString(",")
    }
    
    @TypeConverter
    fun toLongList(value: String?): List<Long>? {
        return value?.takeIf { it.isNotEmpty() }?.split(",")?.mapNotNull { it.toLongOrNull() }
    }
    
    // Int列表转换器
    @TypeConverter
    fun fromIntList(value: List<Int>?): String? {
        return value?.joinToString(",")
    }
    
    @TypeConverter
    fun toIntList(value: String?): List<Int>? {
        return value?.takeIf { it.isNotEmpty() }?.split(",")?.mapNotNull { it.toIntOrNull() }
    }
}