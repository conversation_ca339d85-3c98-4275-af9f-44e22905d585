package com.xue.love.data.local.database

import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import androidx.room.TypeConverters
import android.content.Context
import com.xue.love.data.local.dao.*
import com.xue.love.data.local.entity.*

@Database(
    entities = [
        UserEntity::class,
        ChatMessageEntity::class,
        UserStatsEntity::class,
        AchievementEntity::class,
        CoupleProfileEntity::class,
        AlbumPhotoEntity::class,
        // 恢复私密功能实体
        IntimateRecordEntity::class,
        MenstrualCycleEntity::class,
        IntimateAppointmentEntity::class,
        GameRecordEntity::class,
        DailyChallengeEntity::class
    ],
    version = 2, // 数据库版本
    exportSchema = false
)
@TypeConverters(Converters::class)
abstract class CoupleDatabase : RoomDatabase() {
    
    abstract fun userDao(): UserDao
    abstract fun chatMessageDao(): ChatMessageDao
    abstract fun userStatsDao(): UserStatsDao
    abstract fun achievementDao(): AchievementDao
    abstract fun coupleProfileDao(): CoupleProfileDao
    abstract fun albumPhotoDao(): AlbumPhotoDao
    
    // 恢复私密功能DAO
    abstract fun intimateRecordDao(): IntimateRecordDao
    abstract fun menstrualCycleDao(): MenstrualCycleDao
    abstract fun intimateAppointmentDao(): IntimateAppointmentDao
    abstract fun gameRecordDao(): GameRecordDao
    abstract fun dailyChallengeDao(): DailyChallengeDao
    
    companion object {
        @Volatile
        private var INSTANCE: CoupleDatabase? = null
        
        const val DATABASE_NAME = "couple_database"
        
        fun getDatabase(context: Context): CoupleDatabase {
            return INSTANCE ?: synchronized(this) {
                val instance = Room.databaseBuilder(
                    context.applicationContext,
                    CoupleDatabase::class.java,
                    DATABASE_NAME
                )
                    .fallbackToDestructiveMigration()
                    .build()
                INSTANCE = instance
                instance
            }
        }
    }
}