package com.xue.love.data.local.entity

import androidx.room.Entity
import androidx.room.PrimaryKey

@Entity(tableName = "achievements")
data class AchievementEntity(
    @PrimaryKey
    val id: String,
    val title: String,
    val description: String,
    val iconRes: String, // 图标资源名称
    val colorCode: String, // 颜色代码
    val category: String, // 成就分类 (intimacy/communication/romance/fun/health/creative/milestone)
    val targetValue: Int = 0, // 目标值
    val currentValue: Int = 0, // 当前进度
    val isUnlocked: Boolean = false,
    val unlockedAt: Long? = null,
    
    // 新增私密功能成就字段
    val achievementType: String = "standard", // 成就类型 (standard/milestone/rare/secret/daily/weekly)
    val rarity: String = "common", // 稀有度 (common/uncommon/rare/epic/legendary)
    val difficulty: String = "medium", // 难度 (easy/medium/hard/expert)
    val points: Int = 10, // 奖励积分
    val badgeIcon: String = "", // 徽章图标
    
    // 解锁条件
    val unlockConditions: String = "", // 解锁条件描述
    val prerequisiteAchievements: String = "", // 前置成就，以逗号分隔
    val minimumLevel: Int = 1, // 最低等级要求
    val timeRequirement: Long? = null, // 时间要求(毫秒)
    val relationshipDaysRequired: Int = 0, // 要求的恋爱天数
    
    // 统计和进度
    val progressType: String = "count", // 进度类型 (count/percentage/level/streak)
    val progressUnit: String = "次", // 进度单位
    val isRepeatable: Boolean = false, // 是否可重复获得
    val maxUnlocks: Int = 1, // 最大解锁次数
    val currentStreak: Int = 0, // 当前连续次数
    val bestStreak: Int = 0, // 历史最佳连续次数
    
    // 奖励和激励
    val rewardDescription: String = "", // 奖励描述
    val specialReward: String = "", // 特殊奖励
    val celebrationMessage: String = "", // 庆祝消息
    val shareableText: String = "", // 可分享文本
    val unlockAnimation: String = "default", // 解锁动画
    
    // 隐私和显示
    val isVisible: Boolean = true, // 是否可见
    val isSecret: Boolean = false, // 是否隐藏成就
    val privacyLevel: Int = 1, // 隐私级别 (1-3)
    val showProgress: Boolean = true, // 是否显示进度
    val showToPartner: Boolean = true, // 是否对伴侣显示
    
    // 时间限制
    val hasTimeLimit: Boolean = false, // 是否有时间限制
    val timeLimit: Long? = null, // 时间限制(毫秒)
    val expiresAt: Long? = null, // 过期时间
    val isTimeSensitive: Boolean = false, // 是否时间敏感
    val seasonalAvailability: String = "", // 季节性可用性
    
    // 社交和分享
    val isShareable: Boolean = true, // 是否可分享
    val socialMediaText: String = "", // 社交媒体文本
    val encouragesPartner: Boolean = false, // 是否鼓励伴侣
    val isCompetitive: Boolean = false, // 是否竞争性质
    val groupAchievement: Boolean = false, // 是否团体成就
    
    // 个性化和定制
    val isCustom: Boolean = false, // 是否自定义成就
    val createdBy: String = "system", // 创建者
    val tags: String = "", // 标签，以逗号分隔
    val personalNote: String = "", // 个人备注
    val isFavorite: Boolean = false, // 是否收藏
    
    // 数据和版本
    val version: String = "1.0", // 版本
    val lastUpdated: Long = System.currentTimeMillis(), // 上次更新
    val syncStatus: String = "local", // 同步状态
    val dataSource: String = "local", // 数据来源
    
    val createdAt: Long = System.currentTimeMillis()
)