package com.xue.love.data.local.entity

import androidx.room.Entity
import androidx.room.PrimaryKey

@Entity(tableName = "album_photos")
data class AlbumPhotoEntity(
    @PrimaryKey
    val id: String,
    val coupleId: String,
    val uploaderId: String,
    val fileName: String,
    val localPath: String,
    val remoteUrl: String? = null,
    val thumbnail: String? = null,
    val caption: String? = null,
    val tags: String? = null, // JSON格式的标签数组
    val location: String? = null,
    val isPrivate: Boolean = false,
    val isFavorite: Boolean = false,
    val width: Int = 0,
    val height: Int = 0,
    val fileSize: Long = 0,
    val mimeType: String = "image/jpeg",
    val takenAt: Long = System.currentTimeMillis(),
    val uploadedAt: Long = System.currentTimeMillis(),
    val createdAt: Long = System.currentTimeMillis()
)