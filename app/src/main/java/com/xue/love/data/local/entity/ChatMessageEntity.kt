package com.xue.love.data.local.entity

import androidx.room.Entity
import androidx.room.PrimaryKey

@Entity(tableName = "chat_messages")
data class ChatMessageEntity(
    @PrimaryKey
    val id: String,
    val senderId: String,
    val receiverId: String,
    val content: String,
    val messageType: String, // TEXT, HEART, VIBRATION, KISS, IMAGE
    val timestamp: Long,
    val isRead: Boolean = false,
    val hasSpecialEffect: Boolean = false,
    val imageUrl: String? = null,
    val localImagePath: String? = null
)