package com.xue.love.data.local.entity

import androidx.room.Entity
import androidx.room.PrimaryKey

@Entity(tableName = "couple_profile")
data class CoupleProfileEntity(
    @PrimaryKey
    val id: String,
    val userId: String,
    val partnerId: String,
    val relationshipStartDate: Long,
    val anniversaryDate: Long,
    val nickname: String,
    val partnerNickname: String,
    val coupleAvatar: String? = null,
    val loveLevel: String = "初恋甜蜜",
    val theme: String = "SWEET_PINK",
    val isDarkMode: Boolean = false,
    val isPrivacyMode: Boolean = false,
    val isNotificationEnabled: Boolean = true,
    val biometricEnabled: Boolean = false,
    val lastSyncAt: Long = System.currentTimeMillis(),
    val createdAt: Long = System.currentTimeMillis(),
    val updatedAt: Long = System.currentTimeMillis()
)