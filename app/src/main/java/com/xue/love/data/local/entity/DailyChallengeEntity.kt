package com.xue.love.data.local.entity

import androidx.room.Entity
import androidx.room.PrimaryKey

@Entity(tableName = "daily_challenges")
data class DailyChallengeEntity(
    @PrimaryKey
    val id: String,
    val userId: String,
    val partnerId: String? = null, // 如果是双人挑战
    val challengeDate: Long = System.currentTimeMillis(),
    
    // 挑战基本信息
    val title: String, // 挑战标题
    val description: String, // 挑战描述
    val category: String, // 分类 (intimacy/communication/romance/fun/health/creative)
    val type: String, // 类型 (individual/couple/competitive/collaborative)
    val difficulty: String = "medium", // 难度 (easy/medium/hard/expert)
    val estimatedTime: Int = 30, // 预计用时(分钟)
    
    // 挑战内容
    val objective: String = "", // 具体目标
    val instructions: String = "", // 详细说明
    val materials: String = "", // 需要的材料/道具
    val location: String = "", // 建议地点
    val timing: String = "", // 最佳时机
    val preparation: String = "", // 准备工作
    
    // 亲密挑战特有
    val intimacyLevel: Int = 1, // 亲密程度 (1-5)
    val physicalContact: String = "", // 身体接触类型
    val emotionalIntensity: Int = 3, // 情感强度 (1-5)
    val communicationRequired: Boolean = true, // 是否需要沟通
    val privacyRequired: Boolean = true, // 是否需要隐私
    val consentRequired: String = "", // 同意书要求
    
    // 浪漫挑战特有
    val romanticElements: String = "", // 浪漫元素
    val surpriseLevel: Int = 3, // 惊喜程度 (1-5)
    val creativityRequired: Int = 3, // 创意要求 (1-5)
    val budgetLevel: String = "low", // 预算要求 (free/low/medium/high)
    val planningTime: Int = 0, // 需要规划时间(小时)
    
    // 沟通挑战特有
    val conversationTopics: String = "", // 对话话题
    val questionPrompts: String = "", // 问题提示
    val listeningSkills: Boolean = false, // 是否锻炼倾听技巧
    val emotionalSharing: Boolean = false, // 是否涉及情感分享
    val conflictResolution: Boolean = false, // 是否解决冲突
    
    // 健康挑战特有
    val physicalActivity: String = "", // 身体活动
    val mentalWellness: String = "", // 心理健康
    val nutritionFocus: String = "", // 营养关注点
    val sleepQuality: Boolean = false, // 是否改善睡眠
    val stressReduction: Boolean = false, // 是否减压
    
    // 挑战状态
    val status: String = "pending", // 状态 (pending/active/completed/skipped/failed)
    val startTime: Long? = null, // 开始时间
    val completionTime: Long? = null, // 完成时间
    val actualDuration: Int = 0, // 实际耗时(分钟)
    val attemptCount: Int = 0, // 尝试次数
    val isReattempt: Boolean = false, // 是否重新尝试
    
    // 执行记录
    val executionNotes: String = "", // 执行笔记
    val modifications: String = "", // 做出的修改
    val obstacles: String = "", // 遇到的障碍
    val solutions: String = "", // 解决方案
    val unexpectedOutcomes: String = "", // 意外结果
    val partnerParticipation: String = "", // 伴侣参与情况
    
    // 结果评估
    val success: Boolean = false, // 是否成功
    val satisfactionLevel: Int = 5, // 满意度 (1-10)
    val difficultyExperienced: Int = 5, // 实际难度 (1-10)
    val enjoymentLevel: Int = 5, // 享受程度 (1-10)
    val relationshipImpact: Int = 5, // 对关系的影响 (1-10)
    val personalGrowth: Int = 5, // 个人成长 (1-10)
    val wouldRecommend: Boolean = true, // 是否推荐给他人
    
    // 学习和洞察
    val lessonsLearned: String = "", // 学到的经验
    val personalInsights: String = "", // 个人洞察
    val relationshipInsights: String = "", // 关系洞察
    val skillsImproved: String = "", // 提升的技能
    val newDiscoveries: String = "", // 新发现
    val areasForImprovement: String = "", // 改进领域
    
    // 情感反馈
    val emotionalResponse: String = "", // 情感回应
    val moodBefore: String = "", // 挑战前心情
    val moodAfter: String = "", // 挑战后心情
    val energyBefore: Int = 5, // 挑战前精力 (1-10)
    val energyAfter: Int = 5, // 挑战后精力 (1-10)
    val confidenceGained: Int = 0, // 信心提升程度 (0-10)
    val bondingEffect: Int = 5, // 增进感情程度 (1-10)
    
    // 伴侣互动
    val partnerFeedback: String = "", // 伴侣反馈
    val partnerSatisfaction: Int = 5, // 伴侣满意度 (1-10)
    val collaborationQuality: Int = 5, // 合作质量 (1-10)
    val communicationQuality: Int = 5, // 沟通质量 (1-10)
    val mutualSupport: Int = 5, // 相互支持程度 (1-10)
    val sharedEnjoyment: Int = 5, // 共同享受程度 (1-10)
    
    // 奖励和激励
    val pointsEarned: Int = 0, // 获得积分
    val badgesUnlocked: String = "", // 解锁徽章
    val streakCount: Int = 0, // 连续完成天数
    val levelProgress: Float = 0f, // 等级进度
    val rewardReceived: String = "", // 获得奖励
    val celebrationPlanned: String = "", // 庆祝方式
    
    // 定制和变化
    val customizations: String = "", // 个人定制
    val variations: String = "", // 变化形式
    val nextLevelChallenge: String = "", // 下一级挑战
    val relatedChallenges: String = "", // 相关挑战
    val followUpActions: String = "", // 后续行动
    val habitFormation: String = "", // 习惯养成
    
    // 分享和社交
    val isShareable: Boolean = false, // 是否可分享
    val shareContent: String = "", // 分享内容
    val privacyLevel: Int = 3, // 隐私级别 (1-3)
    val allowScreenshots: Boolean = false, // 是否允许截图
    val moodBoard: String = "", // 心情板
    val photosAllowed: Boolean = false, // 是否允许拍照
    
    // 循环和重复
    val isRecurring: Boolean = false, // 是否循环挑战
    val recurrencePattern: String = "", // 循环模式
    val seasonality: String = "", // 季节性
    val lastAttemptDate: Long? = null, // 上次尝试日期
    val nextScheduledDate: Long? = null, // 下次计划日期
    val maxAttempts: Int = 3, // 最大尝试次数
    
    // 数据和分析
    val performanceMetrics: String = "", // 性能指标
    val comparisonData: String = "", // 对比数据
    val trendAnalysis: String = "", // 趋势分析
    val improvementSuggestions: String = "", // 改进建议
    val predictiveInsights: String = "", // 预测洞察
    val benchmarkData: String = "", // 基准数据
    
    // 元数据
    val source: String = "system", // 来源 (system/custom/community)
    val tags: String = "", // 标签
    val isCustom: Boolean = false, // 是否自定义
    val isFavorite: Boolean = false, // 是否收藏
    val version: String = "1.0", // 版本
    val isEncrypted: Boolean = false, // 是否加密
    val createdAt: Long = System.currentTimeMillis(),
    val updatedAt: Long = System.currentTimeMillis(),
    val syncStatus: String = "local" // 同步状态
)