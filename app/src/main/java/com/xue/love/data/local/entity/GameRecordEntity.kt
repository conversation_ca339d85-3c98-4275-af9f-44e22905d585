package com.xue.love.data.local.entity

import androidx.room.Entity
import androidx.room.PrimaryKey

@Entity(tableName = "game_records")
data class GameRecordEntity(
    @PrimaryKey
    val id: String,
    val userId: String,
    val partnerId: String,
    val gameType: String, // 游戏类型 (truth_dare/compatibility/exploration/role_play)
    val gameName: String, // 游戏名称
    val gameVersion: String = "1.0", // 游戏版本
    
    // 游戏基本信息
    val startTime: Long = System.currentTimeMillis(),
    val endTime: Long? = null,
    val duration: Int = 0, // 游戏时长(分钟)
    val status: String = "active", // 状态 (active/completed/paused/abandoned)
    val difficulty: String = "medium", // 难度 (easy/medium/hard)
    
    // 游戏结果
    val score: Int = 0, // 总分
    val userScore: Int = 0, // 用户得分
    val partnerScore: Int = 0, // 伴侣得分
    val compatibilityScore: Float = 0f, // 默契度分数
    val correctAnswers: Int = 0, // 正确答案数
    val totalQuestions: Int = 0, // 总题目数
    val accuracy: Float = 0f, // 准确率
    
    // 游戏数据
    val gameData: String = "", // 游戏具体数据(JSON格式)
    val questionsAsked: String = "", // 问过的问题，以逗号分隔
    val answersGiven: String = "", // 给出的答案，以逗号分隔  
    val surprisingAnswers: String = "", // 令人惊讶的答案
    val funnyMoments: String = "", // 有趣瞬间记录
    
    // 真心话大冒险特有
    val truthsChosen: Int = 0, // 选择真心话次数
    val daresChosen: Int = 0, // 选择大冒险次数
    val truthQuestions: String = "", // 真心话问题
    val dareActions: String = "", // 大冒险行动
    val completedDares: String = "", // 完成的大冒险
    val skippedChallenges: String = "", // 跳过的挑战
    
    // 默契测试特有
    val matchingAnswers: Int = 0, // 答案匹配数
    val totalComparisons: Int = 0, // 总对比数
    val strongAgreements: String = "", // 强烈一致的答案
    val differences: String = "", // 分歧点记录
    val discoveredPreferences: String = "", // 新发现的偏好
    val surprisingDifferences: String = "", // 意外的差异
    
    // 身体探索游戏特有
    val explorationAreas: String = "", // 探索区域
    val sensitivityMap: String = "", // 敏感点地图
    val preferences: String = "", // 偏好发现
    val dislikes: String = "", // 不喜欢的记录
    val newTechniques: String = "", // 新技巧学习
    val comfortLevel: Int = 5, // 舒适度 (1-10)
    
    // 角色扮演特有
    val rolePlayType: String = "", // 角色扮演类型
    val charactersPlayed: String = "", // 扮演的角色
    val costumesUsed: String = "", // 使用的服装
    val scenarioDetails: String = "", // 情景详情
    val improvisationLevel: Int = 5, // 即兴发挥程度 (1-10)
    val immersionLevel: Int = 5, // 沉浸程度 (1-10)
    
    // 情感和体验
    val overallSatisfaction: Int = 5, // 整体满意度 (1-10)
    val userSatisfaction: Int = 5, // 用户满意度 (1-10)
    val partnerSatisfaction: Int = 5, // 伴侣满意度 (1-10)
    val intimacyLevel: Int = 5, // 亲密度提升 (1-10)
    val funLevel: Int = 5, // 有趣程度 (1-10)
    val emotionalConnection: Int = 5, // 情感连接度 (1-10)
    
    // 学习和成长
    val newDiscoveries: String = "", // 新发现
    val relationshipInsights: String = "", // 关系洞察
    val personalInsights: String = "", // 个人洞察
    val communicationImprovement: String = "", // 沟通改善
    val bondsStrengthened: String = "", // 增强的纽带
    val areasToExplore: String = "", // 待探索领域
    
    // 游戏设置
    val customRules: String = "", // 自定义规则
    val timerUsed: Boolean = false, // 是否使用计时器
    val privateMode: Boolean = true, // 是否私密模式
    val screenRecording: Boolean = false, // 是否屏幕录制
    val photosAllowed: Boolean = false, // 是否允许拍照
    val soundEffects: Boolean = true, // 是否开启音效
    
    // 环境和氛围
    val playLocation: String = "", // 游戏地点
    val atmosphere: String = "", // 氛围描述
    val musicPlayed: String = "", // 播放的音乐
    val lightingSetup: String = "", // 灯光设置
    val distractions: String = "", // 干扰因素
    val privacyLevel: Int = 3, // 隐私程度 (1-3)
    
    // 挑战和奖励
    val challengesCompleted: String = "", // 完成的挑战
    val rewardsEarned: String = "", // 获得的奖励
    val penaltiesReceived: String = "", // 收到的惩罚
    val bonusPoints: Int = 0, // 奖励分数
    val achievementsUnlocked: String = "", // 解锁的成就
    val nextLevelProgress: Float = 0f, // 下一级进度
    
    // 反馈和建议
    val gameplayFeedback: String = "", // 游戏反馈
    val difficultyFeedback: String = "", // 难度反馈
    val suggestedImprovements: String = "", // 改进建议
    val favoriteAspects: String = "", // 最喜欢的方面
    val leastFavoriteAspects: String = "", // 最不喜欢的方面
    val wouldPlayAgain: Boolean = true, // 是否愿意再玩
    
    // 社交和分享
    val shareableResults: String = "", // 可分享的结果
    val privateNotes: String = "", // 私人笔记
    val partnerNotes: String = "", // 伴侣备注
    val memoryHighlights: String = "", // 回忆亮点
    val photosTaken: Int = 0, // 拍摄照片数
    val videosRecorded: Int = 0, // 录制视频数
    
    // 数据和统计
    val gameCount: Int = 1, // 游戏次数统计
    val isPersonalBest: Boolean = false, // 是否个人最佳
    val improvementFromLast: Float = 0f, // 相比上次的改善
    val consistencyScore: Float = 0f, // 一致性分数
    val trendDirection: String = "", // 趋势方向 (improving/stable/declining)
    
    // 元数据
    val tags: String = "", // 自定义标签
    val isEncrypted: Boolean = true, // 是否加密
    val backupIncluded: Boolean = false, // 是否包含在备份中
    val createdAt: Long = System.currentTimeMillis(),
    val updatedAt: Long = System.currentTimeMillis(),
    val syncStatus: String = "local" // 同步状态
)