package com.xue.love.data.local.entity

import androidx.room.Entity
import androidx.room.PrimaryKey

@Entity(tableName = "intimate_appointments")
data class IntimateAppointmentEntity(
    @PrimaryKey
    val id: String,
    val userId: String, // 发起者
    val partnerId: String, // 接收者
    val createdAt: Long = System.currentTimeMillis(),
    
    // 约会基本信息
    val title: String = "", // 约会标题
    val description: String = "", // 约会描述
    val location: String = "", // 约会地点
    val category: String = "romantic", // 约会类型 (romantic/intimate/adventure/relaxing/special)
    val status: String = "pending", // 状态 (pending/accepted/declined/completed/cancelled)
    
    // 时间安排
    val scheduledDateTime: Long = 0L, // 预约时间戳
    val duration: Int = 120, // 预计持续时间(分钟)
    val actualDateTime: Long? = null, // 实际发生时间
    
    // 心情和偏好
    val mood: String = "excited", // 心情状态
    val notes: String = "", // 备注信息
    val isPrivate: Boolean = false, // 是否私密
    
    // 响应和交互
    val partnerResponse: String = "", // 伴侣回应 (accepted/declined)
    val responseDateTime: Long? = null, // 响应时间
    val cancellationReason: String = "", // 取消原因
    val cancelledAt: Long? = null, // 取消时间
    
    // 完成状态
    val isCompleted: Boolean = false, // 是否完成
    val reminderSent: Boolean = false, // 是否已发送提醒
    
    // 系统字段
    val lastModified: Long = System.currentTimeMillis(),
    val isDeleted: Boolean = false // 是否已删除
)