package com.xue.love.data.local.entity

import androidx.room.Entity
import androidx.room.PrimaryKey

@Entity(tableName = "intimate_records")
data class IntimateRecordEntity(
    @PrimaryKey
    val id: String,
    val userId: String,
    val partnerId: String,
    val timestamp: Long = System.currentTimeMillis(),
    
    // 基本信息
    val location: String = "", // 地点
    val duration: Int = 0, // 持续时间(分钟)
    val initiator: String = "", // 发起方 (me/partner)
    
    // 详细记录
    val protectionUsed: Boolean = false, // 是否使用防护
    val protectionType: String = "", // 防护类型 (condom/pill/other)
    val climaxAchieved: String = "", // 高潮情况 (both/me/partner/none)
    val positions: String = "", // 姿势记录，以逗号分隔
    val foreplayDuration: Int = 0, // 前戏时长(分钟)
    
    // 情况描述
    val mood: String = "", // 心情 (excited/romantic/passionate/tired)
    val satisfaction: Int = 5, // 满意度评分 (1-10)
    val partnerSatisfaction: Int = 5, // 伴侣满意度 (1-10)
    val energy: Int = 5, // 体力状况 (1-10)
    val comfort: Int = 5, // 舒适度 (1-10)
    
    // 服装和氛围
    val clothing: String = "", // 衣着描述
    val environment: String = "", // 环境描述 (candlelit/music/massage_oil)
    val specialElements: String = "", // 特殊元素，以逗号分隔
    
    // 身体反应
    val physicalNotes: String = "", // 身体反应记录
    val emotionalNotes: String = "", // 情感感受记录
    val highlights: String = "", // 美好瞬间记录
    val improvementNotes: String = "", // 改进建议
    
    // 健康相关
    val menstrualPhase: String = "", // 生理周期阶段
    val contraceptionMethod: String = "", // 避孕方式
    val healthNotes: String = "", // 健康注意事项
    
    // 私密标签
    val tags: String = "", // 自定义标签，以逗号分隔
    val isSpecialOccasion: Boolean = false, // 是否特殊场合
    val occasionNote: String = "", // 特殊场合说明
    
    // 隐私和加密
    val isEncrypted: Boolean = true, // 是否加密存储
    val privacyLevel: Int = 3, // 隐私级别 (1-3, 3最高)
    
    // 元数据
    val createdAt: Long = System.currentTimeMillis(),
    val updatedAt: Long = System.currentTimeMillis(),
    val syncStatus: String = "local" // 同步状态
)