package com.xue.love.data.local.entity

import androidx.room.Entity
import androidx.room.PrimaryKey

@Entity(tableName = "menstrual_cycles")
data class MenstrualCycleEntity(
    @PrimaryKey
    val id: String,
    val userId: String,
    val cycleStartDate: Long, // 周期开始日期
    val cycleEndDate: Long? = null, // 周期结束日期
    
    // 月经信息
    val periodStartDate: Long, // 月经开始日期
    val periodEndDate: Long? = null, // 月经结束日期
    val periodDuration: Int = 0, // 月经持续天数
    val cycleDuration: Int = 28, // 周期长度(天)
    
    // 流量记录
    val flowLevel: String = "", // 流量等级 (light/medium/heavy/spotting)
    val flowColor: String = "", // 颜色 (bright_red/dark_red/brown/pink)
    val clotsPresent: Boolean = false, // 是否有血块
    
    // 症状记录
    val cramps: Int = 0, // 痛经程度 (0-10)
    val headache: Int = 0, // 头痛程度 (0-10)
    val moodSwings: Int = 0, // 情绪波动 (0-10)
    val bloating: Int = 0, // 腹胀程度 (0-10)
    val breastTenderness: Int = 0, // 乳房胀痛 (0-10)
    val fatigue: Int = 0, // 疲劳程度 (0-10)
    val acne: Boolean = false, // 是否长痘
    val appetite: String = "", // 食欲变化 (increased/decreased/normal)
    
    // 情绪和心理状态
    val mood: String = "", // 主要情绪 (happy/sad/irritable/anxious/normal)
    val stressLevel: Int = 0, // 压力水平 (0-10)
    val sleepQuality: Int = 5, // 睡眠质量 (1-10)
    val energyLevel: Int = 5, // 精力水平 (1-10)
    val libido: Int = 5, // 性欲水平 (1-10)
    
    // 排卵相关
    val ovulationDate: Long? = null, // 排卵日期
    val ovulationSigns: String = "", // 排卵征象 (cervical_mucus/temperature/pain)
    val basalBodyTemperature: Float = 0f, // 基础体温
    val cervicalMucus: String = "", // 宫颈粘液状态
    
    // 药物和治疗
    val medications: String = "", // 服用药物，以逗号分隔
    val painRelief: String = "", // 止痛措施
    val contraception: String = "", // 避孕方式
    val supplements: String = "", // 营养补充剂
    
    // 生活方式
    val exercise: String = "", // 运动情况
    val diet: String = "", // 饮食特点
    val hydration: Int = 0, // 饮水量(杯)
    val alcohol: Boolean = false, // 是否饮酒
    val smoking: Boolean = false, // 是否吸烟
    
    // 性生活相关
    val sexualActivity: Boolean = false, // 是否有性生活
    val sexualComfort: Int = 5, // 性生活舒适度 (1-10)
    val sexualDesire: Int = 5, // 性欲强度 (1-10)
    
    // 预测和分析
    val nextPeriodPredicted: Long? = null, // 预测下次月经日期
    val nextOvulationPredicted: Long? = null, // 预测下次排卵日期
    val fertilityWindow: String = "", // 受孕窗口期
    val cyclePhase: String = "", // 当前周期阶段 (menstrual/follicular/ovulation/luteal)
    
    // 医疗相关
    val irregularities: String = "", // 异常情况记录
    val doctorVisit: Long? = null, // 就医时间
    val medicalNotes: String = "", // 医疗备注
    val pregnancyTest: Boolean = false, // 是否做孕检
    val pregnancyTestResult: String = "", // 孕检结果
    
    // 伴侣关怀
    val partnerSupport: Int = 0, // 伴侣支持程度 (0-10)
    val communicationQuality: Int = 0, // 沟通质量 (0-10)
    val partnerUnderstanding: String = "", // 伴侣理解度
    
    // 备注和标签
    val notes: String = "", // 个人备注
    val tags: String = "", // 自定义标签
    val isSpecialCycle: Boolean = false, // 是否特殊周期
    val specialNotes: String = "", // 特殊情况说明
    
    // 数据状态
    val isComplete: Boolean = false, // 周期是否完整记录
    val dataQuality: String = "good", // 数据质量评级
    val createdAt: Long = System.currentTimeMillis(),
    val updatedAt: Long = System.currentTimeMillis()
)