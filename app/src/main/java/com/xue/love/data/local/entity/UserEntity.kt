package com.xue.love.data.local.entity

import androidx.room.Entity
import androidx.room.PrimaryKey

@Entity(tableName = "users")
data class UserEntity(
    @PrimaryKey
    val id: String,
    val nickname: String,
    val avatar: String? = null,
    val email: String? = null,
    val gender: String? = null,
    val birthday: Long? = null,
    val createdAt: Long = System.currentTimeMillis(),
    val updatedAt: Long = System.currentTimeMillis(),
    val isCurrentUser: Boolean = false
)