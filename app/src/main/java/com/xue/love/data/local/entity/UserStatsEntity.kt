package com.xue.love.data.local.entity

import androidx.room.Entity
import androidx.room.PrimaryKey

@Entity(tableName = "user_stats")
data class UserStatsEntity(
    @PrimaryKey
    val userId: String,
    
    // 原有统计
    val messageCount: Int = 0,
    val heartCount: Int = 0,
    val interactionCount: Int = 0,
    val photoCount: Int = 0,
    val vibrationCount: Int = 0,
    val kissCount: Int = 0,
    val intimacyLevel: Float = 0f,
    val daysInRelationship: Int = 0,
    
    // 新增私密功能统计
    val intimateRecordsCount: Int = 0, // 亲密记录数量
    val totalIntimateTime: Int = 0, // 总亲密时间(分钟)
    val averageSatisfaction: Float = 5f, // 平均满意度
    val intimateFrequency: Float = 0f, // 亲密频率(次/周)
    
    // 生理周期统计
    val menstrualCyclesTracked: Int = 0, // 追踪的周期数
    val averageCycleLength: Int = 28, // 平均周期长度
    val averagePeriodLength: Int = 5, // 平均月经长度
    val lastPeriodDate: Long? = null, // 上次月经日期
    val nextPeriodPredicted: Long? = null, // 预测下次月经
    
    // 甜蜜约定统计
    val appointmentsCreated: Int = 0, // 创建的约定数
    val appointmentsCompleted: Int = 0, // 完成的约定数
    val appointmentSuccessRate: Float = 0f, // 约定成功率
    val averageAppointmentSatisfaction: Float = 5f, // 平均约定满意度
    
    // 游戏统计
    val gamesPlayed: Int = 0, // 游戏次数
    val totalGameTime: Int = 0, // 总游戏时间(分钟)
    val averageGameScore: Float = 0f, // 平均游戏得分
    val compatibilityScore: Float = 0f, // 默契度分数
    val favoriteGameType: String = "", // 最喜欢的游戏类型
    
    // 挑战统计
    val challengesCompleted: Int = 0, // 完成的挑战数
    val challengeStreak: Int = 0, // 连续挑战天数
    val maxChallengeStreak: Int = 0, // 最长连续天数
    val totalChallengePoints: Int = 0, // 总挑战积分
    val averageChallengeSatisfaction: Float = 5f, // 平均挑战满意度
    
    // 关系健康指标
    val communicationQuality: Float = 5f, // 沟通质量 (1-10)
    val emotionalConnection: Float = 5f, // 情感连接 (1-10)
    val relationshipSatisfaction: Float = 5f, // 关系满意度 (1-10)
    val trustLevel: Float = 5f, // 信任程度 (1-10)
    val supportLevel: Float = 5f, // 支持程度 (1-10)
    
    // 个人成长指标
    val confidenceLevel: Float = 5f, // 自信程度 (1-10)
    val selfAwarenessLevel: Float = 5f, // 自我认知 (1-10)
    val emotionalIntelligence: Float = 5f, // 情商 (1-10)
    val communicationSkills: Float = 5f, // 沟通技巧 (1-10)
    val intimacyComfort: Float = 5f, // 亲密舒适度 (1-10)
    
    // 健康和幸福指标
    val overallHappiness: Float = 5f, // 整体幸福感 (1-10)
    val stressLevel: Float = 5f, // 压力水平 (1-10)
    val sleepQuality: Float = 5f, // 睡眠质量 (1-10)
    val energyLevel: Float = 5f, // 精力水平 (1-10)
    val mentalHealth: Float = 5f, // 心理健康 (1-10)
    
    // 成就和里程碑
    val totalAchievements: Int = 0, // 总成就数
    val rareBadges: Int = 0, // 稀有徽章数
    val personalRecords: Int = 0, // 个人记录数
    val relationshipMilestones: Int = 0, // 关系里程碑数
    val explorationAchievements: Int = 0, // 探索成就数
    
    // 时间和活跃度
    val totalAppTime: Long = 0, // 总使用时间(毫秒)
    val averageDailyUsage: Int = 0, // 平均每日使用(分钟)
    val mostActiveHour: Int = 20, // 最活跃时间(24小时制)
    val longestSession: Int = 0, // 最长单次使用(分钟)
    val lastActiveAt: Long = System.currentTimeMillis(),
    
    // 偏好和个性化
    val preferredIntimacyTime: String = "evening", // 偏好的亲密时间
    val preferredGameDifficulty: String = "medium", // 偏好的游戏难度
    val preferredChallengeType: String = "romantic", // 偏好的挑战类型
    val personalityType: String = "", // 性格类型
    val loveLanguage: String = "", // 爱语类型
    
    // 数据质量和可靠性
    val dataCompleteness: Float = 0f, // 数据完整度 (0-1)
    val trackingConsistency: Float = 0f, // 追踪一致性 (0-1)
    val selfReportAccuracy: Float = 0f, // 自报准确性 (0-1)
    val lastDataReview: Long? = null, // 上次数据审查
    val dataVersion: String = "1.0", // 数据版本
    
    val updatedAt: Long = System.currentTimeMillis()
)