package com.xue.love.data.model

/**
 * 经期预测数据类
 */
data class PeriodPrediction(
    val nextPeriodDate: Long,           // 下次月经开始时间戳
    val confidence: Float,              // 预测置信度 (0-1)
    val estimatedCycleLength: Int,      // 预计周期长度(天)
    val estimatedPeriodLength: Int      // 预计月经持续天数
)

/**
 * 排卵期预测数据类
 */
data class OvulationPrediction(
    val ovulationDate: Long,            // 排卵日时间戳
    val fertileWindowStart: Long,       // 易孕期开始时间戳
    val fertileWindowEnd: Long,         // 易孕期结束时间戳
    val confidence: Float               // 预测置信度 (0-1)
)

/**
 * 生理健康洞察数据类
 */
data class MenstrualHealthInsights(
    val totalCycles: Int,               // 总周期数
    val averageCycleLength: Float,      // 平均周期长度
    val cycleRegularity: Float,         // 周期规律性 (0-1)
    val currentPhase: CyclePhase,       // 当前阶段
    val healthScore: Float,             // 健康评分 (0-1)
    val lastUpdated: Long?              // 最后更新时间戳
)