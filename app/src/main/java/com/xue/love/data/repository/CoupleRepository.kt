package com.xue.love.data.repository

import com.xue.love.data.local.dao.*
import com.xue.love.data.local.entity.*
import kotlinx.coroutines.flow.Flow
// 暂时禁用Hilt
// import javax.inject.Inject
// import javax.inject.Singleton

// 暂时禁用Hilt
// @Singleton
class CoupleRepository(
    private val userDao: UserDao,
    private val chatMessageDao: ChatMessageDao,
    private val userStatsDao: UserStatsDao,
    private val achievementDao: AchievementDao,
    private val coupleProfileDao: CoupleProfileDao,
    private val albumPhotoDao: AlbumPhotoDao
) {
    
    // User operations
    suspend fun getCurrentUser(): UserEntity? = userDao.getCurrentUser()
    fun getCurrentUserFlow(): Flow<UserEntity?> = userDao.getCurrentUserFlow()
    suspend fun insertUser(user: UserEntity) = userDao.insertUser(user)
    suspend fun updateUserNickname(userId: String, nickname: String) = 
        userDao.updateNickname(userId, nickname)
    suspend fun updateUserAvatar(userId: String, avatarUrl: String) = 
        userDao.updateAvatar(userId, avatarUrl)
    
    // Chat operations
    fun getAllMessagesFlow(): Flow<List<ChatMessageEntity>> = chatMessageDao.getAllMessagesFlow()
    fun getConversationFlow(userId1: String, userId2: String): Flow<List<ChatMessageEntity>> = 
        chatMessageDao.getConversationFlow(userId1, userId2)
    suspend fun insertMessage(message: ChatMessageEntity) = chatMessageDao.insertMessage(message)
    suspend fun markMessageAsRead(messageId: String) = chatMessageDao.markAsRead(messageId)
    fun getUnreadMessageCountFlow(userId: String): Flow<Int> = 
        chatMessageDao.getUnreadMessageCountFlow(userId)
    suspend fun getMessageCountByType(userId: String, messageType: String): Int = 
        chatMessageDao.getMessageCountByType(userId, messageType)
    
    // User stats operations
    suspend fun getUserStats(userId: String): UserStatsEntity? = userStatsDao.getUserStats(userId)
    fun getUserStatsFlow(userId: String): Flow<UserStatsEntity?> = 
        userStatsDao.getUserStatsFlow(userId)
    suspend fun insertUserStats(stats: UserStatsEntity) = userStatsDao.insertUserStats(stats)
    suspend fun incrementMessageCount(userId: String) = userStatsDao.incrementMessageCount(userId)
    suspend fun incrementHeartCount(userId: String) = userStatsDao.incrementHeartCount(userId)
    suspend fun incrementInteractionCount(userId: String) = userStatsDao.incrementInteractionCount(userId)
    suspend fun incrementPhotoCount(userId: String) = userStatsDao.incrementPhotoCount(userId)
    suspend fun updateIntimacyLevel(userId: String, level: Float) = 
        userStatsDao.updateIntimacyLevel(userId, level)
    suspend fun updateDaysInRelationship(userId: String, days: Int) = 
        userStatsDao.updateDaysInRelationship(userId, days)
    
    // Achievement operations
    fun getAllAchievementsFlow(): Flow<List<AchievementEntity>> = achievementDao.getAllAchievementsFlow()
    fun getUnlockedAchievements(): Flow<List<AchievementEntity>> = achievementDao.getUnlockedAchievements()
    suspend fun insertAchievements(achievements: List<AchievementEntity>) = 
        achievementDao.insertAchievements(achievements)
    suspend fun updateAchievementProgress(achievementId: String, currentValue: Int) = 
        achievementDao.updateProgress(achievementId, currentValue)
    suspend fun unlockAchievement(achievementId: String) = 
        achievementDao.unlockAchievement(achievementId)
    fun getUnlockedAchievementCountFlow(): Flow<Int> = achievementDao.getUnlockedAchievementCountFlow()
    
    // Couple profile operations
    suspend fun getCoupleProfile(userId: String): CoupleProfileEntity? = 
        coupleProfileDao.getCoupleProfile(userId)
    fun getCoupleProfileFlow(userId: String): Flow<CoupleProfileEntity?> = 
        coupleProfileDao.getCoupleProfileFlow(userId)
    suspend fun insertCoupleProfile(profile: CoupleProfileEntity) = 
        coupleProfileDao.insertCoupleProfile(profile)
    suspend fun updateCoupleNickname(userId: String, nickname: String) = 
        coupleProfileDao.updateNickname(userId, nickname)
    suspend fun updateTheme(userId: String, theme: String) = 
        coupleProfileDao.updateTheme(userId, theme)
    suspend fun updateDarkMode(userId: String, isDarkMode: Boolean) = 
        coupleProfileDao.updateDarkMode(userId, isDarkMode)
    suspend fun updatePrivacyMode(userId: String, isPrivacyMode: Boolean) = 
        coupleProfileDao.updatePrivacyMode(userId, isPrivacyMode)
    suspend fun updateNotificationEnabled(userId: String, isEnabled: Boolean) = 
        coupleProfileDao.updateNotificationEnabled(userId, isEnabled)
    suspend fun updateBiometricEnabled(userId: String, isEnabled: Boolean) = 
        coupleProfileDao.updateBiometricEnabled(userId, isEnabled)
    
    // Album operations
    fun getPhotosFlow(coupleId: String): Flow<List<AlbumPhotoEntity>> = 
        albumPhotoDao.getPhotosFlow(coupleId)
    fun getFavoritePhotosFlow(coupleId: String): Flow<List<AlbumPhotoEntity>> = 
        albumPhotoDao.getFavoritePhotosFlow(coupleId)
    fun getPhotoCountFlow(coupleId: String): Flow<Int> = albumPhotoDao.getPhotoCountFlow(coupleId)
    suspend fun insertPhoto(photo: AlbumPhotoEntity) = albumPhotoDao.insertPhoto(photo)
    suspend fun updatePhotoFavorite(photoId: String, isFavorite: Boolean) = 
        albumPhotoDao.updateFavorite(photoId, isFavorite)
    suspend fun updatePhotoCaption(photoId: String, caption: String) = 
        albumPhotoDao.updateCaption(photoId, caption)
    suspend fun deletePhoto(photoId: String) = albumPhotoDao.deletePhotoById(photoId)
}