package com.xue.love.data.repository

import com.xue.love.data.local.dao.IntimateAppointmentDao
import com.xue.love.data.local.entity.IntimateAppointmentEntity
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.util.*

class IntimateAppointmentRepository(
    private val intimateAppointmentDao: IntimateAppointmentDao
) {
    
    // 基本CRUD操作
    suspend fun insertAppointment(appointment: IntimateAppointmentEntity) = withContext(Dispatchers.IO) {
        intimateAppointmentDao.insertAppointment(appointment)
    }
    
    suspend fun updateAppointment(appointment: IntimateAppointmentEntity) = withContext(Dispatchers.IO) {
        intimateAppointmentDao.updateAppointment(appointment)
    }
    
    suspend fun deleteAppointment(appointment: IntimateAppointmentEntity) = withContext(Dispatchers.IO) {
        intimateAppointmentDao.deleteAppointment(appointment)
    }
    
    suspend fun deleteAppointmentById(appointmentId: String) = withContext(Dispatchers.IO) {
        intimateAppointmentDao.deleteAppointmentById(appointmentId)
    }
    
    // 查询操作
    suspend fun getAppointmentById(appointmentId: String): IntimateAppointmentEntity? = withContext(Dispatchers.IO) {
        intimateAppointmentDao.getAppointmentById(appointmentId)
    }
    
    fun getAppointmentsByUser(userId: String): Flow<List<IntimateAppointmentEntity>> {
        return intimateAppointmentDao.getAppointmentsByUser(userId)
    }
    
    fun getAppointmentsByCouple(userId: String, partnerId: String): Flow<List<IntimateAppointmentEntity>> {
        return intimateAppointmentDao.getAppointmentsByCouple(userId, partnerId)
    }
    
    fun getAllAppointments(): Flow<List<IntimateAppointmentEntity>> {
        return intimateAppointmentDao.getAllAppointments()
    }
    
    // 状态查询
    fun getAppointmentsByStatus(userId: String, status: String): Flow<List<IntimateAppointmentEntity>> {
        return intimateAppointmentDao.getAppointmentsByStatus(userId, status)
    }
    
    fun getPendingAppointments(userId: String): Flow<List<IntimateAppointmentEntity>> {
        return intimateAppointmentDao.getPendingAppointments(userId)
    }
    
    fun getAcceptedAppointments(userId: String): Flow<List<IntimateAppointmentEntity>> {
        return intimateAppointmentDao.getAcceptedAppointments(userId)
    }
    
    fun getCompletedAppointments(userId: String): Flow<List<IntimateAppointmentEntity>> {
        return intimateAppointmentDao.getCompletedAppointments(userId)
    }
    
    // 时间范围查询
    fun getAppointmentsByTimeRange(userId: String, startTime: Long, endTime: Long): Flow<List<IntimateAppointmentEntity>> {
        return intimateAppointmentDao.getAppointmentsByTimeRange(userId, startTime, endTime)
    }
    
    fun getUpcomingAppointments(userId: String, withinHours: Int = 24): Flow<List<IntimateAppointmentEntity>> {
        val currentTime = System.currentTimeMillis()
        val futureTime = currentTime + (withinHours * 60 * 60 * 1000L)
        return intimateAppointmentDao.getUpcomingAppointments(userId, currentTime, futureTime)
    }
    
    fun getTodayAppointments(userId: String): Flow<List<IntimateAppointmentEntity>> {
        val currentTime = System.currentTimeMillis()
        val calendar = java.util.Calendar.getInstance()
        calendar.timeInMillis = currentTime
        calendar.set(java.util.Calendar.HOUR_OF_DAY, 0)
        calendar.set(java.util.Calendar.MINUTE, 0)
        calendar.set(java.util.Calendar.SECOND, 0)
        calendar.set(java.util.Calendar.MILLISECOND, 0)
        val dayStart = calendar.timeInMillis
        
        calendar.add(java.util.Calendar.DAY_OF_MONTH, 1)
        val dayEnd = calendar.timeInMillis
        
        return intimateAppointmentDao.getTodayAppointments(userId, dayStart, dayEnd)
    }
    
    // 分页查询
    suspend fun getAppointmentsPage(userId: String, limit: Int, offset: Int): List<IntimateAppointmentEntity> = withContext(Dispatchers.IO) {
        intimateAppointmentDao.getAppointmentsPage(userId, limit, offset)
    }
    
    // 统计查询
    suspend fun getAppointmentCount(userId: String): Int = withContext(Dispatchers.IO) {
        intimateAppointmentDao.getAppointmentCount(userId)
    }
    
    suspend fun getAppointmentCountByStatus(userId: String, status: String): Int = withContext(Dispatchers.IO) {
        intimateAppointmentDao.getAppointmentCountByStatus(userId, status)
    }
    
    suspend fun getCompletionRate(userId: String): Float = withContext(Dispatchers.IO) {
        val totalCount = getAppointmentCount(userId)
        if (totalCount == 0) return@withContext 0f
        val completedCount = getAppointmentCountByStatus(userId, "completed")
        completedCount.toFloat() / totalCount * 100f
    }
    
    // 最近记录
    suspend fun getLatestAppointment(userId: String): IntimateAppointmentEntity? = withContext(Dispatchers.IO) {
        intimateAppointmentDao.getLatestAppointment(userId)
    }
    
    suspend fun getRecentAppointments(userId: String, limit: Int): List<IntimateAppointmentEntity> = withContext(Dispatchers.IO) {
        intimateAppointmentDao.getRecentAppointments(userId, limit)
    }
    
    // 搜索和过滤
    fun searchAppointments(userId: String, searchTerm: String): Flow<List<IntimateAppointmentEntity>> {
        return intimateAppointmentDao.searchAppointments(userId, searchTerm)
    }
    
    fun getAppointmentsByCategory(userId: String, category: String): Flow<List<IntimateAppointmentEntity>> {
        return intimateAppointmentDao.getAppointmentsByCategory(userId, category)
    }
    
    fun getAppointmentsByMood(userId: String, mood: String): Flow<List<IntimateAppointmentEntity>> {
        return intimateAppointmentDao.getAppointmentsByMood(userId, mood)
    }
    
    fun getAppointmentsByLocation(userId: String, location: String): Flow<List<IntimateAppointmentEntity>> {
        return intimateAppointmentDao.getAppointmentsByLocation(userId, location)
    }
    
    // 统计分析
    suspend fun getCategoryStatistics(userId: String): List<com.xue.love.data.local.dao.CategoryCount> = withContext(Dispatchers.IO) {
        intimateAppointmentDao.getCategoryStatistics(userId)
    }
    
    suspend fun getMoodStatistics(userId: String): List<com.xue.love.data.local.dao.MoodCount> = withContext(Dispatchers.IO) {
        intimateAppointmentDao.getMoodStatistics(userId)
    }
    
    suspend fun getLocationStatistics(userId: String): List<com.xue.love.data.local.dao.LocationCount> = withContext(Dispatchers.IO) {
        intimateAppointmentDao.getLocationStatistics(userId)
    }
    
    // 月度统计
    suspend fun getMonthlyStatistics(userId: String, months: Int = 12): List<com.xue.love.data.local.dao.MonthlyCount> = withContext(Dispatchers.IO) {
        val sinceTime = System.currentTimeMillis() - (months * 30L * 24 * 60 * 60 * 1000)
        intimateAppointmentDao.getMonthlyStatistics(userId, sinceTime)
    }
    
    // 便捷方法
    suspend fun createNewAppointment(
        userId: String,
        partnerId: String,
        title: String,
        scheduledDateTime: Long,
        category: String = "romantic",
        location: String = "",
        description: String = ""
    ): IntimateAppointmentEntity = withContext(Dispatchers.IO) {
        val appointment = IntimateAppointmentEntity(
            id = UUID.randomUUID().toString(),
            userId = userId,
            partnerId = partnerId,
            title = title,
            scheduledDateTime = scheduledDateTime,
            category = category,
            location = location,
            description = description,
            status = "pending",
            createdAt = System.currentTimeMillis()
        )
        insertAppointment(appointment)
        appointment
    }
    
    // 约会管理操作
    suspend fun acceptAppointment(appointmentId: String, acceptedAt: Long = System.currentTimeMillis()) = withContext(Dispatchers.IO) {
        val appointment = getAppointmentById(appointmentId)
        appointment?.let {
            val updatedAppointment = it.copy(
                status = "accepted",
                responseDateTime = acceptedAt,
                partnerResponse = "accepted"
            )
            updateAppointment(updatedAppointment)
        }
    }
    
    suspend fun declineAppointment(appointmentId: String, reason: String = "", declinedAt: Long = System.currentTimeMillis()) = withContext(Dispatchers.IO) {
        val appointment = getAppointmentById(appointmentId)
        appointment?.let {
            val updatedAppointment = it.copy(
                status = "declined",
                responseDateTime = declinedAt,
                partnerResponse = "declined",
                cancellationReason = reason
            )
            updateAppointment(updatedAppointment)
        }
    }
    
    suspend fun completeAppointment(appointmentId: String, completedAt: Long = System.currentTimeMillis()) = withContext(Dispatchers.IO) {
        val appointment = getAppointmentById(appointmentId)
        appointment?.let {
            val updatedAppointment = it.copy(
                status = "completed",
                actualDateTime = completedAt,
                isCompleted = true
            )
            updateAppointment(updatedAppointment)
        }
    }
    
    suspend fun rescheduleAppointment(appointmentId: String, newDateTime: Long) = withContext(Dispatchers.IO) {
        val appointment = getAppointmentById(appointmentId)
        appointment?.let {
            val updatedAppointment = it.copy(
                scheduledDateTime = newDateTime,
                status = "pending", // 重新设为待确认
                partnerResponse = "",
                responseDateTime = null
            )
            updateAppointment(updatedAppointment)
        }
    }
    
    suspend fun cancelAppointment(appointmentId: String, reason: String = "") = withContext(Dispatchers.IO) {
        val appointment = getAppointmentById(appointmentId)
        appointment?.let {
            val updatedAppointment = it.copy(
                status = "cancelled",
                cancellationReason = reason,
                cancelledAt = System.currentTimeMillis()
            )
            updateAppointment(updatedAppointment)
        }
    }
    
    // 提醒和通知
    suspend fun getAppointmentsDueForReminder(userId: String, reminderMinutes: Int = 60): List<IntimateAppointmentEntity> = withContext(Dispatchers.IO) {
        val currentTime = System.currentTimeMillis()
        val reminderTime = currentTime + (reminderMinutes * 60 * 1000L)
        intimateAppointmentDao.getAppointmentsDueForReminder(userId, currentTime, reminderTime)
    }
    
    suspend fun markReminderSent(appointmentId: String) = withContext(Dispatchers.IO) {
        val appointment = getAppointmentById(appointmentId)
        appointment?.let {
            val updatedAppointment = it.copy(reminderSent = true)
            updateAppointment(updatedAppointment)
        }
    }
    
    // 数据分析
    suspend fun getAppointmentInsights(userId: String): AppointmentInsights = withContext(Dispatchers.IO) {
        val totalAppointments = getAppointmentCount(userId)
        val completionRate = getCompletionRate(userId)
        val pendingCount = getAppointmentCountByStatus(userId, "pending")
        val acceptedCount = getAppointmentCountByStatus(userId, "accepted")
        val completedCount = getAppointmentCountByStatus(userId, "completed")
        val cancelledCount = getAppointmentCountByStatus(userId, "cancelled")
        
        val categoryStats = getCategoryStatistics(userId)
        val mostPopularCategory = categoryStats.maxByOrNull { it.count }?.category ?: ""
        
        AppointmentInsights(
            totalAppointments = totalAppointments,
            completionRate = completionRate,
            pendingCount = pendingCount,
            acceptedCount = acceptedCount,
            completedCount = completedCount,
            cancelledCount = cancelledCount,
            mostPopularCategory = mostPopularCategory,
            lastAppointmentDate = getLatestAppointment(userId)?.scheduledDateTime
        )
    }
    
    // 推荐系统
    suspend fun getAppointmentRecommendations(userId: String): List<AppointmentRecommendation> = withContext(Dispatchers.IO) {
        val recentAppointments = getRecentAppointments(userId, 10)
        val categoryStats = getCategoryStatistics(userId)
        val moodStats = getMoodStatistics(userId)
        
        generateRecommendations(recentAppointments, categoryStats, moodStats)
    }
    
    private fun generateRecommendations(
        recentAppointments: List<IntimateAppointmentEntity>,
        categoryStats: List<com.xue.love.data.local.dao.CategoryCount>,
        moodStats: List<com.xue.love.data.local.dao.MoodCount>
    ): List<AppointmentRecommendation> {
        val recommendations = mutableListOf<AppointmentRecommendation>()
        
        // 基于最受欢迎类别的推荐
        val topCategory = categoryStats.maxByOrNull { it.count }
        if (topCategory != null && topCategory.count >= 3) {
            recommendations.add(
                AppointmentRecommendation(
                    type = "category",
                    title = "继续${getCategoryDisplayName(topCategory.category)}",
                    description = "你们似乎很喜欢${getCategoryDisplayName(topCategory.category)}类型的约会",
                    category = topCategory.category,
                    priority = RecommendationPriority.HIGH
                )
            )
        }
        
        // 基于时间模式的推荐
        if (recentAppointments.isNotEmpty()) {
            val avgHour = recentAppointments.map { appointment ->
                val calendar = Calendar.getInstance()
                calendar.timeInMillis = appointment.scheduledDateTime
                calendar.get(Calendar.HOUR_OF_DAY)
            }.average().toInt()
            
            val timeSlot = when (avgHour) {
                in 6..11 -> "morning"
                in 12..17 -> "afternoon"
                in 18..22 -> "evening"
                else -> "night"
            }
            
            recommendations.add(
                AppointmentRecommendation(
                    type = "time",
                    title = "在${getTimeSlotDisplayName(timeSlot)}安排约会",
                    description = "基于你们的习惯，${getTimeSlotDisplayName(timeSlot)}是最佳约会时间",
                    category = "time-based",
                    priority = RecommendationPriority.MEDIUM
                )
            )
        }
        
        // 基于频率的推荐
        val daysSinceLastAppointment = recentAppointments.firstOrNull()?.let { lastAppt ->
            ((System.currentTimeMillis() - lastAppt.scheduledDateTime) / (24 * 60 * 60 * 1000L)).toInt()
        } ?: 7
        
        if (daysSinceLastAppointment >= 7) {
            recommendations.add(
                AppointmentRecommendation(
                    type = "frequency",
                    title = "是时候安排新约会了",
                    description = "距离上次约会已经过去${daysSinceLastAppointment}天，来计划一个新的甜蜜时光吧",
                    category = "reminder",
                    priority = RecommendationPriority.HIGH
                )
            )
        }
        
        return recommendations
    }
    
    private fun getCategoryDisplayName(category: String): String {
        return when (category) {
            "romantic" -> "浪漫"
            "intimate" -> "亲密"
            "adventure" -> "冒险"
            "relaxing" -> "休闲"
            "special" -> "特殊"
            else -> category
        }
    }
    
    private fun getTimeSlotDisplayName(timeSlot: String): String {
        return when (timeSlot) {
            "morning" -> "上午"
            "afternoon" -> "下午"
            "evening" -> "晚上"
            "night" -> "深夜"
            else -> timeSlot
        }
    }
}

// 数据类
data class AppointmentInsights(
    val totalAppointments: Int,
    val completionRate: Float,
    val pendingCount: Int,
    val acceptedCount: Int,
    val completedCount: Int,
    val cancelledCount: Int,
    val mostPopularCategory: String,
    val lastAppointmentDate: Long?
)

data class AppointmentRecommendation(
    val type: String,
    val title: String,
    val description: String,
    val category: String,
    val priority: RecommendationPriority
)

enum class RecommendationPriority {
    HIGH, MEDIUM, LOW
}