package com.xue.love.data.repository

import com.xue.love.data.local.dao.IntimateRecordDao
import com.xue.love.data.local.entity.IntimateRecordEntity
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.util.UUID

class IntimateRecordRepository(
    private val intimateRecordDao: IntimateRecordDao
) {
    
    // 基本CRUD操作
    suspend fun insertRecord(record: IntimateRecordEntity) = withContext(Dispatchers.IO) {
        intimateRecordDao.insertRecord(record)
    }
    
    suspend fun updateRecord(record: IntimateRecordEntity) = withContext(Dispatchers.IO) {
        intimateRecordDao.updateRecord(record)
    }
    
    suspend fun deleteRecord(record: IntimateRecordEntity) = withContext(Dispatchers.IO) {
        intimateRecordDao.deleteRecord(record)
    }
    
    suspend fun deleteRecordById(recordId: String) = withContext(Dispatchers.IO) {
        intimateRecordDao.deleteRecordById(recordId)
    }
    
    // 查询操作
    suspend fun getRecordById(recordId: String): IntimateRecordEntity? = withContext(Dispatchers.IO) {
        intimateRecordDao.getRecordById(recordId)
    }
    
    fun getRecordsByUser(userId: String): Flow<List<IntimateRecordEntity>> {
        return intimateRecordDao.getRecordsByUser(userId)
    }
    
    fun getRecordsByCouple(userId: String, partnerId: String): Flow<List<IntimateRecordEntity>> {
        return intimateRecordDao.getRecordsByCouple(userId, partnerId)
    }
    
    fun getAllRecords(): Flow<List<IntimateRecordEntity>> {
        return intimateRecordDao.getAllRecords()
    }
    
    // 分页查询
    suspend fun getRecordsPage(userId: String, limit: Int, offset: Int): List<IntimateRecordEntity> = withContext(Dispatchers.IO) {
        intimateRecordDao.getRecordsPage(userId, limit, offset)
    }
    
    // 时间范围查询
    fun getRecordsByTimeRange(userId: String, startTime: Long, endTime: Long): Flow<List<IntimateRecordEntity>> {
        return intimateRecordDao.getRecordsByTimeRange(userId, startTime, endTime)
    }
    
    fun getRecordsSince(userId: String, sinceTime: Long): Flow<List<IntimateRecordEntity>> {
        return intimateRecordDao.getRecordsSince(userId, sinceTime)
    }
    
    // 统计查询
    suspend fun getRecordCount(userId: String): Int = withContext(Dispatchers.IO) {
        intimateRecordDao.getRecordCount(userId)
    }
    
    suspend fun getRecordCountSince(userId: String, sinceTime: Long): Int = withContext(Dispatchers.IO) {
        intimateRecordDao.getRecordCountSince(userId, sinceTime)
    }
    
    suspend fun getAverageSatisfaction(userId: String): Float = withContext(Dispatchers.IO) {
        intimateRecordDao.getAverageSatisfaction(userId) ?: 5f
    }
    
    suspend fun getAveragePartnerSatisfaction(userId: String): Float = withContext(Dispatchers.IO) {
        intimateRecordDao.getAveragePartnerSatisfaction(userId) ?: 5f
    }
    
    suspend fun getTotalDuration(userId: String): Int = withContext(Dispatchers.IO) {
        intimateRecordDao.getTotalDuration(userId) ?: 0
    }
    
    suspend fun getAverageDuration(userId: String): Float = withContext(Dispatchers.IO) {
        intimateRecordDao.getAverageDuration(userId) ?: 0f
    }
    
    // 高级统计
    suspend fun getRecordCountByProtection(userId: String, protectionUsed: Boolean): Int = withContext(Dispatchers.IO) {
        intimateRecordDao.getRecordCountByProtection(userId, protectionUsed)
    }
    
    suspend fun getMoodStatistics(userId: String) = withContext(Dispatchers.IO) {
        intimateRecordDao.getMoodStatistics(userId)
    }
    
    suspend fun getLocationStatistics(userId: String) = withContext(Dispatchers.IO) {
        intimateRecordDao.getLocationStatistics(userId)
    }
    
    suspend fun getClimaxStatistics(userId: String) = withContext(Dispatchers.IO) {
        intimateRecordDao.getClimaxStatistics(userId)
    }
    
    // 最近记录
    suspend fun getLatestRecord(userId: String): IntimateRecordEntity? = withContext(Dispatchers.IO) {
        intimateRecordDao.getLatestRecord(userId)
    }
    
    suspend fun getRecentRecords(userId: String, limit: Int): List<IntimateRecordEntity> = withContext(Dispatchers.IO) {
        intimateRecordDao.getRecentRecords(userId, limit)
    }
    
    // 搜索和过滤
    fun searchRecords(userId: String, searchTerm: String): Flow<List<IntimateRecordEntity>> {
        return intimateRecordDao.searchRecords(userId, searchTerm)
    }
    
    fun getRecordsByMood(userId: String, mood: String): Flow<List<IntimateRecordEntity>> {
        return intimateRecordDao.getRecordsByMood(userId, mood)
    }
    
    fun getRecordsByLocation(userId: String, location: String): Flow<List<IntimateRecordEntity>> {
        return intimateRecordDao.getRecordsByLocation(userId, location)
    }
    
    fun getRecordsBySatisfaction(userId: String, minSatisfaction: Int): Flow<List<IntimateRecordEntity>> {
        return intimateRecordDao.getRecordsBySatisfaction(userId, minSatisfaction)
    }
    
    // 特殊查询
    fun getSpecialOccasionRecords(userId: String): Flow<List<IntimateRecordEntity>> {
        return intimateRecordDao.getSpecialOccasionRecords(userId)
    }
    
    fun getRecordsByPrivacyLevel(userId: String, privacyLevel: Int): Flow<List<IntimateRecordEntity>> {
        return intimateRecordDao.getRecordsByPrivacyLevel(userId, privacyLevel)
    }
    
    suspend fun getAllTags(userId: String): List<String> = withContext(Dispatchers.IO) {
        intimateRecordDao.getAllTags(userId)
    }
    
    suspend fun getAllMoods(userId: String): List<String> = withContext(Dispatchers.IO) {
        intimateRecordDao.getAllMoods(userId)
    }
    
    suspend fun getAllLocations(userId: String): List<String> = withContext(Dispatchers.IO) {
        intimateRecordDao.getAllLocations(userId)
    }
    
    // 周期性统计
    suspend fun getDailyStatistics(userId: String, days: Int = 30) = withContext(Dispatchers.IO) {
        intimateRecordDao.getDailyStatistics(userId, days)
    }
    
    suspend fun getWeeklyStatistics(userId: String, weeks: Int = 12) = withContext(Dispatchers.IO) {
        intimateRecordDao.getWeeklyStatistics(userId, weeks)
    }
    
    suspend fun getMonthlyStatistics(userId: String, months: Int = 12) = withContext(Dispatchers.IO) {
        intimateRecordDao.getMonthlyStatistics(userId, months)
    }
    
    // 便捷方法
    suspend fun createNewRecord(
        userId: String,
        partnerId: String,
        location: String = "",
        duration: Int = 0,
        protectionUsed: Boolean = false,
        satisfaction: Int = 5,
        partnerSatisfaction: Int = 5,
        mood: String = "",
        notes: String = ""
    ): IntimateRecordEntity = withContext(Dispatchers.IO) {
        val record = IntimateRecordEntity(
            id = UUID.randomUUID().toString(),
            userId = userId,
            partnerId = partnerId,
            location = location,
            duration = duration,
            protectionUsed = protectionUsed,
            satisfaction = satisfaction,
            partnerSatisfaction = partnerSatisfaction,
            mood = mood,
            physicalNotes = notes
        )
        insertRecord(record)
        record
    }
    
    // 数据分析方法
    suspend fun getIntimacyFrequency(userId: String, days: Int = 30): Float = withContext(Dispatchers.IO) {
        val startTime = System.currentTimeMillis() - (days * 24 * 60 * 60 * 1000L)
        val count = getRecordCountSince(userId, startTime)
        count.toFloat() / days * 7 // 每周频率
    }
    
    suspend fun getSatisfactionTrend(userId: String, limit: Int = 10): List<Float> = withContext(Dispatchers.IO) {
        val records = getRecentRecords(userId, limit)
        records.map { it.satisfaction.toFloat() }.reversed()
    }
    
    suspend fun getProtectionUsageRate(userId: String): Float = withContext(Dispatchers.IO) {
        val totalCount = getRecordCount(userId)
        if (totalCount == 0) return@withContext 0f
        val protectedCount = getRecordCountByProtection(userId, true)
        protectedCount.toFloat() / totalCount * 100
    }
    
    // 健康分析
    suspend fun getHealthInsights(userId: String): HealthInsights = withContext(Dispatchers.IO) {
        val totalRecords = getRecordCount(userId)
        val avgSatisfaction = getAverageSatisfaction(userId)
        val avgPartnerSatisfaction = getAveragePartnerSatisfaction(userId)
        val protectionRate = getProtectionUsageRate(userId)
        val frequency = getIntimacyFrequency(userId)
        
        HealthInsights(
            totalRecords = totalRecords,
            averageSatisfaction = avgSatisfaction,
            averagePartnerSatisfaction = avgPartnerSatisfaction,
            protectionUsageRate = protectionRate,
            weeklyFrequency = frequency,
            lastRecordDate = getLatestRecord(userId)?.timestamp
        )
    }
}

// 数据类用于健康分析
data class HealthInsights(
    val totalRecords: Int,
    val averageSatisfaction: Float,
    val averagePartnerSatisfaction: Float,
    val protectionUsageRate: Float,
    val weeklyFrequency: Float,
    val lastRecordDate: Long?
)