package com.xue.love.data.repository

import com.xue.love.data.local.dao.MenstrualCycleDao
import com.xue.love.data.local.entity.MenstrualCycleEntity
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.util.*

class MenstrualCycleRepository(
    private val menstrualCycleDao: MenstrualCycleDao
) {
    
    // 基本CRUD操作
    suspend fun insertCycle(cycle: MenstrualCycleEntity) = withContext(Dispatchers.IO) {
        menstrualCycleDao.insertCycle(cycle)
    }
    
    suspend fun updateCycle(cycle: MenstrualCycleEntity) = withContext(Dispatchers.IO) {
        menstrualCycleDao.updateCycle(cycle)
    }
    
    suspend fun deleteCycle(cycle: MenstrualCycleEntity) = withContext(Dispatchers.IO) {
        menstrualCycleDao.deleteCycle(cycle)
    }
    
    suspend fun deleteCycleById(cycleId: String) = withContext(Dispatchers.IO) {
        menstrualCycleDao.deleteCycleById(cycleId)
    }
    
    // 查询操作
    suspend fun getCycleById(cycleId: String): MenstrualCycleEntity? = withContext(Dispatchers.IO) {
        menstrualCycleDao.getCycleById(cycleId)
    }
    
    fun getCyclesByUser(userId: String): Flow<List<MenstrualCycleEntity>> {
        return menstrualCycleDao.getCyclesByUser(userId)
    }
    
    fun getAllCycles(): Flow<List<MenstrualCycleEntity>> {
        return menstrualCycleDao.getAllCycles()
    }
    
    // 当前和最近周期
    suspend fun getCurrentCycle(userId: String): MenstrualCycleEntity? = withContext(Dispatchers.IO) {
        menstrualCycleDao.getCurrentCycle(userId)
    }
    
    suspend fun getRecentCycles(userId: String, limit: Int): List<MenstrualCycleEntity> = withContext(Dispatchers.IO) {
        menstrualCycleDao.getRecentCycles(userId, limit)
    }
    
    suspend fun getCompletedCycles(userId: String, limit: Int): List<MenstrualCycleEntity> = withContext(Dispatchers.IO) {
        menstrualCycleDao.getCompletedCycles(userId, limit)
    }
    
    // 日期范围查询
    fun getCyclesByDateRange(userId: String, startDate: Long, endDate: Long): Flow<List<MenstrualCycleEntity>> {
        return menstrualCycleDao.getCyclesByDateRange(userId, startDate, endDate)
    }
    
    fun getPeriodsByDateRange(userId: String, startDate: Long, endDate: Long): Flow<List<MenstrualCycleEntity>> {
        return menstrualCycleDao.getPeriodsByDateRange(userId, startDate, endDate)
    }
    
    // 统计查询
    suspend fun getCycleCount(userId: String): Int = withContext(Dispatchers.IO) {
        menstrualCycleDao.getCycleCount(userId)
    }
    
    suspend fun getCompletedCycleCount(userId: String): Int = withContext(Dispatchers.IO) {
        menstrualCycleDao.getCompletedCycleCount(userId)
    }
    
    suspend fun getAverageCycleDuration(userId: String): Float? = withContext(Dispatchers.IO) {
        menstrualCycleDao.getAverageCycleDuration(userId)
    }
    
    suspend fun getAveragePeriodDuration(userId: String): Float? = withContext(Dispatchers.IO) {
        menstrualCycleDao.getAveragePeriodDuration(userId)
    }
    
    suspend fun getAverageCrampLevel(userId: String): Float? = withContext(Dispatchers.IO) {
        menstrualCycleDao.getAverageCrampLevel(userId)
    }
    
    // 症状统计
    suspend fun getFlowLevelStatistics(userId: String) = withContext(Dispatchers.IO) {
        menstrualCycleDao.getFlowLevelStatistics(userId)
    }
    
    suspend fun getMoodStatistics(userId: String) = withContext(Dispatchers.IO) {
        menstrualCycleDao.getMoodStatistics(userId)
    }
    
    // 月度统计
    suspend fun getMonthlyStatistics(userId: String, months: Int = 12) = withContext(Dispatchers.IO) {
        menstrualCycleDao.getMonthlyStatistics(userId, months)
    }
    
    // 便捷方法
    suspend fun createNewCycle(
        userId: String,
        cycleStartDate: Long,
        periodStartDate: Long,
        estimatedCycleLength: Int = 28
    ): MenstrualCycleEntity = withContext(Dispatchers.IO) {
        val cycle = MenstrualCycleEntity(
            id = UUID.randomUUID().toString(),
            userId = userId,
            cycleStartDate = cycleStartDate,
            periodStartDate = periodStartDate,
            cycleDuration = estimatedCycleLength,
            createdAt = System.currentTimeMillis()
        )
        insertCycle(cycle)
        cycle
    }
    
    // 预测下次月经
    suspend fun predictNextPeriod(userId: String): Long? = withContext(Dispatchers.IO) {
        val avgCycleDuration = getAverageCycleDuration(userId)
        val latestCycle = getCurrentCycle(userId)
        
        if (avgCycleDuration != null && latestCycle != null) {
            latestCycle.cycleStartDate + (avgCycleDuration.toLong() * 24 * 60 * 60 * 1000L)
        } else {
            null
        }
    }
    
    // 分析周期规律性
    suspend fun analyzeCycleRegularity(userId: String): Float = withContext(Dispatchers.IO) {
        val recentCycles = getCompletedCycles(userId, 6)
        if (recentCycles.size < 3) return@withContext 0f
        
        val cycleLengths = recentCycles.map { it.cycleDuration }
        val average = cycleLengths.average()
        val variance = cycleLengths.map { (it - average) * (it - average) }.average()
        val standardDeviation = kotlin.math.sqrt(variance)
        
        // 规律性评分：标准差越小，规律性越高
        (1f - (standardDeviation / average).coerceAtMost(1.0).toFloat()).coerceAtLeast(0f)
    }
}