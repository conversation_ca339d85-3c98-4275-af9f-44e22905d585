package com.xue.love.di

import android.content.Context
import androidx.room.Room
import com.xue.love.data.local.database.CoupleDatabase
import com.xue.love.data.repository.CoupleRepository
import com.xue.love.data.repository.IntimateRecordRepository
import com.xue.love.data.repository.MenstrualCycleRepository
import com.xue.love.data.repository.IntimateAppointmentRepository

/**
 * 应用级依赖容器
 * 替代Hilt的临时解决方案
 */
class AppContainer(private val context: Context) {
    
    // 数据库实例
    val database: CoupleDatabase by lazy {
        Room.databaseBuilder(
            context.applicationContext,
            CoupleDatabase::class.java,
            "couple_database"
        ).fallbackToDestructiveMigration()
         .build()
    }
    
    // Repository实例
    val coupleRepository: CoupleRepository by lazy {
        CoupleRepository(
            userDao = database.userDao(),
            chatMessageDao = database.chatMessageDao(),
            userStatsDao = database.userStatsDao(),
            achievementDao = database.achievementDao(),
            coupleProfileDao = database.coupleProfileDao(),
            albumPhotoDao = database.albumPhotoDao()
        )
    }
    
    val intimateRecordRepository: IntimateRecordRepository by lazy {
        IntimateRecordRepository(
            intimateRecordDao = database.intimateRecordDao()
        )
    }
    
    val menstrualCycleRepository: MenstrualCycleRepository by lazy {
        MenstrualCycleRepository(
            menstrualCycleDao = database.menstrualCycleDao()
        )
    }
    
    val intimateAppointmentRepository: IntimateAppointmentRepository by lazy {
        IntimateAppointmentRepository(
            intimateAppointmentDao = database.intimateAppointmentDao()
        )
    }
}