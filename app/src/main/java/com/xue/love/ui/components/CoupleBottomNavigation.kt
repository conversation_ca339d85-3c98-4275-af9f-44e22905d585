package com.xue.love.ui.components

import androidx.compose.animation.animateColorAsState
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.NavigationBar
import androidx.compose.material3.NavigationBarItem
import androidx.compose.material3.NavigationBarItemDefaults
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.xue.love.R
import com.xue.love.ui.theme.Pink60
import com.xue.love.ui.theme.Pink80
import com.xue.love.ui.theme.PinkGradientEnd
import com.xue.love.ui.theme.PinkGradientStart

/**
 * 情侣APP精美底部导航栏
 * 包含圆角、渐变、图标动画等浪漫设计元素
 */
@Composable
fun CoupleBottomNavigation(
    selectedTab: Int,
    onTabSelected: (Int) -> Unit,
    modifier: Modifier = Modifier
) {
    val items = listOf(
        BottomNavItem("主页", R.drawable.ic_home_filled),
        BottomNavItem("聊天", R.drawable.ic_chat_filled),
        BottomNavItem("我", R.drawable.ic_profile_filled)
    )

    // 渐变背景
    val gradientBrush = Brush.horizontalGradient(
        colors = listOf(
            PinkGradientStart.copy(alpha = 0.1f),
            PinkGradientEnd.copy(alpha = 0.1f)
        )
    )

    Surface(
        modifier = modifier
            .fillMaxWidth()
            .height(88.dp),
        color = Color.Transparent
    ) {
        Box(
            modifier = Modifier.fillMaxWidth()
        ) {
            // 导航指示器
            NavigationIndicator(
                selectedIndex = selectedTab,
                totalCount = items.size,
                modifier = Modifier
                    .align(Alignment.TopCenter)
                    .padding(top = 4.dp)
            )
            
            // 主导航栏
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp, vertical = 12.dp)
                    .clip(RoundedCornerShape(24.dp))
                    .background(gradientBrush)
                    .background(
                        MaterialTheme.colorScheme.surface.copy(alpha = 0.95f),
                        RoundedCornerShape(24.dp)
                    )
                    .align(Alignment.BottomCenter)
            ) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 8.dp),
                    horizontalArrangement = Arrangement.SpaceEvenly,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    items.forEachIndexed { index, item ->
                        CoupleNavItem(
                            item = item,
                            isSelected = selectedTab == index,
                            onClick = { onTabSelected(index) }
                        )
                    }
                }
            }
        }
    }
}

/**
 * 单个导航项组件，包含动画效果
 */
@Composable
private fun CoupleNavItem(
    item: BottomNavItem,
    isSelected: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    // 动画效果
    val scale by animateFloatAsState(
        targetValue = if (isSelected) 1.2f else 1.0f,
        animationSpec = tween(300),
        label = "scale"
    )
    
    val iconColor by animateColorAsState(
        targetValue = if (isSelected) Pink60 else MaterialTheme.colorScheme.onSurfaceVariant,
        animationSpec = tween(300),
        label = "iconColor"
    )
    
    val textColor by animateColorAsState(
        targetValue = if (isSelected) Pink60 else MaterialTheme.colorScheme.onSurfaceVariant,
        animationSpec = tween(300),
        label = "textColor"
    )

    Surface(
        onClick = onClick,
        modifier = modifier
            .clip(RoundedCornerShape(16.dp))
            .scale(scale),
        color = if (isSelected) Pink80.copy(alpha = 0.2f) else Color.Transparent,
        shape = RoundedCornerShape(16.dp)
    ) {
        Box(
            modifier = Modifier.padding(horizontal = 16.dp, vertical = 8.dp),
            contentAlignment = Alignment.Center
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.Center
            ) {
                Icon(
                    imageVector = ImageVector.vectorResource(item.iconRes),
                    contentDescription = item.title,
                    tint = iconColor,
                    modifier = Modifier.size(if (isSelected) 28.dp else 24.dp)
                )
                
                if (isSelected) {
                    Text(
                        text = item.title,
                        color = textColor,
                        fontSize = 12.sp,
                        fontWeight = FontWeight.Medium,
                        modifier = Modifier.padding(start = 8.dp)
                    )
                }
            }
        }
    }
}

/**
 * 底部导航项数据类
 */
private data class BottomNavItem(
    val title: String,
    val iconRes: Int
)