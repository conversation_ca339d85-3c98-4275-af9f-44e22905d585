package com.xue.love.ui.components

import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.RepeatMode
import androidx.compose.animation.core.animateFloat
import androidx.compose.animation.core.infiniteRepeatable
import androidx.compose.animation.core.rememberInfiniteTransition
import androidx.compose.animation.core.tween
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Path
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.tooling.preview.Preview
import kotlin.math.cos
import kotlin.math.sin
import kotlin.random.Random

/**
 * 爱心粒子效果组件
 * 创建浪漫的飘落爱心动画
 */
@Composable
fun HeartParticleEffect(
    modifier: Modifier = Modifier,
    particleCount: Int = 20,
    colors: List<Color> = listOf(
        Color(0xFFFF6B9D),
        Color(0xFFFFE0E6),
        Color(0xFFC44569),
        Color(0xFFFF8FA3)
    )
) {
    val infiniteTransition = rememberInfiniteTransition(label = "heartParticles")
    
    val animationProgress by infiniteTransition.animateFloat(
        initialValue = 0f,
        targetValue = 1f,
        animationSpec = infiniteRepeatable(
            animation = tween(durationMillis = 8000, easing = LinearEasing),
            repeatMode = RepeatMode.Restart
        ),
        label = "particleAnimation"
    )
    
    val particles = remember {
        generateHeartParticles(particleCount, colors)
    }
    
    Canvas(modifier = modifier.fillMaxSize()) {
        particles.forEach { particle ->
            drawHeartParticle(
                particle = particle,
                progress = animationProgress,
                canvasSize = size
            )
        }
    }
}

/**
 * 爱心粒子数据类
 */
private data class HeartParticle(
    val startX: Float,
    val startY: Float,
    val endY: Float,
    val size: Float,
    val color: Color,
    val speed: Float,
    val swayAmplitude: Float,
    val swayFrequency: Float
)

/**
 * 生成爱心粒子
 */
private fun generateHeartParticles(count: Int, colors: List<Color>): List<HeartParticle> {
    return (0 until count).map {
        HeartParticle(
            startX = Random.nextFloat(),
            startY = -0.1f,
            endY = 1.1f,
            size = Random.nextFloat() * 8f + 4f,
            color = colors[Random.nextInt(colors.size)],
            speed = Random.nextFloat() * 0.5f + 0.5f,
            swayAmplitude = Random.nextFloat() * 50f + 20f,
            swayFrequency = Random.nextFloat() * 2f + 1f
        )
    }
}

/**
 * 绘制单个爱心粒子
 */
private fun DrawScope.drawHeartParticle(
    particle: HeartParticle,
    progress: Float,
    canvasSize: androidx.compose.ui.geometry.Size
) {
    val adjustedProgress = (progress * particle.speed) % 1f
    val currentY = particle.startY + (particle.endY - particle.startY) * adjustedProgress
    
    // 添加左右摆动效果
    val swayOffset = sin(adjustedProgress * particle.swayFrequency * 2 * Math.PI) * particle.swayAmplitude
    val currentX = particle.startX * canvasSize.width + swayOffset.toFloat()
    
    // 透明度随着下落逐渐减少
    val alpha = (1f - adjustedProgress).coerceIn(0f, 1f)
    val particleColor = particle.color.copy(alpha = alpha)
    
    // 绘制爱心形状
    val heartPath = createHeartPath(
        center = Offset(currentX, currentY * canvasSize.height),
        size = particle.size
    )
    
    drawPath(
        path = heartPath,
        color = particleColor
    )
}

/**
 * 创建爱心路径
 */
private fun createHeartPath(center: Offset, size: Float): Path {
    val path = Path()
    val heartWidth = size
    val heartHeight = size * 0.8f
    
    // 爱心的数学公式
    val steps = 100
    var firstPoint = true
    
    for (i in 0..steps) {
        val t = i * 2 * Math.PI / steps
        val x = 16 * sin(t) * sin(t) * sin(t)
        val y = -(13 * cos(t) - 5 * cos(2 * t) - 2 * cos(3 * t) - cos(4 * t))
        
        val scaledX = center.x + (x * heartWidth / 32f).toFloat()
        val scaledY = center.y + (y * heartHeight / 32f).toFloat()
        
        if (firstPoint) {
            path.moveTo(scaledX, scaledY)
            firstPoint = false
        } else {
            path.lineTo(scaledX, scaledY)
        }
    }
    
    path.close()
    return path
}

@Preview(showBackground = true)
@Composable
fun HeartParticleEffectPreview() {
    HeartParticleEffect(
        particleCount = 10
    )
}