package com.xue.love.ui.components

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.tooling.preview.Preview
import com.xue.love.ui.theme.CoupleThemeType

/**
 * 浪漫渐变背景组件
 * 根据主题类型显示不同的渐变效果
 */
@Composable
fun RomanticBackground(
    themeType: CoupleThemeType = CoupleThemeType.SWEET_PINK,
    modifier: Modifier = Modifier,
    content: @Composable () -> Unit = {}
) {
    val gradientColors = when (themeType) {
        CoupleThemeType.SWEET_PINK -> listOf(
            Color(0xFFFF6B9D),
            Color(0xFFFF5E8A),
            Color(0xFFC44569)
        )
        CoupleThemeType.PASSION_RED -> listOf(
            Color(0xFFE91E63),
            Color(0xFFFF5722),
            Color(0xFFD84315)
        )
        CoupleThemeType.MYSTERY_PURPLE -> listOf(
            Color(0xFF9C27B0),
            Color(0xFF8E24AA),
            Color(0xFF673AB7)
        )
        CoupleThemeType.ELEGANT_BLACK -> listOf(
            Color(0xFF424242),
            Color(0xFF616161),
            Color(0xFF795548)
        )
    }
    
    Box(
        modifier = modifier
            .fillMaxSize()
            .background(
                brush = Brush.verticalGradient(
                    colors = gradientColors,
                    startY = 0f,
                    endY = Float.POSITIVE_INFINITY
                )
            )
    ) {
        // 添加粒子效果
        HeartParticleEffect(
            particleCount = 15,
            colors = gradientColors.map { it.copy(alpha = 0.6f) }
        )
        
        // 内容
        content()
    }
}

/**
 * 径向渐变背景
 */
@Composable
fun RomanticRadialBackground(
    themeType: CoupleThemeType = CoupleThemeType.SWEET_PINK,
    modifier: Modifier = Modifier,
    content: @Composable () -> Unit = {}
) {
    val gradientColors = when (themeType) {
        CoupleThemeType.SWEET_PINK -> listOf(
            Color(0xFFFFB3D1),
            Color(0xFFFF6B9D),
            Color(0xFFC44569)
        )
        CoupleThemeType.PASSION_RED -> listOf(
            Color(0xFFF8BBD9),
            Color(0xFFE91E63),
            Color(0xFF9A0036)
        )
        CoupleThemeType.MYSTERY_PURPLE -> listOf(
            Color(0xFFE1BEE7),
            Color(0xFF9C27B0),
            Color(0xFF4A148C)
        )
        CoupleThemeType.ELEGANT_BLACK -> listOf(
            Color(0xFFBDBDBD),
            Color(0xFF424242),
            Color(0xFF212121)
        )
    }
    
    Box(
        modifier = modifier
            .fillMaxSize()
            .background(
                brush = Brush.radialGradient(
                    colors = gradientColors,
                    radius = 800f
                )
            )
    ) {
        content()
    }
}

@Preview(showBackground = true)
@Composable
fun RomanticBackgroundPreview() {
    RomanticBackground(
        themeType = CoupleThemeType.SWEET_PINK
    )
}

@Preview(showBackground = true)
@Composable
fun RomanticRadialBackgroundPreview() {
    RomanticRadialBackground(
        themeType = CoupleThemeType.MYSTERY_PURPLE
    )
}