package com.xue.love.ui.components

import androidx.compose.animation.animateColorAsState
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.interaction.collectIsPressedAsState
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.scale
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.xue.love.R
import com.xue.love.ui.theme.CoupleAppTheme

/**
 * 浪漫风格的渐变卡片
 */
@Composable
fun RomanticCard(
    modifier: Modifier = Modifier,
    colors: List<Color> = listOf(
        Color.White,
        Color.White
    ),
    borderColors: List<Color> = listOf(
        Color(0xFFFF6B9D).copy(alpha = 0.3f),
        Color(0xFFC44569).copy(alpha = 0.3f)
    ),
    onClick: (() -> Unit)? = null,
    content: @Composable () -> Unit
) {
    val interactionSource = remember { MutableInteractionSource() }
    val isPressed by interactionSource.collectIsPressedAsState()
    
    val scale by animateFloatAsState(
        targetValue = if (isPressed && onClick != null) 0.98f else 1f,
        animationSpec = tween(100),
        label = "cardScale"
    )
    
    val elevation by animateFloatAsState(
        targetValue = if (isPressed && onClick != null) 2.dp.value else 8.dp.value,
        animationSpec = tween(100),
        label = "cardElevation"
    )
    
    Card(
        modifier = modifier
            .scale(scale)
            .shadow(
                elevation = elevation.dp,
                shape = RoundedCornerShape(20.dp),
                ambientColor = Color(0xFFFF6B9D).copy(alpha = 0.1f),
                spotColor = Color(0xFFFF6B9D).copy(alpha = 0.1f)
            )
            .then(
                if (onClick != null) {
                    Modifier.clickable(
                        interactionSource = interactionSource,
                        indication = null
                    ) { onClick() }
                } else {
                    Modifier
                }
            ),
        shape = RoundedCornerShape(20.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.Transparent
        )
    ) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .background(
                    brush = Brush.linearGradient(colors),
                    shape = RoundedCornerShape(20.dp)
                )
                .border(
                    width = 1.dp,
                    brush = Brush.linearGradient(borderColors),
                    shape = RoundedCornerShape(20.dp)
                )
                .padding(20.dp)
        ) {
            content()
        }
    }
}

/**
 * 情侣统计卡片
 */
@Composable
fun CoupleStatsCard(
    title: String,
    value: String,
    subtitle: String,
    icon: ImageVector,
    modifier: Modifier = Modifier,
    colors: List<Color> = listOf(
        Color(0xFFFF6B9D),
        Color(0xFFFF8FA3)
    )
) {
    RomanticCard(
        modifier = modifier,
        colors = listOf(
            Color.White,
            Color.White
        ),
        borderColors = colors.map { it.copy(alpha = 0.3f) }
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            Box(
                modifier = Modifier
                    .size(60.dp)
                    .clip(CircleShape)
                    .background(
                        brush = Brush.radialGradient(
                            colors = colors.map { it.copy(alpha = 0.2f) }
                        )
                    ),
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    imageVector = icon,
                    contentDescription = null,
                    tint = colors.first(),
                    modifier = Modifier.size(30.dp)
                )
            }
            
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = title,
                    style = MaterialTheme.typography.bodyMedium,
                    color = Color(0xFF2D1B20).copy(alpha = 0.8f)
                )
                
                Text(
                    text = value,
                    style = MaterialTheme.typography.headlineMedium,
                    fontWeight = FontWeight.Bold,
                    color = colors.first()
                )
                
                Text(
                    text = subtitle,
                    style = MaterialTheme.typography.bodySmall,
                    color = Color(0xFF2D1B20).copy(alpha = 0.6f)
                )
            }
        }
    }
}

/**
 * 纪念日倒计时卡片
 */
@Composable
fun AnniversaryCard(
    title: String,
    daysLeft: Int,
    date: String,
    modifier: Modifier = Modifier,
    onClick: () -> Unit = {}
) {
    RomanticCard(
        modifier = modifier,
        colors = listOf(
            Color.White,
            Color.White
        ),
        borderColors = listOf(
            Color(0xFFE91E63).copy(alpha = 0.3f),
            Color(0xFFFF5722).copy(alpha = 0.3f)
        ),
        onClick = onClick
    ) {
        Column {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column(
                    modifier = Modifier.weight(1f)
                ) {
                    Text(
                        text = title,
                        style = MaterialTheme.typography.headlineSmall,
                        fontWeight = FontWeight.Bold,
                        color = Color(0xFFE91E63)
                    )
                    
                    Text(
                        text = date,
                        style = MaterialTheme.typography.bodyMedium,
                        color = Color(0xFF2D1B20).copy(alpha = 0.8f)
                    )
                }
                
                Box(
                    modifier = Modifier
                        .size(80.dp)
                        .clip(CircleShape)
                        .background(
                            brush = Brush.radialGradient(
                                colors = listOf(
                                    Color(0xFFE91E63).copy(alpha = 0.2f),
                                    Color(0xFFFF5722).copy(alpha = 0.1f)
                                )
                            )
                        ),
                    contentAlignment = Alignment.Center
                ) {
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Text(
                            text = daysLeft.toString(),
                            style = MaterialTheme.typography.headlineLarge,
                            fontWeight = FontWeight.Bold,
                            color = Color(0xFFE91E63)
                        )
                        
                        Text(
                            text = "天",
                            style = MaterialTheme.typography.bodySmall,
                            color = Color(0xFFE91E63)
                        )
                    }
                }
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
fun RomanticCardPreview() {
    CoupleAppTheme {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            CoupleStatsCard(
                title = "在一起",
                value = "365",
                subtitle = "天了呢~",
                icon = ImageVector.vectorResource(R.drawable.ic_heart_filled)
            )
            
            AnniversaryCard(
                title = "我们的纪念日",
                daysLeft = 30,
                date = "2024年8月28日"
            )
            
            RomanticCard {
                Text(
                    text = "这是一个自定义的浪漫卡片",
                    style = MaterialTheme.typography.bodyLarge,
                    color = MaterialTheme.colorScheme.onSurface
                )
            }
        }
    }
}