package com.xue.love.ui.navigation

import androidx.compose.animation.AnimatedContent
import androidx.compose.animation.AnimatedContentTransitionScope
import androidx.compose.animation.core.tween
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideInHorizontally
import androidx.compose.animation.slideOutHorizontally
import androidx.compose.animation.togetherWith
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.lifecycle.viewmodel.compose.viewModel
import com.xue.love.CoupleApplication
import com.xue.love.ui.screens.chat.ChatScreen
import com.xue.love.ui.screens.home.HomeScreen
import com.xue.love.ui.screens.profile.ProfileScreen
import com.xue.love.ui.viewmodel.ViewModelFactory

/**
 * 情侣APP导航管理器
 * 提供流畅的页面切换动画
 */
@Composable
fun CoupleNavigation(
    selectedTab: Int,
    paddingValues: PaddingValues,
    modifier: Modifier = Modifier,
    onTabSelected: (Int) -> Unit = {}
) {
    // 获取Application实例和依赖容器
    val context = LocalContext.current
    val application = context.applicationContext as CoupleApplication
    val viewModelFactory = ViewModelFactory(
        repository = application.container.coupleRepository,
        intimateRecordRepository = application.container.intimateRecordRepository,
        menstrualCycleRepository = application.container.menstrualCycleRepository,
        intimateAppointmentRepository = application.container.intimateAppointmentRepository
    )
    
    // 创建ViewModels
    val homeViewModel = viewModel<com.xue.love.ui.screens.home.HomeViewModel>(factory = viewModelFactory)
    val chatViewModel = viewModel<com.xue.love.ui.screens.chat.ChatViewModel>(factory = viewModelFactory)
    val profileViewModel = viewModel<com.xue.love.ui.screens.profile.ProfileViewModel>(factory = viewModelFactory)
    
    AnimatedContent(
        targetState = selectedTab,
        modifier = modifier.padding(paddingValues),
        transitionSpec = {
            // 根据导航方向选择不同的动画
            val direction = if (targetState > initialState) 1 else -1
            
            slideInHorizontally(
                initialOffsetX = { fullWidth -> direction * fullWidth },
                animationSpec = tween(durationMillis = 300)
            ) + fadeIn(
                animationSpec = tween(durationMillis = 300)
            ) togetherWith slideOutHorizontally(
                targetOffsetX = { fullWidth -> -direction * fullWidth },
                animationSpec = tween(durationMillis = 300)
            ) + fadeOut(
                animationSpec = tween(durationMillis = 300)
            )
        },
        label = "NavigationTransition"
    ) { targetTab ->
        when (targetTab) {
            0 -> {
                HomeScreen(
                    viewModel = homeViewModel,
                    onHeartClick = {
                        // TODO: 实现爱心点击功能
                    },
                    onIntimateGameClick = {
                        // TODO: 实现情趣游戏功能
                    },
                    onPrivateAlbumClick = {
                        // TODO: 实现私密相册功能
                    },
                    onDailyChallengeClick = {
                        // TODO: 实现每日挑战功能
                    },
                    onIntimateRecordClick = {
                        // TODO: 实现亲密记录功能
                    },
                    onMenstrualCycleClick = {
                        // TODO: 实现生理周期功能  
                    }
                )
            }
            1 -> {
                ChatScreen(
                    viewModel = chatViewModel
                )
            }
            2 -> {
                ProfileScreen(
                    viewModel = profileViewModel
                )
            }
        }
    }
}