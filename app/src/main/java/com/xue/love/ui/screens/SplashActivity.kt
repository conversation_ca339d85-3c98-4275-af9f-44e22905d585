package com.xue.love.ui.screens

import android.annotation.SuppressLint
import android.content.Intent
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge

import com.xue.love.MainActivity
import com.xue.love.ui.screens.splash.SplashScreen
import com.xue.love.ui.theme.CoupleAppTheme

/**
 * 启动画面Activity
 * 显示精美的情侣插画、品牌动画和浪漫特效
 */
@SuppressLint("CustomSplashScreen")
class SplashActivity : ComponentActivity() {
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        
        setContent {
            CoupleAppTheme {
                SplashScreen(
                    onSplashFinished = {
                        startMainActivity()
                    }
                )
            }
        }
    }
    
    private fun startMainActivity() {
        val intent = Intent(this, MainActivity::class.java)
        startActivity(intent)
        finish()
        
        // 添加优雅的过渡动画
        overridePendingTransition(android.R.anim.fade_in, android.R.anim.fade_out)
    }
}