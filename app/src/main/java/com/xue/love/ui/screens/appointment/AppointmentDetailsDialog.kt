package com.xue.love.ui.screens.appointment

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import com.xue.love.data.local.entity.IntimateAppointmentEntity

/**
 * 约会详情对话框
 * 显示约会的完整信息和操作选项
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AppointmentDetailsDialog(
    appointment: IntimateAppointmentEntity,
    viewModel: IntimateAppointmentViewModel,
    onDismiss: () -> Unit
) {
    var showDeleteConfirmation by remember { mutableStateOf(false) }
    var showRescheduleDialog by remember { mutableStateOf(false) }
    var showCancelDialog by remember { mutableStateOf(false) }
    
    Dialog(onDismissRequest = onDismiss) {
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .heightIn(max = 650.dp),
            shape = RoundedCornerShape(20.dp),
            colors = CardDefaults.cardColors(
                containerColor = Color.White
            ),
            elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
        ) {
            Column(
                modifier = Modifier.fillMaxWidth()
            ) {
                // 头部区域
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .background(
                            brush = Brush.verticalGradient(
                                colors = listOf(
                                    Color(0xFFE91E63),
                                    Color(0xFFFF5722)
                                )
                            )
                        )
                        .padding(20.dp)
                ) {
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally,
                        verticalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        // 关闭按钮
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.End
                        ) {
                            IconButton(
                                onClick = onDismiss,
                                modifier = Modifier
                                    .size(32.dp)
                                    .background(
                                        color = Color.White.copy(alpha = 0.2f),
                                        shape = CircleShape
                                    )
                            ) {
                                Icon(
                                    imageVector = Icons.Default.Close,
                                    contentDescription = "关闭",
                                    tint = Color.White,
                                    modifier = Modifier.size(18.dp)
                                )
                            }
                        }
                        
                        // 约会图标
                        Box(
                            modifier = Modifier
                                .size(64.dp)
                                .background(
                                    color = Color.White.copy(alpha = 0.2f),
                                    shape = CircleShape
                                ),
                            contentAlignment = Alignment.Center
                        ) {
                            Icon(
                                imageVector = getCategoryIcon(appointment.category),
                                contentDescription = "约会类型",
                                tint = Color.White,
                                modifier = Modifier.size(32.dp)
                            )
                        }
                        
                        // 约会标题
                        Text(
                            text = appointment.title,
                            style = MaterialTheme.typography.headlineSmall,
                            fontWeight = FontWeight.Bold,
                            color = Color.White,
                            textAlign = TextAlign.Center,
                            maxLines = 2,
                            overflow = TextOverflow.Ellipsis
                        )
                        
                        // 状态标签
                        StatusChipLarge(
                            status = appointment.status,
                            viewModel = viewModel
                        )
                    }
                }
                
                // 内容区域
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .verticalScroll(rememberScrollState())
                        .padding(20.dp),
                    verticalArrangement = Arrangement.spacedBy(16.dp)
                ) {
                    // 基本信息
                    InfoSection(
                        title = "约会信息",
                        content = {
                            InfoItem(
                                icon = Icons.Default.Schedule,
                                label = "时间",
                                value = viewModel.formatDateTime(appointment.scheduledDateTime)
                            )
                            
                            if (appointment.location.isNotEmpty()) {
                                InfoItem(
                                    icon = Icons.Default.LocationOn,
                                    label = "地点",
                                    value = appointment.location
                                )
                            }
                            
                            if (appointment.duration > 0) {
                                InfoItem(
                                    icon = Icons.Default.Timer,
                                    label = "预计时长",
                                    value = "${appointment.duration}分钟"
                                )
                            }
                            
                            InfoItem(
                                icon = Icons.Default.Category,
                                label = "类型",
                                value = viewModel.getCategoryDisplayName(appointment.category)
                            )
                            
                            if (appointment.mood.isNotEmpty()) {
                                InfoItem(
                                    icon = Icons.Default.Mood,
                                    label = "心情",
                                    value = "${getMoodEmoji(appointment.mood)} ${viewModel.getMoodDisplayName(appointment.mood)}"
                                )
                            }
                        }
                    )
                    
                    // 描述
                    if (appointment.description.isNotEmpty()) {
                        InfoSection(
                            title = "约会描述",
                            content = {
                                Text(
                                    text = appointment.description,
                                    style = MaterialTheme.typography.bodyMedium,
                                    color = Color(0xFF2D1B20).copy(alpha = 0.8f),
                                    lineHeight = 20.sp
                                )
                            }
                        )
                    }
                    
                    // 创建和响应信息
                    InfoSection(
                        title = "时间记录",
                        content = {
                            InfoItem(
                                icon = Icons.Default.Add,
                                label = "创建时间",
                                value = viewModel.formatDateTime(appointment.createdAt)
                            )
                            
                            appointment.responseDateTime?.let { responseTime ->
                                InfoItem(
                                    icon = Icons.Default.Reply,
                                    label = "响应时间",
                                    value = viewModel.formatDateTime(responseTime)
                                )
                            }
                            
                            appointment.actualDateTime?.let { actualTime ->
                                InfoItem(
                                    icon = Icons.Default.CheckCircle,
                                    label = "实际时间",
                                    value = viewModel.formatDateTime(actualTime)
                                )
                            }
                        }
                    )
                    
                    // 备注
                    if (appointment.notes.isNotEmpty()) {
                        InfoSection(
                            title = "特殊备注",
                            content = {
                                Text(
                                    text = appointment.notes,
                                    style = MaterialTheme.typography.bodyMedium,
                                    color = Color(0xFF2D1B20).copy(alpha = 0.8f),
                                    lineHeight = 20.sp
                                )
                            }
                        )
                    }
                    
                    // 取消原因（如果有）
                    if (appointment.cancellationReason.isNotEmpty()) {
                        InfoSection(
                            title = "取消原因",
                            content = {
                                Text(
                                    text = appointment.cancellationReason,
                                    style = MaterialTheme.typography.bodyMedium,
                                    color = Color(0xFFC62828),
                                    lineHeight = 20.sp
                                )
                            }
                        )
                    }
                    
                    // 操作按钮
                    ActionButtons(
                        appointment = appointment,
                        onAccept = { viewModel.acceptAppointment(appointment.id) },
                        onDecline = { viewModel.declineAppointment(appointment.id) },
                        onComplete = { viewModel.completeAppointment(appointment.id) },
                        onReschedule = { showRescheduleDialog = true },
                        onCancel = { showCancelDialog = true },
                        onDelete = { showDeleteConfirmation = true }
                    )
                }
            }
        }
    }
    
    // 删除确认对话框
    if (showDeleteConfirmation) {
        AlertDialog(
            onDismissRequest = { showDeleteConfirmation = false },
            title = { Text("确认删除") },
            text = { Text("确定要删除这个约会吗？此操作不可撤销。") },
            confirmButton = {
                TextButton(
                    onClick = {
                        viewModel.deleteAppointment(appointment.id)
                        showDeleteConfirmation = false
                        onDismiss()
                    }
                ) {
                    Text("删除", color = Color(0xFFC62828))
                }
            },
            dismissButton = {
                TextButton(onClick = { showDeleteConfirmation = false }) {
                    Text("取消")
                }
            }
        )
    }
}

@Composable
private fun StatusChipLarge(
    status: String,
    viewModel: IntimateAppointmentViewModel
) {
    val (backgroundColor, textColor) = when (status) {
        "pending" -> Color.White.copy(alpha = 0.9f) to Color(0xFFE65100)
        "accepted" -> Color.White.copy(alpha = 0.9f) to Color(0xFF2E7D32)
        "completed" -> Color.White.copy(alpha = 0.9f) to Color(0xFF1565C0)
        "declined" -> Color.White.copy(alpha = 0.9f) to Color(0xFFC62828)
        "cancelled" -> Color.White.copy(alpha = 0.9f) to Color(0xFF7B1FA2)
        else -> Color.White.copy(alpha = 0.9f) to Color(0xFF757575)
    }
    
    Box(
        modifier = Modifier
            .background(
                color = backgroundColor,
                shape = RoundedCornerShape(16.dp)
            )
            .padding(horizontal = 16.dp, vertical = 8.dp)
    ) {
        Text(
            text = viewModel.getStatusDisplayName(status),
            style = MaterialTheme.typography.labelMedium,
            color = textColor,
            fontWeight = FontWeight.Bold
        )
    }
}

@Composable
private fun InfoSection(
    title: String,
    content: @Composable ColumnScope.() -> Unit
) {
    Column(
        verticalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        Text(
            text = title,
            style = MaterialTheme.typography.titleSmall,
            fontWeight = FontWeight.Bold,
            color = Color(0xFF2D1B20)
        )
        
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(
                containerColor = Color(0xFFF8F9FA)
            ),
            shape = RoundedCornerShape(12.dp)
        ) {
            Column(
                modifier = Modifier.padding(16.dp),
                verticalArrangement = Arrangement.spacedBy(12.dp),
                content = content
            )
        }
    }
}

@Composable
private fun InfoItem(
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    label: String,
    value: String
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        Icon(
            imageVector = icon,
            contentDescription = label,
            modifier = Modifier.size(20.dp),
            tint = Color(0xFFE91E63)
        )
        
        Text(
            text = label,
            style = MaterialTheme.typography.bodySmall,
            color = Color(0xFF2D1B20).copy(alpha = 0.6f),
            modifier = Modifier.width(60.dp)
        )
        
        Text(
            text = value,
            style = MaterialTheme.typography.bodyMedium,
            color = Color(0xFF2D1B20),
            modifier = Modifier.weight(1f)
        )
    }
}

@Composable
private fun ActionButtons(
    appointment: IntimateAppointmentEntity,
    onAccept: () -> Unit,
    onDecline: () -> Unit,
    onComplete: () -> Unit,
    onReschedule: () -> Unit,
    onCancel: () -> Unit,
    onDelete: () -> Unit
) {
    Column(
        verticalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        when (appointment.status) {
            "pending" -> {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    OutlinedButton(
                        onClick = onDecline,
                        modifier = Modifier.weight(1f),
                        colors = ButtonDefaults.outlinedButtonColors(
                            contentColor = Color(0xFFC62828)
                        )
                    ) {
                        Icon(
                            imageVector = Icons.Default.Close,
                            contentDescription = "拒绝",
                            modifier = Modifier.size(18.dp)
                        )
                        Spacer(modifier = Modifier.width(4.dp))
                        Text("拒绝")
                    }
                    
                    Button(
                        onClick = onAccept,
                        modifier = Modifier.weight(1f),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = Color(0xFF2E7D32)
                        )
                    ) {
                        Icon(
                            imageVector = Icons.Default.Check,
                            contentDescription = "接受",
                            modifier = Modifier.size(18.dp)
                        )
                        Spacer(modifier = Modifier.width(4.dp))
                        Text("接受")
                    }
                }
                
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    OutlinedButton(
                        onClick = onReschedule,
                        modifier = Modifier.weight(1f)
                    ) {
                        Icon(
                            imageVector = Icons.Default.Schedule,
                            contentDescription = "重新安排",
                            modifier = Modifier.size(18.dp)
                        )
                        Spacer(modifier = Modifier.width(4.dp))
                        Text("重新安排")
                    }
                    
                    OutlinedButton(
                        onClick = onCancel,
                        modifier = Modifier.weight(1f),
                        colors = ButtonDefaults.outlinedButtonColors(
                            contentColor = Color(0xFF7B1FA2)
                        )
                    ) {
                        Icon(
                            imageVector = Icons.Default.Cancel,
                            contentDescription = "取消",
                            modifier = Modifier.size(18.dp)
                        )
                        Spacer(modifier = Modifier.width(4.dp))
                        Text("取消")
                    }
                }
            }
            
            "accepted" -> {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    Button(
                        onClick = onComplete,
                        modifier = Modifier.weight(1f),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = Color(0xFF1565C0)
                        )
                    ) {
                        Icon(
                            imageVector = Icons.Default.CheckCircle,
                            contentDescription = "完成",
                            modifier = Modifier.size(18.dp)
                        )
                        Spacer(modifier = Modifier.width(4.dp))
                        Text("标记完成")
                    }
                    
                    OutlinedButton(
                        onClick = onReschedule,
                        modifier = Modifier.weight(1f)
                    ) {
                        Icon(
                            imageVector = Icons.Default.Schedule,
                            contentDescription = "重新安排",
                            modifier = Modifier.size(18.dp)
                        )
                        Spacer(modifier = Modifier.width(4.dp))
                        Text("重新安排")
                    }
                }
            }
        }
        
        // 删除按钮（所有状态都可以删除）
        OutlinedButton(
            onClick = onDelete,
            modifier = Modifier.fillMaxWidth(),
            colors = ButtonDefaults.outlinedButtonColors(
                contentColor = Color(0xFFC62828)
            )
        ) {
            Icon(
                imageVector = Icons.Default.Delete,
                contentDescription = "删除",
                modifier = Modifier.size(18.dp)
            )
            Spacer(modifier = Modifier.width(4.dp))
            Text("删除约会")
        }
    }
}

private fun getCategoryIcon(category: String): androidx.compose.ui.graphics.vector.ImageVector {
    return when (category) {
        "romantic" -> Icons.Default.Favorite
        "intimate" -> Icons.Default.FavoriteBorder
        "adventure" -> Icons.Default.Explore
        "relaxing" -> Icons.Default.Spa
        "special" -> Icons.Default.Star
        else -> Icons.Default.Event
    }
}

private fun getMoodEmoji(mood: String): String {
    return when (mood) {
        "excited" -> "😍"
        "romantic" -> "💕"
        "playful" -> "😘"
        "passionate" -> "🔥"
        "tender" -> "🥰"
        "adventurous" -> "🎯"
        else -> "😊"
    }
}