package com.xue.love.ui.screens.appointment

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import java.text.SimpleDateFormat
import java.util.*

/**
 * 创建约会对话框
 * 包含完整的约会信息输入表单
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CreateAppointmentDialog(
    onDismiss: () -> Unit,
    onConfirm: (
        title: String,
        dateTime: Long,
        category: String,
        location: String,
        description: String,
        mood: String,
        duration: Int,
        isPrivate: Boolean,
        notes: String
    ) -> Unit
) {
    var title by remember { mutableStateOf("") }
    var selectedDate by remember { mutableStateOf(Calendar.getInstance()) }
    var selectedTime by remember { mutableStateOf(Calendar.getInstance()) }
    var selectedCategory by remember { mutableStateOf("romantic") }
    var location by remember { mutableStateOf("") }
    var description by remember { mutableStateOf("") }
    var selectedMood by remember { mutableStateOf("excited") }
    var duration by remember { mutableStateOf("120") }
    var isPrivate by remember { mutableStateOf(false) }
    var notes by remember { mutableStateOf("") }
    var showDatePicker by remember { mutableStateOf(false) }
    var showTimePicker by remember { mutableStateOf(false) }
    
    val categories = listOf(
        Category("romantic", "浪漫", Color(0xFFE91E63)),
        Category("intimate", "亲密", Color(0xFFFF5722)),
        Category("adventure", "冒险", Color(0xFF9C27B0)),
        Category("relaxing", "休闲", Color(0xFF2196F3)),
        Category("special", "特殊", Color(0xFF673AB7))
    )
    
    val moods = listOf(
        Mood("excited", "兴奋", "😍"),
        Mood("romantic", "浪漫", "💕"),
        Mood("playful", "顽皮", "😘"),
        Mood("passionate", "热情", "🔥"),
        Mood("tender", "温柔", "🥰"),
        Mood("adventurous", "冒险", "🎯")
    )
    
    Dialog(onDismissRequest = onDismiss) {
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .heightIn(max = 600.dp),
            shape = RoundedCornerShape(16.dp),
            colors = CardDefaults.cardColors(
                containerColor = Color.White
            )
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .verticalScroll(rememberScrollState())
                    .padding(24.dp),
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                // 标题
                Text(
                    text = "创建甜蜜约会",
                    style = MaterialTheme.typography.headlineSmall,
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF2D1B20),
                    textAlign = TextAlign.Center,
                    modifier = Modifier.fillMaxWidth()
                )
                
                // 约会标题
                OutlinedTextField(
                    value = title,
                    onValueChange = { title = it },
                    label = { Text("约会标题") },
                    placeholder = { Text("给这次约会起个浪漫的名字吧~") },
                    modifier = Modifier.fillMaxWidth(),
                    shape = RoundedCornerShape(12.dp),
                    colors = OutlinedTextFieldDefaults.colors(
                        focusedBorderColor = Color(0xFFE91E63),
                        focusedLabelColor = Color(0xFFE91E63)
                    ),
                    leadingIcon = {
                        Icon(
                            imageVector = Icons.Default.Title,
                            contentDescription = "标题",
                            tint = Color(0xFFE91E63)
                        )
                    }
                )
                
                // 日期和时间选择
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    // 日期选择
                    OutlinedTextField(
                        value = SimpleDateFormat("MM月dd日", Locale.CHINA).format(selectedDate.time),
                        onValueChange = { },
                        label = { Text("日期") },
                        modifier = Modifier.weight(1f),
                        readOnly = true,
                        shape = RoundedCornerShape(12.dp),
                        colors = OutlinedTextFieldDefaults.colors(
                            focusedBorderColor = Color(0xFFE91E63)
                        ),
                        leadingIcon = {
                            Icon(
                                imageVector = Icons.Default.DateRange,
                                contentDescription = "日期",
                                tint = Color(0xFFE91E63)
                            )
                        },
                        trailingIcon = {
                            IconButton(onClick = { showDatePicker = true }) {
                                Icon(
                                    imageVector = Icons.Default.Edit,
                                    contentDescription = "选择日期",
                                    tint = Color(0xFFE91E63)
                                )
                            }
                        }
                    )
                    
                    // 时间选择
                    OutlinedTextField(
                        value = SimpleDateFormat("HH:mm", Locale.CHINA).format(selectedTime.time),
                        onValueChange = { },
                        label = { Text("时间") },
                        modifier = Modifier.weight(1f),
                        readOnly = true,
                        shape = RoundedCornerShape(12.dp),
                        colors = OutlinedTextFieldDefaults.colors(
                            focusedBorderColor = Color(0xFFE91E63)
                        ),
                        leadingIcon = {
                            Icon(
                                imageVector = Icons.Default.Schedule,
                                contentDescription = "时间",
                                tint = Color(0xFFE91E63)
                            )
                        },
                        trailingIcon = {
                            IconButton(onClick = { showTimePicker = true }) {
                                Icon(
                                    imageVector = Icons.Default.Edit,
                                    contentDescription = "选择时间",
                                    tint = Color(0xFFE91E63)
                                )
                            }
                        }
                    )
                }
                
                // 分类选择
                Column(
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    Text(
                        text = "约会类型",
                        style = MaterialTheme.typography.labelLarge,
                        color = Color(0xFF2D1B20),
                        fontWeight = FontWeight.Medium
                    )
                    
                    LazyRow(
                        horizontalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        items(categories) { category ->
                            CategoryChipSelectable(
                                category = category,
                                isSelected = selectedCategory == category.id,
                                onClick = { selectedCategory = category.id }
                            )
                        }
                    }
                }
                
                // 心情选择
                Column(
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    Text(
                        text = "当前心情",
                        style = MaterialTheme.typography.labelLarge,
                        color = Color(0xFF2D1B20),
                        fontWeight = FontWeight.Medium
                    )
                    
                    LazyRow(
                        horizontalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        items(moods) { mood ->
                            MoodChipSelectable(
                                mood = mood,
                                isSelected = selectedMood == mood.id,
                                onClick = { selectedMood = mood.id }
                            )
                        }
                    }
                }
                
                // 地点
                OutlinedTextField(
                    value = location,
                    onValueChange = { location = it },
                    label = { Text("约会地点") },
                    placeholder = { Text("在哪里度过美好时光呢？") },
                    modifier = Modifier.fillMaxWidth(),
                    shape = RoundedCornerShape(12.dp),
                    colors = OutlinedTextFieldDefaults.colors(
                        focusedBorderColor = Color(0xFFE91E63),
                        focusedLabelColor = Color(0xFFE91E63)
                    ),
                    leadingIcon = {
                        Icon(
                            imageVector = Icons.Default.LocationOn,
                            contentDescription = "地点",
                            tint = Color(0xFFE91E63)
                        )
                    }
                )
                
                // 预计时长
                OutlinedTextField(
                    value = duration,
                    onValueChange = { duration = it },
                    label = { Text("预计时长（分钟）") },
                    placeholder = { Text("120") },
                    modifier = Modifier.fillMaxWidth(),
                    shape = RoundedCornerShape(12.dp),
                    colors = OutlinedTextFieldDefaults.colors(
                        focusedBorderColor = Color(0xFFE91E63),
                        focusedLabelColor = Color(0xFFE91E63)
                    ),
                    leadingIcon = {
                        Icon(
                            imageVector = Icons.Default.Timer,
                            contentDescription = "时长",
                            tint = Color(0xFFE91E63)
                        )
                    },
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number)
                )
                
                // 约会描述
                OutlinedTextField(
                    value = description,
                    onValueChange = { description = it },
                    label = { Text("约会描述") },
                    placeholder = { Text("描述一下这次约会的安排吧~") },
                    modifier = Modifier.fillMaxWidth(),
                    minLines = 3,
                    maxLines = 5,
                    shape = RoundedCornerShape(12.dp),
                    colors = OutlinedTextFieldDefaults.colors(
                        focusedBorderColor = Color(0xFFE91E63),
                        focusedLabelColor = Color(0xFFE91E63)
                    ),
                    leadingIcon = {
                        Icon(
                            imageVector = Icons.Default.Description,
                            contentDescription = "描述",
                            tint = Color(0xFFE91E63)
                        )
                    }
                )
                
                // 私密模式开关
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Column(
                        modifier = Modifier.weight(1f)
                    ) {
                        Text(
                            text = "私密模式",
                            style = MaterialTheme.typography.bodyMedium,
                            fontWeight = FontWeight.Medium,
                            color = Color(0xFF2D1B20)
                        )
                        Text(
                            text = "开启后只有你们两个人能看到",
                            style = MaterialTheme.typography.bodySmall,
                            color = Color(0xFF2D1B20).copy(alpha = 0.6f)
                        )
                    }
                    
                    Switch(
                        checked = isPrivate,
                        onCheckedChange = { isPrivate = it },
                        colors = SwitchDefaults.colors(
                            checkedThumbColor = Color.White,
                            checkedTrackColor = Color(0xFFE91E63),
                            uncheckedThumbColor = Color.White,
                            uncheckedTrackColor = Color(0xFF2D1B20).copy(alpha = 0.3f)
                        )
                    )
                }
                
                // 备注
                OutlinedTextField(
                    value = notes,
                    onValueChange = { notes = it },
                    label = { Text("特殊备注") },
                    placeholder = { Text("有什么特别要注意的吗？") },
                    modifier = Modifier.fillMaxWidth(),
                    minLines = 2,
                    maxLines = 3,
                    shape = RoundedCornerShape(12.dp),
                    colors = OutlinedTextFieldDefaults.colors(
                        focusedBorderColor = Color(0xFFE91E63),
                        focusedLabelColor = Color(0xFFE91E63)
                    ),
                    leadingIcon = {
                        Icon(
                            imageVector = Icons.Default.Note,
                            contentDescription = "备注",
                            tint = Color(0xFFE91E63)
                        )
                    }
                )
                
                // 按钮
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    OutlinedButton(
                        onClick = onDismiss,
                        modifier = Modifier.weight(1f),
                        colors = ButtonDefaults.outlinedButtonColors(
                            contentColor = Color(0xFF757575)
                        )
                    ) {
                        Text("取消")
                    }
                    
                    Button(
                        onClick = {
                            if (title.isNotBlank()) {
                                // 合并日期和时间
                                val calendar = Calendar.getInstance()
                                calendar.set(Calendar.YEAR, selectedDate.get(Calendar.YEAR))
                                calendar.set(Calendar.MONTH, selectedDate.get(Calendar.MONTH))
                                calendar.set(Calendar.DAY_OF_MONTH, selectedDate.get(Calendar.DAY_OF_MONTH))
                                calendar.set(Calendar.HOUR_OF_DAY, selectedTime.get(Calendar.HOUR_OF_DAY))
                                calendar.set(Calendar.MINUTE, selectedTime.get(Calendar.MINUTE))
                                calendar.set(Calendar.SECOND, 0)
                                calendar.set(Calendar.MILLISECOND, 0)
                                
                                onConfirm(
                                    title,
                                    calendar.timeInMillis,
                                    selectedCategory,
                                    location,
                                    description,
                                    selectedMood,
                                    duration.toIntOrNull() ?: 120,
                                    isPrivate,
                                    notes
                                )
                            }
                        },
                        modifier = Modifier.weight(1f),
                        enabled = title.isNotBlank(),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = Color(0xFFE91E63)
                        )
                    ) {
                        Text("创建约会")
                    }
                }
            }
        }
    }
}

@Composable
private fun CategoryChipSelectable(
    category: Category,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    Box(
        modifier = Modifier
            .clickable { onClick() }
            .background(
                color = if (isSelected) category.color.copy(alpha = 0.2f) else Color.Transparent,
                shape = RoundedCornerShape(16.dp)
            )
            .border(
                width = if (isSelected) 2.dp else 1.dp,
                color = if (isSelected) category.color else Color(0xFF2D1B20).copy(alpha = 0.3f),
                shape = RoundedCornerShape(16.dp)
            )
            .padding(horizontal = 16.dp, vertical = 8.dp)
    ) {
        Text(
            text = category.displayName,
            style = MaterialTheme.typography.bodySmall,
            color = if (isSelected) category.color else Color(0xFF2D1B20),
            fontWeight = if (isSelected) FontWeight.Medium else FontWeight.Normal
        )
    }
}

@Composable
private fun MoodChipSelectable(
    mood: Mood,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    Box(
        modifier = Modifier
            .clickable { onClick() }
            .background(
                color = if (isSelected) Color(0xFFE91E63).copy(alpha = 0.1f) else Color.Transparent,
                shape = RoundedCornerShape(20.dp)
            )
            .border(
                width = if (isSelected) 2.dp else 1.dp,
                color = if (isSelected) Color(0xFFE91E63) else Color(0xFF2D1B20).copy(alpha = 0.3f),
                shape = RoundedCornerShape(20.dp)
            )
            .padding(horizontal = 12.dp, vertical = 6.dp)
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(6.dp)
        ) {
            Text(
                text = mood.emoji,
                fontSize = 16.sp
            )
            Text(
                text = mood.displayName,
                style = MaterialTheme.typography.bodySmall,
                color = if (isSelected) Color(0xFFE91E63) else Color(0xFF2D1B20),
                fontWeight = if (isSelected) FontWeight.Medium else FontWeight.Normal
            )
        }
    }
}

// 数据类
private data class Category(
    val id: String,
    val displayName: String,
    val color: Color
)

private data class Mood(
    val id: String,
    val displayName: String,
    val emoji: String
)