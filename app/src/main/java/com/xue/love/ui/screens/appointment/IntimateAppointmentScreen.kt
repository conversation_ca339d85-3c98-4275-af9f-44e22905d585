package com.xue.love.ui.screens.appointment

import androidx.compose.animation.AnimatedContent
import androidx.compose.animation.ExperimentalAnimationApi
import androidx.compose.animation.core.tween
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideInHorizontally
import androidx.compose.animation.slideOutHorizontally
import androidx.compose.animation.with
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.xue.love.R
import com.xue.love.data.local.entity.IntimateAppointmentEntity
import com.xue.love.ui.components.RomanticBackground
import com.xue.love.ui.components.RomanticCard
import com.xue.love.ui.theme.CoupleThemeType

/**
 * 浪漫邀请主界面
 * 支持创建、管理和响应浪漫约会邀请
 */
@OptIn(ExperimentalAnimationApi::class, ExperimentalMaterial3Api::class)
@Composable
fun IntimateAppointmentScreen(
    modifier: Modifier = Modifier,
    viewModel: IntimateAppointmentViewModel,
    onNavigateBack: () -> Unit = {}
) {
    val uiState by viewModel.uiState.collectAsState()
    
    // 错误消息处理
    LaunchedEffect(uiState.error) {
        uiState.error?.let {
            // TODO: 显示错误提示
            viewModel.clearError()
        }
    }
    
    Box(modifier = modifier.fillMaxSize()) {
        RomanticBackground(
            themeType = CoupleThemeType.PASSION_RED
        ) {
            Column(
                modifier = Modifier.fillMaxSize()
            ) {
                // 顶部导航栏
                AppointmentTopBar(
                    onNavigateBack = onNavigateBack,
                    onCreateClick = { viewModel.showCreateDialog() },
                    currentTab = uiState.currentTab,
                    onTabChange = { viewModel.selectTab(it) }
                )
                
                // 搜索和过滤栏
                SearchAndFilterBar(
                    searchQuery = uiState.searchQuery,
                    onSearchChange = { viewModel.updateSearchQuery(it) },
                    selectedCategory = uiState.selectedCategory,
                    onCategoryChange = { viewModel.updateCategoryFilter(it) },
                    selectedMood = uiState.selectedMood,
                    onMoodChange = { viewModel.updateMoodFilter(it) },
                    dateFilter = uiState.dateFilter,
                    onDateFilterChange = { viewModel.updateDateFilter(it) }
                )
                
                // 主要内容区域
                AnimatedContent(
                    targetState = uiState.currentTab,
                    transitionSpec = {
                        slideInHorizontally(
                            initialOffsetX = { if (targetState.ordinal > initialState.ordinal) 300 else -300 },
                            animationSpec = tween(300)
                        ) + fadeIn(animationSpec = tween(300)) with
                                slideOutHorizontally(
                                    targetOffsetX = { if (targetState.ordinal > initialState.ordinal) -300 else 300 },
                                    animationSpec = tween(300)
                                ) + fadeOut(animationSpec = tween(300))
                    },
                    label = "TabTransition"
                ) { tab ->
                    when (tab) {
                        AppointmentTab.ALL -> AppointmentListContent(
                            appointments = uiState.appointments,
                            viewModel = viewModel,
                            isLoading = uiState.isLoading
                        )
                        AppointmentTab.PENDING -> AppointmentListContent(
                            appointments = uiState.pendingAppointments,
                            viewModel = viewModel,
                            isLoading = uiState.isLoading,
                            showActions = true
                        )
                        AppointmentTab.ACCEPTED -> AppointmentListContent(
                            appointments = uiState.acceptedAppointments,
                            viewModel = viewModel,
                            isLoading = uiState.isLoading
                        )
                        AppointmentTab.COMPLETED -> AppointmentListContent(
                            appointments = uiState.completedAppointments,
                            viewModel = viewModel,
                            isLoading = uiState.isLoading,
                            showRating = true
                        )
                        AppointmentTab.UPCOMING -> UpcomingAppointmentsContent(
                            appointments = uiState.upcomingAppointments,
                            viewModel = viewModel,
                            isLoading = uiState.isLoading
                        )
                    }
                }
            }
        }
        
        // 对话框
        if (uiState.showCreateDialog) {
            CreateAppointmentDialog(
                onDismiss = { viewModel.hideCreateDialog() },
                onConfirm = { title, dateTime, category, location, description, mood, duration, isPrivate, notes ->
                    viewModel.createAppointment(
                        title = title,
                        scheduledDateTime = dateTime,
                        category = category,
                        location = location,
                        description = description,
                        mood = mood,
                        duration = duration,
                        isPrivate = isPrivate,
                        notes = notes
                    )
                }
            )
        }
        
        if (uiState.showDetailsDialog && uiState.selectedAppointment != null) {
            AppointmentDetailsDialog(
                appointment = uiState.selectedAppointment!!,
                viewModel = viewModel,
                onDismiss = { viewModel.hideDetailsDialog() }
            )
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun AppointmentTopBar(
    onNavigateBack: () -> Unit,
    onCreateClick: () -> Unit,
    currentTab: AppointmentTab,
    onTabChange: (AppointmentTab) -> Unit
) {
    RomanticCard(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column {
            // 标题栏
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                IconButton(onClick = onNavigateBack) {
                    Icon(
                        imageVector = Icons.Default.ArrowBack,
                        contentDescription = "返回",
                        tint = Color(0xFF2D1B20)
                    )
                }
                
                Text(
                    text = "甜蜜约会",
                    style = MaterialTheme.typography.headlineSmall,
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF2D1B20)
                )
                
                IconButton(onClick = onCreateClick) {
                    Icon(
                        imageVector = Icons.Default.Add,
                        contentDescription = "创建约会",
                        tint = Color(0xFFE91E63)
                    )
                }
            }
            
            // 标签栏
            ScrollableTabRow(
                selectedTabIndex = currentTab.ordinal,
                containerColor = Color.Transparent,
                contentColor = Color(0xFF2D1B20),
                indicator = { tabPositions ->
                    if (tabPositions.isNotEmpty() && currentTab.ordinal < tabPositions.size) {
                        Box(
                            modifier = Modifier
                                .offset(x = tabPositions[currentTab.ordinal].left)
                                .width(tabPositions[currentTab.ordinal].width)
                                .height(3.dp)
                                .background(
                                    brush = Brush.horizontalGradient(
                                        colors = listOf(
                                            Color(0xFFE91E63),
                                            Color(0xFFFF5722)
                                        )
                                    ),
                                    shape = RoundedCornerShape(1.5.dp)
                                )
                        )
                    }
                }
            ) {
                AppointmentTab.values().forEach { tab ->
                    Tab(
                        selected = currentTab == tab,
                        onClick = { onTabChange(tab) },
                        text = {
                            Text(
                                text = getTabDisplayName(tab),
                                fontSize = 14.sp,
                                fontWeight = if (currentTab == tab) FontWeight.Bold else FontWeight.Normal
                            )
                        }
                    )
                }
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun SearchAndFilterBar(
    searchQuery: String,
    onSearchChange: (String) -> Unit,
    selectedCategory: String,
    onCategoryChange: (String) -> Unit,
    selectedMood: String,
    onMoodChange: (String) -> Unit,
    dateFilter: DateFilter,
    onDateFilterChange: (DateFilter) -> Unit
) {
    RomanticCard(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp, vertical = 8.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            // 搜索框
            OutlinedTextField(
                value = searchQuery,
                onValueChange = onSearchChange,
                modifier = Modifier.fillMaxWidth(),
                placeholder = { Text("搜索约会...") },
                leadingIcon = {
                    Icon(
                        imageVector = Icons.Default.Search,
                        contentDescription = "搜索"
                    )
                },
                trailingIcon = {
                    if (searchQuery.isNotEmpty()) {
                        IconButton(onClick = { onSearchChange("") }) {
                            Icon(
                                imageVector = Icons.Default.Clear,
                                contentDescription = "清除"
                            )
                        }
                    }
                },
                shape = RoundedCornerShape(12.dp),
                colors = OutlinedTextFieldDefaults.colors(
                    focusedBorderColor = Color(0xFFE91E63),
                    unfocusedBorderColor = Color(0xFF2D1B20).copy(alpha = 0.3f)
                )
            )
            
            // 过滤选项
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                // 分类过滤
                FilterChip(
                    selected = selectedCategory != "all",
                    onClick = { 
                        onCategoryChange(if (selectedCategory == "all") "romantic" else "all")
                    },
                    label = { Text("分类") },
                    leadingIcon = {
                        Icon(
                            imageVector = Icons.Default.Category,
                            contentDescription = "分类",
                            modifier = Modifier.size(18.dp)
                        )
                    }
                )
                
                // 心情过滤
                FilterChip(
                    selected = selectedMood != "all",
                    onClick = { 
                        onMoodChange(if (selectedMood == "all") "excited" else "all")
                    },
                    label = { Text("心情") },
                    leadingIcon = {
                        Icon(
                            imageVector = Icons.Default.Mood,
                            contentDescription = "心情",
                            modifier = Modifier.size(18.dp)
                        )
                    }
                )
                
                // 时间过滤
                FilterChip(
                    selected = dateFilter != DateFilter.ALL,
                    onClick = { 
                        val nextFilter = when (dateFilter) {
                            DateFilter.ALL -> DateFilter.TODAY
                            DateFilter.TODAY -> DateFilter.THIS_WEEK
                            DateFilter.THIS_WEEK -> DateFilter.THIS_MONTH
                            DateFilter.THIS_MONTH -> DateFilter.ALL
                        }
                        onDateFilterChange(nextFilter)
                    },
                    label = { Text(getDateFilterDisplayName(dateFilter)) },
                    leadingIcon = {
                        Icon(
                            imageVector = Icons.Default.DateRange,
                            contentDescription = "时间",
                            modifier = Modifier.size(18.dp)
                        )
                    }
                )
            }
        }
    }
}

@Composable
private fun AppointmentListContent(
    appointments: List<IntimateAppointmentEntity>,
    viewModel: IntimateAppointmentViewModel,
    isLoading: Boolean,
    showActions: Boolean = false,
    showRating: Boolean = false
) {
    if (isLoading) {
        Box(
            modifier = Modifier.fillMaxSize(),
            contentAlignment = Alignment.Center
        ) {
            CircularProgressIndicator(
                color = Color(0xFFE91E63)
            )
        }
        return
    }
    
    if (appointments.isEmpty()) {
        Box(
            modifier = Modifier.fillMaxSize(),
            contentAlignment = Alignment.Center
        ) {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                Icon(
                    imageVector = Icons.Default.EventNote,
                    contentDescription = "暂无约会",
                    modifier = Modifier.size(64.dp),
                    tint = Color(0xFF2D1B20).copy(alpha = 0.5f)
                )
                Text(
                    text = "暂无约会记录",
                    style = MaterialTheme.typography.bodyLarge,
                    color = Color(0xFF2D1B20).copy(alpha = 0.7f)
                )
                Text(
                    text = "点击右上角的 + 号创建你们的第一个甜蜜约会吧~",
                    style = MaterialTheme.typography.bodySmall,
                    color = Color(0xFF2D1B20).copy(alpha = 0.5f),
                    textAlign = TextAlign.Center
                )
            }
        }
        return
    }
    
    LazyColumn(
        modifier = Modifier.fillMaxSize(),
        contentPadding = PaddingValues(16.dp),
        verticalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        items(appointments) { appointment ->
            AppointmentCard(
                appointment = appointment,
                onCardClick = { viewModel.showDetailsDialog(appointment) },
                onAccept = if (showActions) {{ viewModel.acceptAppointment(appointment.id) }} else null,
                onDecline = if (showActions) {{ viewModel.declineAppointment(appointment.id) }} else null,
                showRating = showRating,
                viewModel = viewModel
            )
        }
    }
}

@Composable
private fun UpcomingAppointmentsContent(
    appointments: List<IntimateAppointmentEntity>,
    viewModel: IntimateAppointmentViewModel,
    isLoading: Boolean
) {
    AppointmentListContent(
        appointments = appointments,
        viewModel = viewModel,
        isLoading = isLoading
    )
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun AppointmentCard(
    appointment: IntimateAppointmentEntity,
    onCardClick: () -> Unit,
    onAccept: (() -> Unit)? = null,
    onDecline: (() -> Unit)? = null,
    showRating: Boolean = false,
    viewModel: IntimateAppointmentViewModel
) {
    RomanticCard(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onCardClick() }
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            // 标题和状态
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = appointment.title,
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF2D1B20),
                    modifier = Modifier.weight(1f),
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )
                
                StatusChip(
                    status = appointment.status,
                    viewModel = viewModel
                )
            }
            
            // 时间和地点
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = Icons.Default.Schedule,
                    contentDescription = "时间",
                    modifier = Modifier.size(16.dp),
                    tint = Color(0xFF2D1B20).copy(alpha = 0.6f)
                )
                Spacer(modifier = Modifier.width(4.dp))
                Text(
                    text = viewModel.formatDateTime(appointment.scheduledDateTime),
                    style = MaterialTheme.typography.bodySmall,
                    color = Color(0xFF2D1B20).copy(alpha = 0.8f)
                )
            }
            
            if (appointment.location.isNotEmpty()) {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        imageVector = Icons.Default.LocationOn,
                        contentDescription = "地点",
                        modifier = Modifier.size(16.dp),
                        tint = Color(0xFF2D1B20).copy(alpha = 0.6f)
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text(
                        text = appointment.location,
                        style = MaterialTheme.typography.bodySmall,
                        color = Color(0xFF2D1B20).copy(alpha = 0.8f),
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis
                    )
                }
            }
            
            // 分类和心情标签
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                CategoryChip(
                    category = appointment.category,
                    viewModel = viewModel
                )
                
                if (appointment.mood.isNotEmpty()) {
                    MoodChip(
                        mood = appointment.mood,
                        viewModel = viewModel
                    )
                }
            }
            
            // 描述
            if (appointment.description.isNotEmpty()) {
                Text(
                    text = appointment.description,
                    style = MaterialTheme.typography.bodySmall,
                    color = Color(0xFF2D1B20).copy(alpha = 0.7f),
                    maxLines = 2,
                    overflow = TextOverflow.Ellipsis
                )
            }
            
            // 操作按钮
            if (onAccept != null && onDecline != null) {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    OutlinedButton(
                        onClick = onDecline,
                        modifier = Modifier.weight(1f),
                        colors = ButtonDefaults.outlinedButtonColors(
                            contentColor = Color(0xFF757575)
                        )
                    ) {
                        Text("拒绝")
                    }
                    
                    Button(
                        onClick = onAccept,
                        modifier = Modifier.weight(1f),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = Color(0xFFE91E63)
                        )
                    ) {
                        Text("接受")
                    }
                }
            }
        }
    }
}

@Composable
private fun StatusChip(
    status: String,
    viewModel: IntimateAppointmentViewModel
) {
    val (backgroundColor, textColor) = when (status) {
        "pending" -> Color(0xFFFFF3E0) to Color(0xFFE65100)
        "accepted" -> Color(0xFFE8F5E8) to Color(0xFF2E7D32)
        "completed" -> Color(0xFFE3F2FD) to Color(0xFF1565C0)
        "declined" -> Color(0xFFFFEBEE) to Color(0xFFC62828)
        "cancelled" -> Color(0xFFF3E5F5) to Color(0xFF7B1FA2)
        else -> Color(0xFFF5F5F5) to Color(0xFF757575)
    }
    
    Box(
        modifier = Modifier
            .background(
                color = backgroundColor,
                shape = RoundedCornerShape(12.dp)
            )
            .padding(horizontal = 8.dp, vertical = 4.dp)
    ) {
        Text(
            text = viewModel.getStatusDisplayName(status),
            style = MaterialTheme.typography.labelSmall,
            color = textColor,
            fontWeight = FontWeight.Medium
        )
    }
}

@Composable
private fun CategoryChip(
    category: String,
    viewModel: IntimateAppointmentViewModel
) {
    Box(
        modifier = Modifier
            .background(
                brush = Brush.horizontalGradient(
                    colors = listOf(
                        Color(0xFFE91E63).copy(alpha = 0.1f),
                        Color(0xFFFF5722).copy(alpha = 0.1f)
                    )
                ),
                shape = RoundedCornerShape(8.dp)
            )
            .padding(horizontal = 6.dp, vertical = 3.dp)
    ) {
        Text(
            text = viewModel.getCategoryDisplayName(category),
            style = MaterialTheme.typography.labelSmall,
            color = Color(0xFFE91E63),
            fontSize = 10.sp
        )
    }
}

@Composable
private fun MoodChip(
    mood: String,
    viewModel: IntimateAppointmentViewModel
) {
    Box(
        modifier = Modifier
            .background(
                brush = Brush.horizontalGradient(
                    colors = listOf(
                        Color(0xFF9C27B0).copy(alpha = 0.1f),
                        Color(0xFF673AB7).copy(alpha = 0.1f)
                    )
                ),
                shape = RoundedCornerShape(8.dp)
            )
            .padding(horizontal = 6.dp, vertical = 3.dp)
    ) {
        Text(
            text = viewModel.getMoodDisplayName(mood),
            style = MaterialTheme.typography.labelSmall,
            color = Color(0xFF9C27B0),
            fontSize = 10.sp
        )
    }
}

private fun getTabDisplayName(tab: AppointmentTab): String {
    return when (tab) {
        AppointmentTab.ALL -> "全部"
        AppointmentTab.PENDING -> "待确认"
        AppointmentTab.ACCEPTED -> "已接受"
        AppointmentTab.COMPLETED -> "已完成"
        AppointmentTab.UPCOMING -> "即将到来"
    }
}

private fun getDateFilterDisplayName(filter: DateFilter): String {
    return when (filter) {
        DateFilter.ALL -> "全部时间"
        DateFilter.TODAY -> "今天"
        DateFilter.THIS_WEEK -> "本周"
        DateFilter.THIS_MONTH -> "本月"
    }
}