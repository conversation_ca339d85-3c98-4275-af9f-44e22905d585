package com.xue.love.ui.screens.appointment

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.xue.love.data.local.entity.IntimateAppointmentEntity
import com.xue.love.data.repository.IntimateAppointmentRepository
import com.xue.love.data.repository.AppointmentInsights
import com.xue.love.data.repository.AppointmentRecommendation
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.launch
import java.text.SimpleDateFormat
import java.util.*

data class AppointmentUiState(
    val appointments: List<IntimateAppointmentEntity> = emptyList(),
    val pendingAppointments: List<IntimateAppointmentEntity> = emptyList(),
    val acceptedAppointments: List<IntimateAppointmentEntity> = emptyList(),
    val completedAppointments: List<IntimateAppointmentEntity> = emptyList(),
    val upcomingAppointments: List<IntimateAppointmentEntity> = emptyList(),
    val selectedAppointment: IntimateAppointmentEntity? = null,
    val insights: AppointmentInsights? = null,
    val recommendations: List<AppointmentRecommendation> = emptyList(),
    val isLoading: Boolean = false,
    val error: String? = null,
    val currentTab: AppointmentTab = AppointmentTab.ALL,
    val showCreateDialog: Boolean = false,
    val showEditDialog: Boolean = false,
    val showDetailsDialog: Boolean = false,
    val searchQuery: String = "",
    val selectedCategory: String = "all",
    val selectedMood: String = "all",
    val dateFilter: DateFilter = DateFilter.ALL
)

enum class AppointmentTab {
    ALL, PENDING, ACCEPTED, COMPLETED, UPCOMING
}

enum class DateFilter {
    ALL, TODAY, THIS_WEEK, THIS_MONTH
}

class IntimateAppointmentViewModel(
    private val repository: IntimateAppointmentRepository
) : ViewModel() {
    
    private val _uiState = MutableStateFlow(AppointmentUiState())
    val uiState: StateFlow<AppointmentUiState> = _uiState.asStateFlow()
    
    private val currentUserId = "current_user" // TODO: 从登录状态获取
    private val partnerId = "partner_user" // TODO: 从用户设置获取
    
    init {
        loadAppointmentData()
    }
    
    private fun loadAppointmentData() {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(isLoading = true)
                
                // 组合多个数据流
                combine(
                    repository.getAppointmentsByUser(currentUserId),
                    repository.getPendingAppointments(currentUserId),
                    repository.getAcceptedAppointments(currentUserId),
                    repository.getCompletedAppointments(currentUserId),
                    repository.getUpcomingAppointments(currentUserId)
                ) { all, pending, accepted, completed, upcoming ->
                    _uiState.value = _uiState.value.copy(
                        appointments = all,
                        pendingAppointments = pending,
                        acceptedAppointments = accepted,
                        completedAppointments = completed,
                        upcomingAppointments = upcoming,
                        isLoading = false
                    )
                }.collect { }
                
                // 加载统计数据
                loadInsights()
                loadRecommendations()
                
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = "加载数据失败: ${e.message}"
                )
            }
        }
    }
    
    private suspend fun loadInsights() {
        try {
            val insights = repository.getAppointmentInsights(currentUserId)
            _uiState.value = _uiState.value.copy(insights = insights)
        } catch (e: Exception) {
            // 记录错误但不影响主要功能
        }
    }
    
    private suspend fun loadRecommendations() {
        try {
            val recommendations = repository.getAppointmentRecommendations(currentUserId)
            _uiState.value = _uiState.value.copy(recommendations = recommendations)
        } catch (e: Exception) {
            // 记录错误但不影响主要功能
        }
    }
    
    // 约会管理操作
    fun createAppointment(
        title: String,
        scheduledDateTime: Long,
        category: String = "romantic",
        location: String = "",
        description: String = "",
        mood: String = "excited",
        duration: Int = 120,
        isPrivate: Boolean = false,
        notes: String = ""
    ) {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(isLoading = true)
                
                val appointment = repository.createNewAppointment(
                    userId = currentUserId,
                    partnerId = partnerId,
                    title = title,
                    scheduledDateTime = scheduledDateTime,
                    category = category,
                    location = location,
                    description = description
                )
                
                // 更新额外信息
                val updatedAppointment = appointment.copy(
                    mood = mood,
                    duration = duration,
                    isPrivate = isPrivate,
                    notes = notes
                )
                repository.updateAppointment(updatedAppointment)
                
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    showCreateDialog = false
                )
                
                loadAppointmentData()
                
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = "创建约会失败: ${e.message}"
                )
            }
        }
    }
    
    fun acceptAppointment(appointmentId: String) {
        viewModelScope.launch {
            try {
                repository.acceptAppointment(appointmentId)
                loadAppointmentData()
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "接受约会失败: ${e.message}"
                )
            }
        }
    }
    
    fun declineAppointment(appointmentId: String, reason: String = "") {
        viewModelScope.launch {
            try {
                repository.declineAppointment(appointmentId, reason)
                loadAppointmentData()
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "拒绝约会失败: ${e.message}"
                )
            }
        }
    }
    
    fun completeAppointment(appointmentId: String) {
        viewModelScope.launch {
            try {
                repository.completeAppointment(appointmentId)
                loadAppointmentData()
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "完成约会失败: ${e.message}"
                )
            }
        }
    }
    
    fun rescheduleAppointment(appointmentId: String, newDateTime: Long) {
        viewModelScope.launch {
            try {
                repository.rescheduleAppointment(appointmentId, newDateTime)
                loadAppointmentData()
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "重新安排约会失败: ${e.message}"
                )
            }
        }
    }
    
    fun cancelAppointment(appointmentId: String, reason: String = "") {
        viewModelScope.launch {
            try {
                repository.cancelAppointment(appointmentId, reason)
                loadAppointmentData()
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "取消约会失败: ${e.message}"
                )
            }
        }
    }
    
    fun deleteAppointment(appointmentId: String) {
        viewModelScope.launch {
            try {
                repository.deleteAppointmentById(appointmentId)
                loadAppointmentData()
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "删除约会失败: ${e.message}"
                )
            }
        }
    }
    
    // UI状态管理
    fun selectTab(tab: AppointmentTab) {
        _uiState.value = _uiState.value.copy(currentTab = tab)
    }
    
    fun showCreateDialog() {
        _uiState.value = _uiState.value.copy(showCreateDialog = true)
    }
    
    fun hideCreateDialog() {
        _uiState.value = _uiState.value.copy(showCreateDialog = false)
    }
    
    fun showEditDialog(appointment: IntimateAppointmentEntity) {
        _uiState.value = _uiState.value.copy(
            selectedAppointment = appointment,
            showEditDialog = true
        )
    }
    
    fun hideEditDialog() {
        _uiState.value = _uiState.value.copy(
            selectedAppointment = null,
            showEditDialog = false
        )
    }
    
    fun showDetailsDialog(appointment: IntimateAppointmentEntity) {
        _uiState.value = _uiState.value.copy(
            selectedAppointment = appointment,
            showDetailsDialog = true
        )
    }
    
    fun hideDetailsDialog() {
        _uiState.value = _uiState.value.copy(
            selectedAppointment = null,
            showDetailsDialog = false
        )
    }
    
    fun updateSearchQuery(query: String) {
        _uiState.value = _uiState.value.copy(searchQuery = query)
        performSearch()
    }
    
    fun updateCategoryFilter(category: String) {
        _uiState.value = _uiState.value.copy(selectedCategory = category)
        applyFilters()
    }
    
    fun updateMoodFilter(mood: String) {
        _uiState.value = _uiState.value.copy(selectedMood = mood)
        applyFilters()
    }
    
    fun updateDateFilter(dateFilter: DateFilter) {
        _uiState.value = _uiState.value.copy(dateFilter = dateFilter)
        applyFilters()
    }
    
    fun clearError() {
        _uiState.value = _uiState.value.copy(error = null)
    }
    
    // 搜索和过滤
    private fun performSearch() {
        val query = _uiState.value.searchQuery
        if (query.isBlank()) {
            loadAppointmentData()
            return
        }
        
        viewModelScope.launch {
            repository.searchAppointments(currentUserId, query).collect { results ->
                _uiState.value = _uiState.value.copy(appointments = results)
            }
        }
    }
    
    private fun applyFilters() {
        viewModelScope.launch {
            try {
                val category = _uiState.value.selectedCategory
                val mood = _uiState.value.selectedMood
                val dateFilter = _uiState.value.dateFilter
                
                var filteredFlow = repository.getAppointmentsByUser(currentUserId)
                
                // 应用分类过滤
                if (category != "all") {
                    filteredFlow = repository.getAppointmentsByCategory(currentUserId, category)
                }
                
                // 应用心情过滤
                if (mood != "all") {
                    filteredFlow = repository.getAppointmentsByMood(currentUserId, mood)
                }
                
                // 收集结果并应用时间过滤
                filteredFlow.collect { appointments ->
                    val timeFilteredAppointments = when (dateFilter) {
                        DateFilter.ALL -> appointments
                        DateFilter.TODAY -> filterByToday(appointments)
                        DateFilter.THIS_WEEK -> filterByThisWeek(appointments)
                        DateFilter.THIS_MONTH -> filterByThisMonth(appointments)
                    }
                    
                    _uiState.value = _uiState.value.copy(appointments = timeFilteredAppointments)
                }
                
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "过滤数据失败: ${e.message}"
                )
            }
        }
    }
    
    // 时间过滤辅助方法
    private fun filterByToday(appointments: List<IntimateAppointmentEntity>): List<IntimateAppointmentEntity> {
        val today = Calendar.getInstance()
        today.set(Calendar.HOUR_OF_DAY, 0)
        today.set(Calendar.MINUTE, 0)
        today.set(Calendar.SECOND, 0)
        today.set(Calendar.MILLISECOND, 0)
        val todayStart = today.timeInMillis
        
        today.add(Calendar.DAY_OF_MONTH, 1)
        val todayEnd = today.timeInMillis
        
        return appointments.filter { appointment ->
            appointment.scheduledDateTime in todayStart until todayEnd
        }
    }
    
    private fun filterByThisWeek(appointments: List<IntimateAppointmentEntity>): List<IntimateAppointmentEntity> {
        val calendar = Calendar.getInstance()
        calendar.set(Calendar.DAY_OF_WEEK, calendar.firstDayOfWeek)
        calendar.set(Calendar.HOUR_OF_DAY, 0)
        calendar.set(Calendar.MINUTE, 0)
        calendar.set(Calendar.SECOND, 0)
        calendar.set(Calendar.MILLISECOND, 0)
        val weekStart = calendar.timeInMillis
        
        calendar.add(Calendar.WEEK_OF_YEAR, 1)
        val weekEnd = calendar.timeInMillis
        
        return appointments.filter { appointment ->
            appointment.scheduledDateTime in weekStart until weekEnd
        }
    }
    
    private fun filterByThisMonth(appointments: List<IntimateAppointmentEntity>): List<IntimateAppointmentEntity> {
        val calendar = Calendar.getInstance()
        calendar.set(Calendar.DAY_OF_MONTH, 1)
        calendar.set(Calendar.HOUR_OF_DAY, 0)
        calendar.set(Calendar.MINUTE, 0)
        calendar.set(Calendar.SECOND, 0)
        calendar.set(Calendar.MILLISECOND, 0)
        val monthStart = calendar.timeInMillis
        
        calendar.add(Calendar.MONTH, 1)
        val monthEnd = calendar.timeInMillis
        
        return appointments.filter { appointment ->
            appointment.scheduledDateTime in monthStart until monthEnd
        }
    }
    
    // 获取当前显示的约会列表
    fun getCurrentAppointments(): List<IntimateAppointmentEntity> {
        return when (_uiState.value.currentTab) {
            AppointmentTab.ALL -> _uiState.value.appointments
            AppointmentTab.PENDING -> _uiState.value.pendingAppointments
            AppointmentTab.ACCEPTED -> _uiState.value.acceptedAppointments
            AppointmentTab.COMPLETED -> _uiState.value.completedAppointments
            AppointmentTab.UPCOMING -> _uiState.value.upcomingAppointments
        }
    }
    
    // 数据统计方法
    fun getAppointmentCountByStatus(status: String): Int {
        return when (status) {
            "pending" -> _uiState.value.pendingAppointments.size
            "accepted" -> _uiState.value.acceptedAppointments.size
            "completed" -> _uiState.value.completedAppointments.size
            else -> _uiState.value.appointments.size
        }
    }
    
    fun getCompletionRate(): Float {
        val total = _uiState.value.appointments.size
        if (total == 0) return 0f
        val completed = _uiState.value.completedAppointments.size
        return (completed.toFloat() / total * 100f)
    }
    
    // 格式化方法
    fun formatDateTime(timestamp: Long): String {
        val sdf = SimpleDateFormat("yyyy年MM月dd日 HH:mm", Locale.CHINA)
        return sdf.format(Date(timestamp))
    }
    
    fun formatDate(timestamp: Long): String {
        val sdf = SimpleDateFormat("MM月dd日", Locale.CHINA)
        return sdf.format(Date(timestamp))
    }
    
    fun formatTime(timestamp: Long): String {
        val sdf = SimpleDateFormat("HH:mm", Locale.CHINA)
        return sdf.format(Date(timestamp))
    }
    
    fun getStatusDisplayName(status: String): String {
        return when (status) {
            "pending" -> "待确认"
            "accepted" -> "已接受"
            "declined" -> "已拒绝"
            "completed" -> "已完成"
            "cancelled" -> "已取消"
            else -> status
        }
    }
    
    fun getCategoryDisplayName(category: String): String {
        return when (category) {
            "romantic" -> "浪漫"
            "intimate" -> "亲密"
            "adventure" -> "冒险"
            "relaxing" -> "休闲"
            "special" -> "特殊"
            else -> category
        }
    }
    
    fun getMoodDisplayName(mood: String): String {
        return when (mood) {
            "excited" -> "兴奋"
            "romantic" -> "浪漫"
            "playful" -> "顽皮"
            "passionate" -> "热情"
            "tender" -> "温柔"
            "adventurous" -> "冒险"
            else -> mood
        }
    }
}