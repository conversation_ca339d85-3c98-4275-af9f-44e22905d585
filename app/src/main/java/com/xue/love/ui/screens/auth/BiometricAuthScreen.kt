package com.xue.love.ui.screens.auth
import androidx.compose.animation.core.Animatable
import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.RepeatMode
import androidx.compose.animation.core.infiniteRepeatable
import androidx.compose.animation.core.tween
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp

import com.xue.love.R
import com.xue.love.ui.theme.Pink60
import com.xue.love.ui.theme.PinkGradientEnd
import com.xue.love.ui.theme.PinkGradientStart

/**
 * 生物识别认证界面
 * 提供指纹、面部识别等多种认证方式的美观界面
 */
@Composable
fun BiometricAuthScreen(
    onAuthSuccess: () -> Unit,
    onAuthError: (String) -> Unit,
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current
    
    // 渐变背景
    val gradientBrush = Brush.radialGradient(
        colors = listOf(
            PinkGradientStart.copy(alpha = 0.2f),
            PinkGradientEnd.copy(alpha = 0.1f),
            Color.Transparent
        ),
        radius = 600f
    )
    
    Box(
        modifier = modifier
            .fillMaxSize()
            .background(gradientBrush)
            .background(MaterialTheme.colorScheme.background),
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center,
            modifier = Modifier.padding(32.dp)
        ) {
            // 生物识别图标动画
            BiometricIcon()
            
            Spacer(modifier = Modifier.height(32.dp))
            
            // 标题
            Text(
                text = "安全验证",
                style = MaterialTheme.typography.headlineMedium.copy(
                    fontSize = 28.sp,
                    fontWeight = FontWeight.Bold
                ),
                color = MaterialTheme.colorScheme.onBackground,
                textAlign = TextAlign.Center
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // 描述文字
            Text(
                text = "请使用生物识别验证身份\n保护你们的私密空间",
                style = MaterialTheme.typography.bodyLarge,
                color = MaterialTheme.colorScheme.onBackground.copy(alpha = 0.7f),
                textAlign = TextAlign.Center,
                lineHeight = 24.sp
            )
            
            Spacer(modifier = Modifier.height(48.dp))
            
            // 认证按钮
            Button(
                onClick = {
                    // 简化版本，直接调用成功回调
                    onAuthSuccess()
                },
                modifier = Modifier
                    .size(width = 200.dp, height = 56.dp),
                colors = ButtonDefaults.buttonColors(
                    containerColor = Pink60
                ),
                shape = CircleShape
            ) {
                Icon(
                    imageVector = ImageVector.vectorResource(R.drawable.ic_heart_filled),
                    contentDescription = null,
                    modifier = Modifier.size(20.dp),
                    tint = Color.White
                )
                Text(
                    text = "开始验证",
                    color = Color.White,
                    fontWeight = FontWeight.Medium,
                    modifier = Modifier.padding(start = 8.dp)
                )
            }
        }
    }
}

/**
 * 生物识别图标动画组件
 */
@Composable
private fun BiometricIcon(
    modifier: Modifier = Modifier
) {
    val pulseAnimation = remember { Animatable(1f) }
    
    LaunchedEffect(Unit) {
        pulseAnimation.animateTo(
            targetValue = 1.2f,
            animationSpec = infiniteRepeatable(
                animation = tween(1500, easing = LinearEasing),
                repeatMode = RepeatMode.Reverse
            )
        )
    }
    
    Box(
        modifier = modifier.size(120.dp),
        contentAlignment = Alignment.Center
    ) {
        // 脉冲背景圆圈
        Canvas(modifier = Modifier.fillMaxSize()) {
            val center = Offset(size.width / 2, size.height / 2)
            val baseRadius = size.minDimension / 3
            
            // 绘制多层脉冲圆圈
            for (i in 1..3) {
                val radius = baseRadius * pulseAnimation.value * (1 + i * 0.2f)
                val alpha = (1f - (i * 0.3f)) * 0.3f
                
                drawCircle(
                    brush = Brush.radialGradient(
                        colors = listOf(
                            Pink60.copy(alpha = alpha),
                            Color.Transparent
                        ),
                        radius = radius
                    ),
                    radius = radius,
                    center = center
                )
            }
        }
        
        // 主图标
        Box(
            modifier = Modifier
                .size(80.dp)
                .clip(CircleShape)
                .background(
                    Brush.radialGradient(
                        colors = listOf(PinkGradientStart, PinkGradientEnd)
                    )
                ),
            contentAlignment = Alignment.Center
        ) {
            Icon(
                imageVector = ImageVector.vectorResource(R.drawable.ic_heart_filled),
                contentDescription = "生物识别",
                tint = Color.White,
                modifier = Modifier.size(40.dp)
            )
        }
    }
}

