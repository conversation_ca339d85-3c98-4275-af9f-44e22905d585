package com.xue.love.ui.screens.chat

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.xue.love.data.local.entity.ChatMessageEntity
import com.xue.love.data.repository.CoupleRepository
// 暂时禁用Hilt
// import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
// 暂时禁用Hilt
// import javax.inject.Inject

data class ChatUiState(
    val messages: List<ChatMessage> = emptyList(),
    val messageText: String = "",
    val showSpecialEffects: Boolean = false,
    val showEmojiPanel: Boolean = false,
    val isLoading: Boolean = false,
    val unreadCount: Int = 0
)

// 暂时禁用Hilt
// @HiltViewModel
class ChatViewModel(
    private val repository: CoupleRepository
) : ViewModel() {
    
    private val currentUserId = "current_user" // TODO: 从登录状态获取
    private val partnerId = "partner_user" // TODO: 从登录状态获取
    
    private val _uiState = MutableStateFlow(ChatUiState())
    val uiState: StateFlow<ChatUiState> = _uiState.asStateFlow()
    
    init {
        loadMessages()
        observeUnreadCount()
    }
    
    private fun loadMessages() {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(isLoading = true)
                
                repository.getConversationFlow(currentUserId, partnerId)
                    .collect { entities ->
                        val messages = entities.map { entity ->
                            ChatMessage(
                                id = entity.id,
                                content = entity.content,
                                isFromMe = entity.senderId == currentUserId,
                                timestamp = entity.timestamp,
                                type = MessageType.valueOf(entity.messageType),
                                hasSpecialEffect = entity.hasSpecialEffect
                            )
                        }
                        
                        _uiState.value = _uiState.value.copy(
                            messages = messages,
                            isLoading = false
                        )
                    }
                
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(isLoading = false)
                // TODO: 处理错误
            }
        }
    }
    
    private fun observeUnreadCount() {
        viewModelScope.launch {
            repository.getUnreadMessageCountFlow(currentUserId)
                .collect { count ->
                    _uiState.value = _uiState.value.copy(unreadCount = count)
                }
        }
    }
    
    fun updateMessageText(text: String) {
        _uiState.value = _uiState.value.copy(messageText = text)
    }
    
    fun sendMessage(content: String) {
        if (content.isBlank()) return
        
        viewModelScope.launch {
            try {
                val messageEntity = ChatMessageEntity(
                    id = generateMessageId(),
                    senderId = currentUserId,
                    receiverId = partnerId,
                    content = content,
                    messageType = MessageType.TEXT.name,
                    timestamp = System.currentTimeMillis()
                )
                
                repository.insertMessage(messageEntity)
                repository.incrementMessageCount(currentUserId)
                repository.incrementInteractionCount(currentUserId)
                
                _uiState.value = _uiState.value.copy(messageText = "")
                
                // TODO: 发送消息到服务器
                
            } catch (e: Exception) {
                // TODO: 处理错误
            }
        }
    }
    
    fun sendHeart() {
        viewModelScope.launch {
            try {
                val heartMessage = ChatMessageEntity(
                    id = generateMessageId(),
                    senderId = currentUserId,
                    receiverId = partnerId,
                    content = "",
                    messageType = MessageType.HEART.name,
                    timestamp = System.currentTimeMillis(),
                    hasSpecialEffect = true
                )
                
                repository.insertMessage(heartMessage)
                repository.incrementHeartCount(currentUserId)
                repository.incrementInteractionCount(currentUserId)
                
                _uiState.value = _uiState.value.copy(showSpecialEffects = true)
                
                // TODO: 发送爱心到服务器
                
            } catch (e: Exception) {
                // TODO: 处理错误
            }
        }
    }
    
    fun sendVibration() {
        viewModelScope.launch {
            try {
                val vibrationMessage = ChatMessageEntity(
                    id = generateMessageId(),
                    senderId = currentUserId,
                    receiverId = partnerId,
                    content = "",
                    messageType = MessageType.VIBRATION.name,
                    timestamp = System.currentTimeMillis()
                )
                
                repository.insertMessage(vibrationMessage)
                repository.incrementInteractionCount(currentUserId)
                
                // TODO: 发送震动消息到服务器
                
            } catch (e: Exception) {
                // TODO: 处理错误
            }
        }
    }
    
    fun sendKiss() {
        viewModelScope.launch {
            try {
                val kissMessage = ChatMessageEntity(
                    id = generateMessageId(),
                    senderId = currentUserId,
                    receiverId = partnerId,
                    content = "",
                    messageType = MessageType.KISS.name,
                    timestamp = System.currentTimeMillis()
                )
                
                repository.insertMessage(kissMessage)
                repository.incrementInteractionCount(currentUserId)
                
                // TODO: 发送飞吻到服务器
                
            } catch (e: Exception) {
                // TODO: 处理错误
            }
        }
    }
    
    fun toggleEmojiPanel() {
        _uiState.value = _uiState.value.copy(
            showEmojiPanel = !_uiState.value.showEmojiPanel
        )
    }
    
    fun hideSpecialEffects() {
        _uiState.value = _uiState.value.copy(showSpecialEffects = false)
    }
    
    fun addEmoji(emoji: String) {
        val currentText = _uiState.value.messageText
        _uiState.value = _uiState.value.copy(
            messageText = currentText + emoji
        )
    }
    
    fun markMessageAsRead(messageId: String) {
        viewModelScope.launch {
            try {
                repository.markMessageAsRead(messageId)
            } catch (e: Exception) {
                // TODO: 处理错误
            }
        }
    }
    
    fun markAllAsRead() {
        viewModelScope.launch {
            try {
                // TODO: 实现标记所有消息为已读
            } catch (e: Exception) {
                // TODO: 处理错误
            }
        }
    }
    
    private fun generateMessageId(): String {
        return "${System.currentTimeMillis()}_${currentUserId}"
    }
}