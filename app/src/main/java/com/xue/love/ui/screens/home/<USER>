package com.xue.love.ui.screens.home

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.xue.love.data.repository.CoupleRepository
// 暂时禁用Hilt
// import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
// 暂时禁用Hilt
// import javax.inject.Inject

data class HomeUiState(
    val isHeartLiked: Boolean = false,
    val intimacyLevel: Float = 85f,
    val daysInRelationship: Int = 365,
    val totalInteractions: String = "1,234",
    val todayMoments: String = "3",
    val isLoading: Boolean = false,
    val errorMessage: String? = null,
    // 导航状态
    val shouldNavigateToIntimateRecord: Boolean = false,
    val shouldNavigateToMenstrualCycle: Boolean = false,
    val shouldNavigateToIntimateAppointment: Boolean = false,
    // 问候和标题
    val greeting: String = "❤️ 早安，我的爱人",
    val subtitle: String = "今天也要充满爱意哦~",
    // 统计数据
    val todayInteractions: Int = 12,
    val anniversaryDaysLeft: Int = 30,
    val anniversaryDate: String = "2024年8月30日",
    val chatProgress: Float = 0.75f,
    val intimateGameProgress: Float = 0.60f,
    val sweetnessIndex: Float = 0.85f
)

// 暂时禁用Hilt
// @HiltViewModel
class HomeViewModel(
    private val coupleRepository: CoupleRepository
) : ViewModel() {
    
    private val _uiState = MutableStateFlow(HomeUiState())
    val uiState: StateFlow<HomeUiState> = _uiState.asStateFlow()
    
    init {
        loadHomeData()
    }
    
    fun toggleHeartLike() {
        _uiState.value = _uiState.value.copy(
            isHeartLiked = !_uiState.value.isHeartLiked
        )
    }
    
    // 导航处理方法
    fun onNavigationHandled() {
        _uiState.value = _uiState.value.copy(shouldNavigateToIntimateRecord = false)
    }
    
    fun onMenstrualNavigationHandled() {
        _uiState.value = _uiState.value.copy(shouldNavigateToMenstrualCycle = false)
    }
    
    fun onAppointmentNavigationHandled() {
        _uiState.value = _uiState.value.copy(shouldNavigateToIntimateAppointment = false)
    }
    
    // 功能点击方法
    fun onMenstrualCycleClick() {
        _uiState.value = _uiState.value.copy(shouldNavigateToMenstrualCycle = true)
    }
    
    fun onIntimateRecordClick() {
        _uiState.value = _uiState.value.copy(shouldNavigateToIntimateRecord = true)
    }
    
    fun onIntimateAppointmentClick() {
        _uiState.value = _uiState.value.copy(shouldNavigateToIntimateAppointment = true)
    }
    
    fun onIntimateGameClick() {
        // TODO: 实现情趣游戏导航
    }
    
    fun onPrivateAlbumClick() {
        // TODO: 实现私密相册导航
    }
    
    fun onDailyChallengeClick() {
        // TODO: 实现每日挑战导航
    }
    
    // 心跳振动功能
    fun sendHeartVibration() {
        // TODO: 实现心跳振动发送
    }
    
    private fun loadHomeData() {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true)
            
            try {
                // TODO: 连接实际的Repository数据
                // 暂时使用模拟数据
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    intimacyLevel = 85f,
                    daysInRelationship = 365,
                    totalInteractions = "1,234",
                    todayMoments = "3"
                )
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    errorMessage = e.message
                )
            }
        }
    }
    
    fun refreshData() {
        loadHomeData()
    }
}