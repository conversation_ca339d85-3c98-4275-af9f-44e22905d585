package com.xue.love.ui.screens.home

import android.content.Intent
import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.RepeatMode
import androidx.compose.animation.core.animateFloat
import androidx.compose.animation.core.infiniteRepeatable
import androidx.compose.animation.core.rememberInfiniteTransition
import androidx.compose.animation.core.tween
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
// 暂时移除Hilt依赖
// import androidx.hilt.navigation.compose.hiltViewModel
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.xue.love.R
import com.xue.love.ui.components.AnniversaryCard
import com.xue.love.ui.components.CoupleStatsCard
import com.xue.love.ui.components.HeartButton
import com.xue.love.ui.components.IntimacyMeter
import com.xue.love.ui.components.IntimateCardButton
import com.xue.love.ui.components.RomanticBackground
import com.xue.love.ui.components.RomanticCard
import com.xue.love.ui.components.RomanticLinearProgress
import com.xue.love.ui.screens.intimate.IntimateRecordActivity
import com.xue.love.ui.screens.menstrual.MenstrualCycleActivity
import com.xue.love.ui.screens.appointment.IntimateAppointmentActivity
import com.xue.love.ui.theme.CoupleAppTheme
import com.xue.love.ui.theme.CoupleThemeType

/**
 * 情趣化主页界面
 * 显示亲密关系状态、互动功能、每日挑战等
 */
@Composable
fun HomeScreen(
    modifier: Modifier = Modifier,
    viewModel: HomeViewModel, // 移除默认值，需要外部传入
    onHeartClick: () -> Unit = {},
    onIntimateGameClick: () -> Unit = {},
    onPrivateAlbumClick: () -> Unit = {},
    onDailyChallengeClick: () -> Unit = {},
    onIntimateRecordClick: () -> Unit = {},
    onMenstrualCycleClick: () -> Unit = {}
) {
    val uiState by viewModel.uiState.collectAsState()
    val context = LocalContext.current
    
    // 处理导航
    LaunchedEffect(uiState.shouldNavigateToIntimateRecord) {
        if (uiState.shouldNavigateToIntimateRecord) {
            val intent = Intent(context, IntimateRecordActivity::class.java)
            context.startActivity(intent)
            viewModel.onNavigationHandled()
        }
    }
    
    LaunchedEffect(uiState.shouldNavigateToMenstrualCycle) {
        if (uiState.shouldNavigateToMenstrualCycle) {
            val intent = Intent(context, MenstrualCycleActivity::class.java)
            context.startActivity(intent)
            viewModel.onMenstrualNavigationHandled()
        }
    }
    
    LaunchedEffect(uiState.shouldNavigateToIntimateAppointment) {
        if (uiState.shouldNavigateToIntimateAppointment) {
            val intent = Intent(context, IntimateAppointmentActivity::class.java)
            context.startActivity(intent)
            viewModel.onAppointmentNavigationHandled()
        }
    }
    
    // 脉冲动画
    val infiniteTransition = rememberInfiniteTransition(label = "heartPulse")
    val heartPulse by infiniteTransition.animateFloat(
        initialValue = 1f,
        targetValue = 1.1f,
        animationSpec = infiniteRepeatable(
            animation = tween(durationMillis = 1500, easing = LinearEasing),
            repeatMode = RepeatMode.Reverse
        ),
        label = "heartPulse"
    )
    
    Box(modifier = modifier.fillMaxSize()) {
        // 浪漫背景
        RomanticBackground(
            themeType = CoupleThemeType.SWEET_PINK
        ) {
            LazyColumn(
                modifier = Modifier.fillMaxSize(),
                contentPadding = PaddingValues(16.dp),
                verticalArrangement = Arrangement.spacedBy(20.dp)
            ) {
                item {
                    // 顶部问候区域
                    RomanticCard(
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Column(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalAlignment = Alignment.CenterHorizontally,
                            verticalArrangement = Arrangement.spacedBy(16.dp)
                        ) {
                            Text(
                                text = uiState.greeting,
                                style = MaterialTheme.typography.headlineMedium,
                                fontWeight = FontWeight.Bold,
                                color = Color(0xFF2D1B20),
                                textAlign = TextAlign.Center
                            )
                            
                            Text(
                                text = uiState.subtitle,
                                style = MaterialTheme.typography.bodyMedium,
                                color = Color(0xFF2D1B20).copy(alpha = 0.8f),
                                textAlign = TextAlign.Center
                            )
                        }
                    }
                }
                
                item {
                    // 亲密度仪表盘
                    RomanticCard(
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Column(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalAlignment = Alignment.CenterHorizontally,
                            verticalArrangement = Arrangement.spacedBy(16.dp)
                        ) {
                            Text(
                                text = "今日亲密度",
                                style = MaterialTheme.typography.headlineSmall,
                                fontWeight = FontWeight.Bold,
                                color = Color(0xFF2D1B20),
                                textAlign = TextAlign.Center
                            )
                            
                            IntimacyMeter(
                                intimacyLevel = uiState.intimacyLevel,
                                colors = listOf(
                                    Color(0xFFE91E63),
                                    Color(0xFFFF5722)
                                )
                            )
                            
                            Text(
                                text = "你们今天的互动很棒哦！继续保持这份甜蜜~",
                                style = MaterialTheme.typography.bodySmall,
                                color = Color(0xFF2D1B20).copy(alpha = 0.7f),
                                textAlign = TextAlign.Center
                            )
                        }
                    }
                }
                
                item {
                    // 互动按钮区域
                    RomanticCard {
                        Column(
                            verticalArrangement = Arrangement.spacedBy(16.dp)
                        ) {
                            Text(
                                text = "快速互动",
                                style = MaterialTheme.typography.headlineSmall,
                                fontWeight = FontWeight.Bold,
                                color = Color(0xFF2D1B20),
                                modifier = Modifier.fillMaxWidth(),
                                textAlign = TextAlign.Center
                            )
                            
                            Row(
                                modifier = Modifier.fillMaxWidth(),
                                horizontalArrangement = Arrangement.SpaceEvenly,
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Column(
                                    horizontalAlignment = Alignment.CenterHorizontally,
                                    verticalArrangement = Arrangement.spacedBy(8.dp)
                                ) {
                                    HeartButton(
                                        onClick = {
                                            viewModel.sendHeartVibration()
                                            onHeartClick()
                                        },
                                        isLiked = uiState.isHeartLiked,
                                        size = 80,
                                        modifier = Modifier.scale(heartPulse)
                                    )
                                    
                                    Text(
                                        text = "发送爱心",
                                        style = MaterialTheme.typography.bodySmall,
                                        color = Color(0xFF2D1B20).copy(alpha = 0.8f)
                                    )
                                }
                                
                                Column(
                                    horizontalAlignment = Alignment.CenterHorizontally,
                                    verticalArrangement = Arrangement.spacedBy(8.dp)
                                ) {
                                    Box(
                                        modifier = Modifier
                                            .size(80.dp)
                                            .clip(CircleShape),
                                        contentAlignment = Alignment.Center
                                    ) {
                                        Icon(
                                            imageVector = ImageVector.vectorResource(R.drawable.ic_vibration),
                                            contentDescription = "震动消息",
                                            modifier = Modifier.size(40.dp),
                                            tint = Color(0xFFFF5722)
                                        )
                                    }
                                    
                                    Text(
                                        text = "震动消息",
                                        style = MaterialTheme.typography.bodySmall,
                                        color = Color(0xFF2D1B20).copy(alpha = 0.8f)
                                    )
                                }
                                
                                Column(
                                    horizontalAlignment = Alignment.CenterHorizontally,
                                    verticalArrangement = Arrangement.spacedBy(8.dp)
                                ) {
                                    Box(
                                        modifier = Modifier
                                            .size(80.dp)
                                            .clip(CircleShape),
                                        contentAlignment = Alignment.Center
                                    ) {
                                        Icon(
                                            imageVector = ImageVector.vectorResource(R.drawable.ic_kiss),
                                            contentDescription = "飞吻",
                                            modifier = Modifier.size(40.dp),
                                            tint = Color(0xFF9C27B0)
                                        )
                                    }
                                    
                                    Text(
                                        text = "飞吻",
                                        style = MaterialTheme.typography.bodySmall,
                                        color = Color(0xFF2D1B20).copy(alpha = 0.8f)
                                    )
                                }
                            }
                        }
                    }
                }
                
                item {
                    // 统计数据
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.spacedBy(12.dp)
                    ) {
                        CoupleStatsCard(
                            title = "在一起",
                            value = uiState.daysInRelationship.toString(),
                            subtitle = "天了呢~",
                            icon = ImageVector.vectorResource(R.drawable.ic_heart_filled),
                            modifier = Modifier.weight(1f),
                            colors = listOf(
                                Color(0xFFFF6B9D),
                                Color(0xFFFF8FA3)
                            )
                        )
                        
                        CoupleStatsCard(
                            title = "互动次数",
                            value = uiState.totalInteractions.toString(),
                            subtitle = "今天 +${uiState.todayInteractions}",
                            icon = ImageVector.vectorResource(R.drawable.ic_couple_love),
                            modifier = Modifier.weight(1f),
                            colors = listOf(
                                Color(0xFF9C27B0),
                                Color(0xFFBA68C8)
                            )
                        )
                    }
                }
                
                item {
                    // 纪念日倒计时
                    AnniversaryCard(
                        title = "我们的纪念日",
                        daysLeft = uiState.anniversaryDaysLeft,
                        date = uiState.anniversaryDate,
                        onClick = { /* 处理点击 */ }
                    )
                }
                
                item {
                    // 今日进度
                    RomanticCard {
                        Column(
                            verticalArrangement = Arrangement.spacedBy(16.dp)
                        ) {
                            Text(
                                text = "今日互动进度",
                                style = MaterialTheme.typography.headlineSmall,
                                fontWeight = FontWeight.Bold,
                                color = Color(0xFF2D1B20)
                            )
                            
                            RomanticLinearProgress(
                                progress = uiState.chatProgress,
                                label = "聊天互动",
                                colors = listOf(
                                    Color(0xFFFF6B9D),
                                    Color(0xFFC44569)
                                )
                            )
                            
                            RomanticLinearProgress(
                                progress = uiState.intimateGameProgress,
                                label = "情趣挑战",
                                colors = listOf(
                                    Color(0xFF9C27B0),
                                    Color(0xFF673AB7)
                                )
                            )
                            
                            RomanticLinearProgress(
                                progress = uiState.sweetnessIndex,
                                label = "甜蜜指数",
                                colors = listOf(
                                    Color(0xFFE91E63),
                                    Color(0xFFFF5722)
                                )
                            )
                        }
                    }
                }
                
                item {
                    // 功能快捷入口
                    Column(
                        verticalArrangement = Arrangement.spacedBy(12.dp)
                    ) {
                        IntimateCardButton(
                            title = "生理周期",
                            subtitle = "追踪你的健康周期",
                            icon = ImageVector.vectorResource(R.drawable.ic_heart_filled),
                            onClick = {
                                viewModel.onMenstrualCycleClick()
                                onMenstrualCycleClick()
                            },
                            colors = listOf(
                                Color(0xFFE91E63),
                                Color(0xFFFF6B9D)
                            )
                        )
                        
                        IntimateCardButton(
                            title = "亲密记录",
                            subtitle = "记录我们的美好时光",
                            icon = ImageVector.vectorResource(R.drawable.ic_heart_filled),
                            onClick = {
                                viewModel.onIntimateRecordClick()
                                onIntimateRecordClick()
                            },
                            colors = listOf(
                                Color(0xFF9C27B0),
                                Color(0xFFBA68C8)
                            )
                        )
                        
                        IntimateCardButton(
                            title = "甜蜜约会",
                            subtitle = "创建浪漫的约会邀请",
                            icon = ImageVector.vectorResource(R.drawable.ic_heart_filled),
                            onClick = {
                                viewModel.onIntimateAppointmentClick()
                            },
                            colors = listOf(
                                Color(0xFFFF6B9D),
                                Color(0xFFFF8FA3)
                            )
                        )
                        
                        IntimateCardButton(
                            title = "情趣游戏",
                            subtitle = "一起玩有趣的互动游戏",
                            icon = ImageVector.vectorResource(R.drawable.ic_heart_filled),
                            onClick = {
                                viewModel.onIntimateGameClick()
                                onIntimateGameClick()
                            },
                            colors = listOf(
                                Color(0xFFE91E63),
                                Color(0xFFFF5722)
                            )
                        )
                        
                        IntimateCardButton(
                            title = "私密相册",
                            subtitle = "查看我们的甜蜜回忆",
                            icon = ImageVector.vectorResource(R.drawable.ic_intimate_message),
                            onClick = {
                                viewModel.onPrivateAlbumClick()
                                onPrivateAlbumClick()
                            },
                            colors = listOf(
                                Color(0xFF9C27B0),
                                Color(0xFF673AB7)
                            )
                        )
                        
                        IntimateCardButton(
                            title = "每日挑战",
                            subtitle = "完成今天的情侣任务",
                            icon = ImageVector.vectorResource(R.drawable.ic_vibration),
                            onClick = {
                                viewModel.onDailyChallengeClick()
                                onDailyChallengeClick()
                            },
                            colors = listOf(
                                Color(0xFFFF6B9D),
                                Color(0xFFFF8FA3)
                            )
                        )
                    }
                }
                
                item {
                    Spacer(modifier = Modifier.height(20.dp))
                }
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
fun HomeScreenPreview() {
    CoupleAppTheme {
        // Preview暂时隐藏，需要ViewModel实例
        // HomeScreen()
        Text("Home Screen Preview")
    }
}