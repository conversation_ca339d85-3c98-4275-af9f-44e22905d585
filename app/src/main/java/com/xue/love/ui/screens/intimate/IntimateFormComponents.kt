package com.xue.love.ui.screens.intimate

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp

// 姿势选择器
@Composable
fun PositionSelector(
    selectedPositions: List<String>,
    onPositionsChanged: (List<String>) -> Unit
) {
    val availablePositions = listOf(
        "传统式", "女上位", "侧卧式", "后进式", "坐位式",
        "站立式", "抱抱式", "交缠式", "其他"
    )
    
    Column {
        Text(
            text = "姿势 (可多选)",
            style = MaterialTheme.typography.bodyMedium,
            color = Color(0xFF666666),
            modifier = Modifier.padding(bottom = 8.dp)
        )
        
        LazyRow(
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            items(availablePositions) { position ->
                FilterChip(
                    onClick = {
                        val newPositions = if (selectedPositions.contains(position)) {
                            selectedPositions - position
                        } else {
                            selectedPositions + position
                        }
                        onPositionsChanged(newPositions)
                    },
                    label = { Text(position) },
                    selected = selectedPositions.contains(position),
                    colors = FilterChipDefaults.filterChipColors(
                        selectedContainerColor = Color(0xFF9C27B0),
                        selectedLabelColor = Color.White
                    )
                )
            }
        }
    }
}

// 高潮选择器
@Composable
fun ClimaxSelector(
    selected: String,
    onSelectionChanged: (String) -> Unit
) {
    Column {
        Text(
            text = "高潮情况",
            style = MaterialTheme.typography.bodyMedium,
            color = Color(0xFF666666),
            modifier = Modifier.padding(bottom = 8.dp)
        )
        
        Row(
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            listOf(
                "both" to "双方满足",
                "me" to "我满足",
                "partner" to "TA满足",
                "none" to "无"
            ).forEach { (value, label) ->
                FilterChip(
                    onClick = { onSelectionChanged(value) },
                    label = { Text(label) },
                    selected = selected == value,
                    colors = FilterChipDefaults.filterChipColors(
                        selectedContainerColor = Color(0xFF9C27B0),
                        selectedLabelColor = Color.White
                    )
                )
            }
        }
    }
}

// 心情选择器
@Composable
fun MoodSelector(
    selected: String,
    onSelectionChanged: (String) -> Unit
) {
    val moods = listOf(
        "开心" to "😊", "兴奋" to "😍", "浪漫" to "🥰", "激情" to "🔥",
        "温柔" to "😌", "放松" to "😇", "甜蜜" to "🍯", "满足" to "😋",
        "疲惫" to "😴", "一般" to "😐"
    )
    
    Column {
        Text(
            text = "心情",
            style = MaterialTheme.typography.bodyMedium,
            color = Color(0xFF666666),
            modifier = Modifier.padding(bottom = 8.dp)
        )
        
        LazyRow(
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            items(moods) { (mood, emoji) ->
                FilterChip(
                    onClick = { onSelectionChanged(mood) },
                    label = { 
                        Text("$emoji $mood")
                    },
                    selected = selected == mood,
                    colors = FilterChipDefaults.filterChipColors(
                        selectedContainerColor = Color(0xFF9C27B0),
                        selectedLabelColor = Color.White
                    )
                )
            }
        }
    }
}

// 满意度评分区域
@Composable
fun SatisfactionRatingSection(
    mySatisfaction: Int,
    partnerSatisfaction: Int,
    onMyRatingChanged: (Int) -> Unit,
    onPartnerRatingChanged: (Int) -> Unit
) {
    Column(
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        RatingSection(
            title = "我的满意度",
            value = mySatisfaction,
            onValueChanged = onMyRatingChanged,
            color = Color(0xFF9C27B0)
        )
        
        RatingSection(
            title = "TA的满意度",
            value = partnerSatisfaction,
            onValueChanged = onPartnerRatingChanged,
            color = Color(0xFFE91E63)
        )
    }
}

// 评分组件
@Composable
fun RatingSection(
    title: String,
    value: Int,
    onValueChanged: (Int) -> Unit,
    color: Color = Color(0xFF9C27B0)
) {
    Column {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = title,
                style = MaterialTheme.typography.bodyMedium,
                color = Color(0xFF666666)
            )
            
            Text(
                text = "$value/10",
                style = MaterialTheme.typography.bodyMedium,
                fontWeight = FontWeight.Bold,
                color = color
            )
        }
        
        Spacer(modifier = Modifier.height(8.dp))
        
        // 星级评分
        Row(
            horizontalArrangement = Arrangement.spacedBy(4.dp)
        ) {
            repeat(10) { index ->
                Icon(
                    imageVector = if (index < value) Icons.Default.Star else Icons.Default.StarBorder,
                    contentDescription = null,
                    tint = if (index < value) color else Color(0xFFCCCCCC),
                    modifier = Modifier
                        .size(24.dp)
                        .clickable { onValueChanged(index + 1) }
                )
            }
        }
    }
}

// 特殊元素选择器
@Composable
fun SpecialElementsSelector(
    selectedElements: List<String>,
    onElementsChanged: (List<String>) -> Unit
) {
    val specialElements = listOf(
        "蜡烛", "音乐", "香薰", "按摩油", "情趣用品",
        "角色扮演", "户外", "旅行", "酒店", "其他"
    )
    
    Column {
        Text(
            text = "特殊元素 (可多选)",
            style = MaterialTheme.typography.bodyMedium,
            color = Color(0xFF666666),
            modifier = Modifier.padding(bottom = 8.dp)
        )
        
        LazyRow(
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            items(specialElements) { element ->
                FilterChip(
                    onClick = {
                        val newElements = if (selectedElements.contains(element)) {
                            selectedElements - element
                        } else {
                            selectedElements + element
                        }
                        onElementsChanged(newElements)
                    },
                    label = { Text(element) },
                    selected = selectedElements.contains(element),
                    colors = FilterChipDefaults.filterChipColors(
                        selectedContainerColor = Color(0xFF9C27B0),
                        selectedLabelColor = Color.White
                    )
                )
            }
        }
    }
}

// 生理周期选择器
@Composable
fun MenstrualPhaseSelector(
    selected: String,
    onSelectionChanged: (String) -> Unit
) {
    Column {
        Text(
            text = "生理周期阶段",
            style = MaterialTheme.typography.bodyMedium,
            color = Color(0xFF666666),
            modifier = Modifier.padding(bottom = 8.dp)
        )
        
        LazyRow(
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            items(listOf(
                "menstruation" to "月经期",
                "follicular" to "卵泡期", 
                "ovulation" to "排卵期",
                "luteal" to "黄体期",
                "unknown" to "不确定",
                "none" to "无"
            )) { (value, label) ->
                FilterChip(
                    onClick = { onSelectionChanged(value) },
                    label = { Text(label) },
                    selected = selected == value,
                    colors = FilterChipDefaults.filterChipColors(
                        selectedContainerColor = Color(0xFF9C27B0),
                        selectedLabelColor = Color.White
                    )
                )
            }
        }
    }
}

// 标签选择器
@Composable
fun TagSelector(
    selectedTags: List<String>,
    onTagsChanged: (List<String>) -> Unit
) {
    val predefinedTags = listOf(
        "第一次", "特别的日子", "纪念日", "生日", "情人节",
        "度假", "压力释放", "惊喜", "计划内", "自然发生",
        "新尝试", "完美", "需要改进", "值得纪念"
    )
    
    Column {
        Text(
            text = "标签 (可多选)",
            style = MaterialTheme.typography.bodyMedium,
            color = Color(0xFF666666),
            modifier = Modifier.padding(bottom = 8.dp)
        )
        
        LazyRow(
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            items(predefinedTags) { tag ->
                FilterChip(
                    onClick = {
                        val newTags = if (selectedTags.contains(tag)) {
                            selectedTags - tag
                        } else {
                            selectedTags + tag
                        }
                        onTagsChanged(newTags)
                    },
                    label = { Text(tag) },
                    selected = selectedTags.contains(tag),
                    colors = FilterChipDefaults.filterChipColors(
                        selectedContainerColor = Color(0xFF9C27B0),
                        selectedLabelColor = Color.White
                    )
                )
            }
        }
    }
}

// 特殊场合区域
@Composable
fun SpecialOccasionSection(
    isSpecialOccasion: Boolean,
    occasionNote: String,
    onSpecialOccasionChanged: (Boolean, String) -> Unit
) {
    Column {
        Row(
            verticalAlignment = Alignment.CenterVertically
        ) {
            Checkbox(
                checked = isSpecialOccasion,
                onCheckedChange = { onSpecialOccasionChanged(it, occasionNote) },
                colors = CheckboxDefaults.colors(
                    checkedColor = Color(0xFF9C27B0)
                )
            )
            Text(
                text = "特殊场合",
                style = MaterialTheme.typography.bodyMedium
            )
        }
        
        if (isSpecialOccasion) {
            Spacer(modifier = Modifier.height(8.dp))
            OutlinedTextField(
                value = occasionNote,
                onValueChange = { onSpecialOccasionChanged(isSpecialOccasion, it) },
                label = { Text("场合说明") },
                placeholder = { Text("如：结婚纪念日、生日等") },
                modifier = Modifier.fillMaxWidth(),
                colors = OutlinedTextFieldDefaults.colors(
                    focusedBorderColor = Color(0xFF9C27B0),
                    focusedLabelColor = Color(0xFF9C27B0)
                )
            )
        }
    }
}

// 隐私级别选择器
@Composable
fun PrivacyLevelSelector(
    selected: Int,
    onSelectionChanged: (Int) -> Unit
) {
    Column {
        Text(
            text = "隐私级别",
            style = MaterialTheme.typography.bodyMedium,
            color = Color(0xFF666666),
            modifier = Modifier.padding(bottom = 8.dp)
        )
        
        Row(
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            listOf(
                1 to "公开",
                2 to "部分私密", 
                3 to "私密",
                4 to "高度私密",
                5 to "绝密"
            ).forEach { (level, label) ->
                FilterChip(
                    onClick = { onSelectionChanged(level) },
                    label = { Text(label) },
                    selected = selected == level,
                    colors = FilterChipDefaults.filterChipColors(
                        selectedContainerColor = when (level) {
                            1 -> Color(0xFF4CAF50)
                            2 -> Color(0xFF2196F3)
                            3 -> Color(0xFF9C27B0)
                            4 -> Color(0xFFFF9800)
                            5 -> Color(0xFFE91E63)
                            else -> Color(0xFF9C27B0)
                        },
                        selectedLabelColor = Color.White
                    )
                )
            }
        }
        
        // 隐私级别说明
        Text(
            text = when (selected) {
                1 -> "记录可以被分享和导出"
                2 -> "记录部分信息可见"
                3 -> "仅自己可见的私密记录"
                4 -> "高度加密的敏感记录"
                5 -> "最高级别的绝密记录"
                else -> "选择适合的隐私级别"
            },
            style = MaterialTheme.typography.bodySmall,
            color = Color(0xFF666666),
            modifier = Modifier.padding(top = 4.dp)
        )
    }
}