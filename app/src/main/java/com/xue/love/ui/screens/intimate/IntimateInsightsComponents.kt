package com.xue.love.ui.screens.intimate

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.xue.love.data.local.entity.IntimateRecordEntity
import com.xue.love.data.repository.HealthInsights
import java.text.SimpleDateFormat
import java.util.*
import kotlin.math.roundToInt

@Composable
fun PersonalInsightsCard(
    healthInsights: HealthInsights,
    recentRecords: List<IntimateRecordEntity>
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.White.copy(alpha = 0.95f)
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = Icons.Default.Psychology,
                    contentDescription = null,
                    tint = Color(0xFF9C27B0),
                    modifier = Modifier.size(24.dp)
                )
                
                Spacer(modifier = Modifier.width(8.dp))
                
                Text(
                    text = "个人洞察",
                    style = MaterialTheme.typography.titleLarge,
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF2D1B20)
                )
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // 满意度趋势分析
            if (recentRecords.isNotEmpty()) {
                val satisfactionTrend = analyzeSatisfactionTrend(recentRecords)
                InsightItem(
                    icon = Icons.Default.TrendingUp,
                    title = "满意度趋势",
                    content = satisfactionTrend.description,
                    color = satisfactionTrend.color
                )
                
                Spacer(modifier = Modifier.height(12.dp))
            }
            
            // 频率分析
            val frequencyInsight = analyzeFrequency(healthInsights.weeklyFrequency)
            InsightItem(
                icon = Icons.Default.Schedule,
                title = "亲密频率",
                content = frequencyInsight.description,
                color = frequencyInsight.color
            )
            
            Spacer(modifier = Modifier.height(12.dp))
            
            // 保护措施分析
            val protectionInsight = analyzeProtectionUsage(healthInsights.protectionUsageRate)
            InsightItem(
                icon = Icons.Default.Shield,
                title = "健康保护",
                content = protectionInsight.description,
                color = protectionInsight.color
            )
            
            // 时间模式分析
            if (recentRecords.isNotEmpty()) {
                Spacer(modifier = Modifier.height(12.dp))
                val timePattern = analyzeTimePattern(recentRecords)
                InsightItem(
                    icon = Icons.Default.AccessTime,
                    title = "时间偏好",
                    content = timePattern.description,
                    color = timePattern.color
                )
            }
        }
    }
}

@Composable
fun RelationshipHealthCard(
    healthInsights: HealthInsights
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.White.copy(alpha = 0.95f)
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = Icons.Default.Favorite,
                    contentDescription = null,
                    tint = Color(0xFFE91E63),
                    modifier = Modifier.size(24.dp)
                )
                
                Spacer(modifier = Modifier.width(8.dp))
                
                Text(
                    text = "关系健康",
                    style = MaterialTheme.typography.titleLarge,
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF2D1B20)
                )
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // 整体健康评分
            val overallScore = calculateOverallHealthScore(healthInsights)
            HealthScoreIndicator(
                score = overallScore,
                label = "综合健康评分"
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // 具体指标
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                HealthMetric(
                    label = "满意度",
                    value = healthInsights.averageSatisfaction,
                    maxValue = 10f,
                    color = Color(0xFFE91E63)
                )
                
                HealthMetric(
                    label = "频率",
                    value = minOf(healthInsights.weeklyFrequency, 7f),
                    maxValue = 7f,
                    color = Color(0xFF9C27B0)
                )
                
                HealthMetric(
                    label = "保护",
                    value = healthInsights.protectionUsageRate,
                    maxValue = 100f,
                    color = Color(0xFF4CAF50)
                )
            }
        }
    }
}

@Composable
fun ImprovementSuggestionsCard(
    healthInsights: HealthInsights,
    recentRecords: List<IntimateRecordEntity>
) {
    val suggestions = generateImprovementSuggestions(healthInsights, recentRecords)
    
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.White.copy(alpha = 0.95f)
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = Icons.Default.Lightbulb,
                    contentDescription = null,
                    tint = Color(0xFFFF9800),
                    modifier = Modifier.size(24.dp)
                )
                
                Spacer(modifier = Modifier.width(8.dp))
                
                Text(
                    text = "改进建议",
                    style = MaterialTheme.typography.titleLarge,
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF2D1B20)
                )
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            suggestions.forEach { suggestion ->
                SuggestionItem(
                    icon = suggestion.icon,
                    title = suggestion.title,
                    description = suggestion.description,
                    priority = suggestion.priority
                )
                
                Spacer(modifier = Modifier.height(12.dp))
            }
        }
    }
}

@Composable
fun RecentTrendsCard(
    recentRecords: List<IntimateRecordEntity>
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.White.copy(alpha = 0.95f)
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = Icons.Default.Timeline,
                    contentDescription = null,
                    tint = Color(0xFF673AB7),
                    modifier = Modifier.size(24.dp)
                )
                
                Spacer(modifier = Modifier.width(8.dp))
                
                Text(
                    text = "最近趋势",
                    style = MaterialTheme.typography.titleLarge,
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF2D1B20)
                )
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            if (recentRecords.isEmpty()) {
                Text(
                    text = "暂无足够数据进行趋势分析",
                    style = MaterialTheme.typography.bodyMedium,
                    color = Color(0xFF2D1B20).copy(alpha = 0.6f),
                    textAlign = TextAlign.Center,
                    modifier = Modifier.fillMaxWidth()
                )
            } else {
                // 最近满意度变化
                val satisfactionTrend = recentRecords.takeLast(5).map { it.satisfaction }
                TrendIndicator(
                    label = "满意度变化",
                    values = satisfactionTrend,
                    color = Color(0xFFE91E63)
                )
                
                Spacer(modifier = Modifier.height(12.dp))
                
                // 最近频率变化
                val frequencyTrend = analyzeRecentFrequency(recentRecords)
                Text(
                    text = "频率趋势: $frequencyTrend",
                    style = MaterialTheme.typography.bodyMedium,
                    color = Color(0xFF2D1B20)
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                // 最活跃时间段
                val preferredTime = findPreferredTime(recentRecords)
                Text(
                    text = "偏好时间: $preferredTime",
                    style = MaterialTheme.typography.bodyMedium,
                    color = Color(0xFF2D1B20)
                )
            }
        }
    }
}

@Composable
private fun InsightItem(
    icon: ImageVector,
    title: String,
    content: String,
    color: Color
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        verticalAlignment = Alignment.Top
    ) {
        Box(
            modifier = Modifier
                .size(36.dp)
                .clip(RoundedCornerShape(8.dp))
                .background(color.copy(alpha = 0.1f)),
            contentAlignment = Alignment.Center
        ) {
            Icon(
                imageVector = icon,
                contentDescription = null,
                tint = color,
                modifier = Modifier.size(20.dp)
            )
        }
        
        Spacer(modifier = Modifier.width(12.dp))
        
        Column(
            modifier = Modifier.weight(1f)
        ) {
            Text(
                text = title,
                style = MaterialTheme.typography.titleSmall,
                fontWeight = FontWeight.Medium,
                color = Color(0xFF2D1B20)
            )
            
            Text(
                text = content,
                style = MaterialTheme.typography.bodySmall,
                color = Color(0xFF2D1B20).copy(alpha = 0.7f)
            )
        }
    }
}

@Composable
private fun HealthScoreIndicator(
    score: Float,
    label: String
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Box(
            modifier = Modifier.size(80.dp),
            contentAlignment = Alignment.Center
        ) {
            // 背景圆环
            Box(
                modifier = Modifier
                    .size(80.dp)
                    .clip(RoundedCornerShape(40.dp))
                    .background(Color(0xFF2D1B20).copy(alpha = 0.1f))
            )
            
            // 分数显示
            Text(
                text = "${score.roundToInt()}",
                style = MaterialTheme.typography.headlineMedium,
                fontWeight = FontWeight.Bold,
                color = getScoreColor(score)
            )
        }
        
        Spacer(modifier = Modifier.height(8.dp))
        
        Text(
            text = label,
            style = MaterialTheme.typography.bodySmall,
            color = Color(0xFF2D1B20).copy(alpha = 0.7f)
        )
        
        Text(
            text = getScoreDescription(score),
            style = MaterialTheme.typography.bodySmall,
            fontWeight = FontWeight.Medium,
            color = getScoreColor(score)
        )
    }
}

@Composable
private fun HealthMetric(
    label: String,
    value: Float,
    maxValue: Float,
    color: Color
) {
    val percentage = (value / maxValue).coerceIn(0f, 1f)
    
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Box(
            modifier = Modifier.size(60.dp),
            contentAlignment = Alignment.Center
        ) {
            CircularProgressIndicator(
                progress = percentage,
                modifier = Modifier.size(60.dp),
                color = color,
                trackColor = Color(0xFF2D1B20).copy(alpha = 0.1f),
                strokeWidth = 6.dp
            )
            
            Text(
                text = "${(percentage * 100).roundToInt()}%",
                style = MaterialTheme.typography.bodySmall,
                fontWeight = FontWeight.Bold,
                color = Color(0xFF2D1B20)
            )
        }
        
        Spacer(modifier = Modifier.height(4.dp))
        
        Text(
            text = label,
            style = MaterialTheme.typography.bodySmall,
            color = Color(0xFF2D1B20).copy(alpha = 0.7f)
        )
    }
}

@Composable
private fun SuggestionItem(
    icon: ImageVector,
    title: String,
    description: String,
    priority: SuggestionPriority
) {
    val priorityColor = when (priority) {
        SuggestionPriority.HIGH -> Color(0xFFE91E63)
        SuggestionPriority.MEDIUM -> Color(0xFFFF9800)
        SuggestionPriority.LOW -> Color(0xFF4CAF50)
    }
    
    Row(
        modifier = Modifier.fillMaxWidth(),
        verticalAlignment = Alignment.Top
    ) {
        Box(
            modifier = Modifier
                .size(36.dp)
                .clip(RoundedCornerShape(8.dp))
                .background(priorityColor.copy(alpha = 0.1f)),
            contentAlignment = Alignment.Center
        ) {
            Icon(
                imageVector = icon,
                contentDescription = null,
                tint = priorityColor,
                modifier = Modifier.size(20.dp)
            )
        }
        
        Spacer(modifier = Modifier.width(12.dp))
        
        Column(
            modifier = Modifier.weight(1f)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = title,
                    style = MaterialTheme.typography.titleSmall,
                    fontWeight = FontWeight.Medium,
                    color = Color(0xFF2D1B20),
                    modifier = Modifier.weight(1f)
                )
                
                Surface(
                    shape = RoundedCornerShape(8.dp),
                    color = priorityColor.copy(alpha = 0.1f),
                    modifier = Modifier.padding(start = 8.dp)
                ) {
                    Text(
                        text = when (priority) {
                            SuggestionPriority.HIGH -> "重要"
                            SuggestionPriority.MEDIUM -> "建议"
                            SuggestionPriority.LOW -> "提示"
                        },
                        style = MaterialTheme.typography.labelSmall,
                        color = priorityColor,
                        modifier = Modifier.padding(horizontal = 8.dp, vertical = 2.dp)
                    )
                }
            }
            
            Text(
                text = description,
                style = MaterialTheme.typography.bodySmall,
                color = Color(0xFF2D1B20).copy(alpha = 0.7f)
            )
        }
    }
}

@Composable
private fun TrendIndicator(
    label: String,
    values: List<Int>,
    color: Color
) {
    Column {
        Text(
            text = label,
            style = MaterialTheme.typography.bodyMedium,
            fontWeight = FontWeight.Medium,
            color = Color(0xFF2D1B20)
        )
        
        Spacer(modifier = Modifier.height(8.dp))
        
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.Bottom
        ) {
            values.forEach { value ->
                Box(
                    modifier = Modifier
                        .width(24.dp)
                        .height((value * 4).dp)
                        .clip(RoundedCornerShape(topStart = 4.dp, topEnd = 4.dp))
                        .background(color.copy(alpha = 0.7f))
                )
            }
        }
    }
}

// 数据类和枚举
data class Insight(
    val description: String,
    val color: Color
)

data class Suggestion(
    val icon: ImageVector,
    val title: String,
    val description: String,
    val priority: SuggestionPriority
)

enum class SuggestionPriority {
    HIGH, MEDIUM, LOW
}

// 分析函数
private fun analyzeSatisfactionTrend(records: List<IntimateRecordEntity>): Insight {
    if (records.size < 2) {
        return Insight("需要更多数据来分析趋势", Color(0xFF9E9E9E))
    }
    
    val recent = records.takeLast(3).map { it.satisfaction }
    val earlier = records.dropLast(3).takeLast(3).map { it.satisfaction }
    
    val recentAvg = recent.average()
    val earlierAvg = if (earlier.isNotEmpty()) earlier.average() else recentAvg
    
    return when {
        recentAvg > earlierAvg + 0.5 -> Insight("满意度呈上升趋势，很棒！", Color(0xFF4CAF50))
        recentAvg < earlierAvg - 0.5 -> Insight("满意度有所下降，需要关注", Color(0xFFE91E63))
        else -> Insight("满意度保持稳定", Color(0xFF2196F3))
    }
}

private fun analyzeFrequency(weeklyFrequency: Float): Insight {
    return when {
        weeklyFrequency >= 4f -> Insight("频率很健康，保持下去！", Color(0xFF4CAF50))
        weeklyFrequency >= 2f -> Insight("频率适中，可以考虑增加", Color(0xFFFF9800))
        weeklyFrequency >= 1f -> Insight("频率较低，建议增进沟通", Color(0xFFE91E63))
        else -> Insight("建议更多关注亲密关系", Color(0xFFE91E63))
    }
}

private fun analyzeProtectionUsage(protectionRate: Float): Insight {
    return when {
        protectionRate >= 90f -> Insight("健康保护做得很好！", Color(0xFF4CAF50))
        protectionRate >= 70f -> Insight("大部分时候有保护，继续保持", Color(0xFFFF9800))
        protectionRate >= 50f -> Insight("保护措施需要加强", Color(0xFFE91E63))
        else -> Insight("强烈建议重视健康保护", Color(0xFFE91E63))
    }
}

private fun analyzeTimePattern(records: List<IntimateRecordEntity>): Insight {
    val times = records.map {
        val calendar = Calendar.getInstance()
        calendar.timeInMillis = it.timestamp
        calendar.get(Calendar.HOUR_OF_DAY)
    }
    
    val avgHour = times.average().roundToInt()
    val timeDescription = when (avgHour) {
        in 6..11 -> "早晨"
        in 12..17 -> "下午"
        in 18..22 -> "晚上"
        else -> "深夜"
    }
    
    return Insight("你们偏好在${timeDescription}亲密", Color(0xFF673AB7))
}

private fun calculateOverallHealthScore(healthInsights: HealthInsights): Float {
    val satisfactionScore = healthInsights.averageSatisfaction
    val frequencyScore = minOf(healthInsights.weeklyFrequency * 2f, 10f)
    val protectionScore = healthInsights.protectionUsageRate / 10f
    
    return (satisfactionScore + frequencyScore + protectionScore) / 3f
}

private fun generateImprovementSuggestions(
    healthInsights: HealthInsights,
    recentRecords: List<IntimateRecordEntity>
): List<Suggestion> {
    val suggestions = mutableListOf<Suggestion>()
    
    // 满意度建议
    if (healthInsights.averageSatisfaction < 7f) {
        suggestions.add(
            Suggestion(
                icon = Icons.Default.Favorite,
                title = "提升满意度",
                description = "尝试更多沟通和前戏，了解彼此的喜好",
                priority = SuggestionPriority.HIGH
            )
        )
    }
    
    // 频率建议
    if (healthInsights.weeklyFrequency < 2f) {
        suggestions.add(
            Suggestion(
                icon = Icons.Default.Schedule,
                title = "增加亲密频率",
                description = "规划更多二人时光，营造浪漫氛围",
                priority = SuggestionPriority.MEDIUM
            )
        )
    }
    
    // 保护措施建议
    if (healthInsights.protectionUsageRate < 80f) {
        suggestions.add(
            Suggestion(
                icon = Icons.Default.Shield,
                title = "加强健康保护",
                description = "建议更频繁使用保护措施，保障双方健康",
                priority = SuggestionPriority.HIGH
            )
        )
    }
    
    // 记录建议
    if (healthInsights.totalRecords < 10) {
        suggestions.add(
            Suggestion(
                icon = Icons.Default.EventNote,
                title = "坚持记录",
                description = "继续记录可以帮助你们更好地了解彼此",
                priority = SuggestionPriority.LOW
            )
        )
    }
    
    return suggestions
}

private fun analyzeRecentFrequency(records: List<IntimateRecordEntity>): String {
    if (records.size < 4) return "数据不足"
    
    val now = System.currentTimeMillis()
    val twoWeeksAgo = now - (14 * 24 * 60 * 60 * 1000L)
    val oneWeekAgo = now - (7 * 24 * 60 * 60 * 1000L)
    
    val recentWeek = records.count { it.timestamp >= oneWeekAgo }
    val previousWeek = records.count { it.timestamp >= twoWeeksAgo && it.timestamp < oneWeekAgo }
    
    return when {
        recentWeek > previousWeek -> "上升"
        recentWeek < previousWeek -> "下降"
        else -> "稳定"
    }
}

private fun findPreferredTime(records: List<IntimateRecordEntity>): String {
    val hours = records.map {
        val calendar = Calendar.getInstance()
        calendar.timeInMillis = it.timestamp
        calendar.get(Calendar.HOUR_OF_DAY)
    }
    
    val timeGroups = mapOf(
        "清晨" to hours.count { it in 6..8 },
        "上午" to hours.count { it in 9..11 },
        "下午" to hours.count { it in 12..17 },
        "晚上" to hours.count { it in 18..22 },
        "深夜" to hours.count { it in 23..24 || it in 0..5 }
    )
    
    return timeGroups.maxByOrNull { it.value }?.key ?: "不确定"
}

private fun getScoreColor(score: Float): Color {
    return when {
        score >= 8f -> Color(0xFF4CAF50)
        score >= 6f -> Color(0xFFFF9800)
        else -> Color(0xFFE91E63)
    }
}

private fun getScoreDescription(score: Float): String {
    return when {
        score >= 8f -> "优秀"
        score >= 6f -> "良好"
        score >= 4f -> "一般"
        else -> "需改进"
    }
}