package com.xue.love.ui.screens.intimate

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import java.text.SimpleDateFormat
import java.util.*

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun IntimateRecordFormDialog(
    form: IntimateRecordForm,
    isEditing: Boolean,
    isSaving: Boolean,
    onFormUpdate: (IntimateRecordForm.() -> IntimateRecordForm) -> Unit,
    onSave: () -> Unit,
    onDismiss: () -> Unit
) {
    var selectedSection by remember { mutableStateOf(FormSection.BASIC) }
    
    Dialog(
        onDismissRequest = onDismiss,
        properties = DialogProperties(
            usePlatformDefaultWidth = false,
            dismissOnBackPress = true,
            dismissOnClickOutside = false
        )
    ) {
        Card(
            modifier = Modifier
                .fillMaxWidth(0.95f)
                .fillMaxHeight(0.9f),
            shape = RoundedCornerShape(20.dp),
            colors = CardDefaults.cardColors(
                containerColor = Color.White
            ),
            elevation = CardDefaults.cardElevation(defaultElevation = 16.dp)
        ) {
            Column(
                modifier = Modifier.fillMaxSize()
            ) {
                // 标题栏
                FormHeader(
                    isEditing = isEditing,
                    onDismiss = onDismiss,
                    onSave = onSave,
                    isSaving = isSaving
                )
                
                // 分节导航
                FormSectionTabs(
                    selectedSection = selectedSection,
                    onSectionSelected = { selectedSection = it }
                )
                
                // 表单内容
                LazyColumn(
                    modifier = Modifier
                        .weight(1f)
                        .padding(16.dp),
                    verticalArrangement = Arrangement.spacedBy(16.dp)
                ) {
                    when (selectedSection) {
                        FormSection.BASIC -> {
                            item { BasicInfoSection(form, onFormUpdate) }
                        }
                        FormSection.DETAILS -> {
                            item { DetailedInfoSection(form, onFormUpdate) }
                        }
                        FormSection.EMOTIONS -> {
                            item { EmotionalInfoSection(form, onFormUpdate) }
                        }
                        FormSection.HEALTH -> {
                            item { HealthInfoSection(form, onFormUpdate) }
                        }
                        FormSection.NOTES -> {
                            item { NotesSection(form, onFormUpdate) }
                        }
                    }
                }
            }
        }
    }
}

@Composable
private fun FormHeader(
    isEditing: Boolean,
    onDismiss: () -> Unit,
    onSave: () -> Unit,
    isSaving: Boolean
) {
    Surface(
        color = Color(0xFF9C27B0),
        modifier = Modifier.fillMaxWidth()
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                IconButton(onClick = onDismiss) {
                    Icon(
                        imageVector = Icons.Default.Close,
                        contentDescription = "关闭",
                        tint = Color.White
                    )
                }
                
                Spacer(modifier = Modifier.width(8.dp))
                
                Text(
                    text = if (isEditing) "编辑记录" else "添加记录",
                    style = MaterialTheme.typography.titleLarge,
                    fontWeight = FontWeight.Bold,
                    color = Color.White
                )
            }
            
            Button(
                onClick = onSave,
                enabled = !isSaving,
                colors = ButtonDefaults.buttonColors(
                    containerColor = Color.White,
                    contentColor = Color(0xFF9C27B0)
                )
            ) {
                if (isSaving) {
                    CircularProgressIndicator(
                        modifier = Modifier.size(16.dp),
                        color = Color(0xFF9C27B0),
                        strokeWidth = 2.dp
                    )
                } else {
                    Text("保存")
                }
            }
        }
    }
}

@Composable
private fun FormSectionTabs(
    selectedSection: FormSection,
    onSectionSelected: (FormSection) -> Unit
) {
    LazyRow(
        modifier = Modifier
            .fillMaxWidth()
            .background(Color(0xFFF5F5F5)),
        contentPadding = PaddingValues(horizontal = 16.dp),
        horizontalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        items(FormSection.values()) { section ->
            val isSelected = selectedSection == section
            
            Surface(
                modifier = Modifier
                    .clickable { onSectionSelected(section) }
                    .padding(vertical = 8.dp),
                shape = RoundedCornerShape(20.dp),
                color = if (isSelected) Color(0xFF9C27B0) else Color.Transparent
            ) {
                Text(
                    text = when (section) {
                        FormSection.BASIC -> "基本信息"
                        FormSection.DETAILS -> "详细信息"
                        FormSection.EMOTIONS -> "情感体验"
                        FormSection.HEALTH -> "健康记录"
                        FormSection.NOTES -> "备注标签"
                    },
                    modifier = Modifier.padding(horizontal = 16.dp, vertical = 8.dp),
                    style = MaterialTheme.typography.bodyMedium,
                    color = if (isSelected) Color.White else Color(0xFF666666),
                    fontWeight = if (isSelected) FontWeight.Bold else FontWeight.Normal
                )
            }
        }
    }
}

@Composable
private fun BasicInfoSection(
    form: IntimateRecordForm,
    onFormUpdate: (IntimateRecordForm.() -> IntimateRecordForm) -> Unit
) {
    Column(
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        SectionTitle("基本信息")
        
        // 时间选择
        TimePickerField(
            label = "时间",
            timestamp = form.timestamp,
            onTimeChanged = { timestamp ->
                onFormUpdate { copy(timestamp = timestamp) }
            }
        )
        
        // 地点
        OutlinedTextField(
            value = form.location,
            onValueChange = { onFormUpdate { copy(location = it) } },
            label = { Text("地点") },
            modifier = Modifier.fillMaxWidth(),
            colors = OutlinedTextFieldDefaults.colors(
                focusedBorderColor = Color(0xFF9C27B0),
                focusedLabelColor = Color(0xFF9C27B0)
            )
        )
        
        // 持续时间
        OutlinedTextField(
            value = if (form.duration > 0) form.duration.toString() else "",
            onValueChange = { value ->
                val duration = value.toIntOrNull() ?: 0
                onFormUpdate { copy(duration = duration) }
            },
            label = { Text("持续时间(分钟)") },
            keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
            modifier = Modifier.fillMaxWidth(),
            colors = OutlinedTextFieldDefaults.colors(
                focusedBorderColor = Color(0xFF9C27B0),
                focusedLabelColor = Color(0xFF9C27B0)
            )
        )
        
        // 发起者
        InitiatorSelector(
            selected = form.initiator,
            onSelectionChanged = { onFormUpdate { copy(initiator = it) } }
        )
        
        // 保护措施
        ProtectionSection(
            protectionUsed = form.protectionUsed,
            protectionType = form.protectionType,
            onProtectionChanged = { used, type ->
                onFormUpdate { copy(protectionUsed = used, protectionType = type) }
            }
        )
    }
}

@Composable
private fun DetailedInfoSection(
    form: IntimateRecordForm,
    onFormUpdate: (IntimateRecordForm.() -> IntimateRecordForm) -> Unit
) {
    Column(
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        SectionTitle("详细信息")
        
        // 前戏时长
        OutlinedTextField(
            value = if (form.foreplayDuration > 0) form.foreplayDuration.toString() else "",
            onValueChange = { value ->
                val duration = value.toIntOrNull() ?: 0
                onFormUpdate { copy(foreplayDuration = duration) }
            },
            label = { Text("前戏时长(分钟)") },
            keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
            modifier = Modifier.fillMaxWidth(),
            colors = OutlinedTextFieldDefaults.colors(
                focusedBorderColor = Color(0xFF9C27B0),
                focusedLabelColor = Color(0xFF9C27B0)
            )
        )
        
        // 姿势选择
        PositionSelector(
            selectedPositions = form.positions,
            onPositionsChanged = { onFormUpdate { copy(positions = it) } }
        )
        
        // 高潮情况
        ClimaxSelector(
            selected = form.climaxAchieved,
            onSelectionChanged = { onFormUpdate { copy(climaxAchieved = it) } }
        )
        
        // 环境
        OutlinedTextField(
            value = form.environment,
            onValueChange = { onFormUpdate { copy(environment = it) } },
            label = { Text("环境氛围") },
            placeholder = { Text("如：浪漫、激情、温馨等") },
            modifier = Modifier.fillMaxWidth(),
            colors = OutlinedTextFieldDefaults.colors(
                focusedBorderColor = Color(0xFF9C27B0),
                focusedLabelColor = Color(0xFF9C27B0)
            )
        )
        
        // 服装
        OutlinedTextField(
            value = form.clothing,
            onValueChange = { onFormUpdate { copy(clothing = it) } },
            label = { Text("服装描述") },
            modifier = Modifier.fillMaxWidth(),
            colors = OutlinedTextFieldDefaults.colors(
                focusedBorderColor = Color(0xFF9C27B0),
                focusedLabelColor = Color(0xFF9C27B0)
            )
        )
        
        // 特殊元素
        SpecialElementsSelector(
            selectedElements = form.specialElements,
            onElementsChanged = { onFormUpdate { copy(specialElements = it) } }
        )
    }
}

@Composable
private fun EmotionalInfoSection(
    form: IntimateRecordForm,
    onFormUpdate: (IntimateRecordForm.() -> IntimateRecordForm) -> Unit
) {
    Column(
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        SectionTitle("情感体验")
        
        // 心情
        MoodSelector(
            selected = form.mood,
            onSelectionChanged = { onFormUpdate { copy(mood = it) } }
        )
        
        // 满意度评分
        SatisfactionRatingSection(
            mySatisfaction = form.satisfaction,
            partnerSatisfaction = form.partnerSatisfaction,
            onMyRatingChanged = { onFormUpdate { copy(satisfaction = it) } },
            onPartnerRatingChanged = { onFormUpdate { copy(partnerSatisfaction = it) } }
        )
        
        // 能量和舒适度
        RatingSection(
            title = "能量水平",
            value = form.energy,
            onValueChanged = { onFormUpdate { copy(energy = it) } }
        )
        
        RatingSection(
            title = "舒适度",
            value = form.comfort,
            onValueChanged = { onFormUpdate { copy(comfort = it) } }
        )
        
        // 情感记录
        OutlinedTextField(
            value = form.emotionalNotes,
            onValueChange = { onFormUpdate { copy(emotionalNotes = it) } },
            label = { Text("情感记录") },
            placeholder = { Text("记录当时的情感感受...") },
            modifier = Modifier.fillMaxWidth(),
            minLines = 3,
            colors = OutlinedTextFieldDefaults.colors(
                focusedBorderColor = Color(0xFF9C27B0),
                focusedLabelColor = Color(0xFF9C27B0)
            )
        )
    }
}

@Composable
private fun HealthInfoSection(
    form: IntimateRecordForm,
    onFormUpdate: (IntimateRecordForm.() -> IntimateRecordForm) -> Unit
) {
    Column(
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        SectionTitle("健康记录")
        
        // 生理周期
        MenstrualPhaseSelector(
            selected = form.menstrualPhase,
            onSelectionChanged = { onFormUpdate { copy(menstrualPhase = it) } }
        )
        
        // 避孕方式
        OutlinedTextField(
            value = form.contraceptionMethod,
            onValueChange = { onFormUpdate { copy(contraceptionMethod = it) } },
            label = { Text("避孕方式") },
            modifier = Modifier.fillMaxWidth(),
            colors = OutlinedTextFieldDefaults.colors(
                focusedBorderColor = Color(0xFF9C27B0),
                focusedLabelColor = Color(0xFF9C27B0)
            )
        )
        
        // 身体记录
        OutlinedTextField(
            value = form.physicalNotes,
            onValueChange = { onFormUpdate { copy(physicalNotes = it) } },
            label = { Text("身体记录") },
            placeholder = { Text("记录身体感受、不适等...") },
            modifier = Modifier.fillMaxWidth(),
            minLines = 3,
            colors = OutlinedTextFieldDefaults.colors(
                focusedBorderColor = Color(0xFF9C27B0),
                focusedLabelColor = Color(0xFF9C27B0)
            )
        )
        
        // 健康备注
        OutlinedTextField(
            value = form.healthNotes,
            onValueChange = { onFormUpdate { copy(healthNotes = it) } },
            label = { Text("健康备注") },
            placeholder = { Text("其他健康相关信息...") },
            modifier = Modifier.fillMaxWidth(),
            minLines = 2,
            colors = OutlinedTextFieldDefaults.colors(
                focusedBorderColor = Color(0xFF9C27B0),
                focusedLabelColor = Color(0xFF9C27B0)
            )
        )
    }
}

@Composable
private fun NotesSection(
    form: IntimateRecordForm,
    onFormUpdate: (IntimateRecordForm.() -> IntimateRecordForm) -> Unit
) {
    Column(
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        SectionTitle("备注标签")
        
        // 亮点记录
        OutlinedTextField(
            value = form.highlights,
            onValueChange = { onFormUpdate { copy(highlights = it) } },
            label = { Text("美好时刻") },
            placeholder = { Text("记录这次最美好的时刻...") },
            modifier = Modifier.fillMaxWidth(),
            minLines = 3,
            colors = OutlinedTextFieldDefaults.colors(
                focusedBorderColor = Color(0xFF9C27B0),
                focusedLabelColor = Color(0xFF9C27B0)
            )
        )
        
        // 改进建议
        OutlinedTextField(
            value = form.improvementNotes,
            onValueChange = { onFormUpdate { copy(improvementNotes = it) } },
            label = { Text("改进建议") },
            placeholder = { Text("下次可以尝试的改进...") },
            modifier = Modifier.fillMaxWidth(),
            minLines = 2,
            colors = OutlinedTextFieldDefaults.colors(
                focusedBorderColor = Color(0xFF9C27B0),
                focusedLabelColor = Color(0xFF9C27B0)
            )
        )
        
        // 标签选择
        TagSelector(
            selectedTags = form.tags,
            onTagsChanged = { onFormUpdate { copy(tags = it) } }
        )
        
        // 特殊场合
        SpecialOccasionSection(
            isSpecialOccasion = form.isSpecialOccasion,
            occasionNote = form.occasionNote,
            onSpecialOccasionChanged = { isSpecial, note ->
                onFormUpdate { copy(isSpecialOccasion = isSpecial, occasionNote = note) }
            }
        )
        
        // 隐私级别
        PrivacyLevelSelector(
            selected = form.privacyLevel,
            onSelectionChanged = { onFormUpdate { copy(privacyLevel = it) } }
        )
    }
}

// 辅助组件
@Composable
private fun SectionTitle(title: String) {
    Text(
        text = title,
        style = MaterialTheme.typography.titleMedium,
        fontWeight = FontWeight.Bold,
        color = Color(0xFF9C27B0)
    )
}

@Composable
private fun TimePickerField(
    label: String,
    timestamp: Long,
    onTimeChanged: (Long) -> Unit
) {
    val dateFormatter = remember { SimpleDateFormat("yyyy年MM月dd日 HH:mm", Locale.getDefault()) }
    
    OutlinedTextField(
        value = dateFormatter.format(Date(timestamp)),
        onValueChange = { /* 只读 */ },
        label = { Text(label) },
        readOnly = true,
        modifier = Modifier.fillMaxWidth(),
        trailingIcon = {
            IconButton(onClick = { /* 打开时间选择器 */ }) {
                Icon(
                    imageVector = Icons.Default.Schedule,
                    contentDescription = "选择时间",
                    tint = Color(0xFF9C27B0)
                )
            }
        },
        colors = OutlinedTextFieldDefaults.colors(
            focusedBorderColor = Color(0xFF9C27B0),
            focusedLabelColor = Color(0xFF9C27B0)
        )
    )
}

@Composable
private fun InitiatorSelector(
    selected: String,
    onSelectionChanged: (String) -> Unit
) {
    Column {
        Text(
            text = "发起者",
            style = MaterialTheme.typography.bodyMedium,
            color = Color(0xFF666666)
        )
        
        Spacer(modifier = Modifier.height(8.dp))
        
        Row(
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            listOf("me" to "我", "partner" to "TA", "mutual" to "共同").forEach { (value, label) ->
                FilterChip(
                    onClick = { onSelectionChanged(value) },
                    label = { Text(label) },
                    selected = selected == value,
                    colors = FilterChipDefaults.filterChipColors(
                        selectedContainerColor = Color(0xFF9C27B0),
                        selectedLabelColor = Color.White
                    )
                )
            }
        }
    }
}

@Composable
private fun ProtectionSection(
    protectionUsed: Boolean,
    protectionType: String,
    onProtectionChanged: (Boolean, String) -> Unit
) {
    Column {
        Row(
            verticalAlignment = Alignment.CenterVertically
        ) {
            Checkbox(
                checked = protectionUsed,
                onCheckedChange = { onProtectionChanged(it, protectionType) },
                colors = CheckboxDefaults.colors(
                    checkedColor = Color(0xFF9C27B0)
                )
            )
            Text(
                text = "使用了保护措施",
                style = MaterialTheme.typography.bodyMedium
            )
        }
        
        if (protectionUsed) {
            Spacer(modifier = Modifier.height(8.dp))
            OutlinedTextField(
                value = protectionType,
                onValueChange = { onProtectionChanged(protectionUsed, it) },
                label = { Text("保护措施类型") },
                placeholder = { Text("如：安全套、避孕药等") },
                modifier = Modifier.fillMaxWidth(),
                colors = OutlinedTextFieldDefaults.colors(
                    focusedBorderColor = Color(0xFF9C27B0),
                    focusedLabelColor = Color(0xFF9C27B0)
                )
            )
        }
    }
}

// 更多辅助组件...
enum class FormSection {
    BASIC, DETAILS, EMOTIONS, HEALTH, NOTES
}

// 其他选择器组件的实现会在下一个文件中继续...