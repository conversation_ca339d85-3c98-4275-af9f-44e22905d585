package com.xue.love.ui.screens.intimate

import androidx.compose.animation.*
import androidx.compose.animation.core.tween
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.xue.love.R
import com.xue.love.data.local.entity.IntimateRecordEntity
import com.xue.love.ui.components.RomanticBackground
import com.xue.love.ui.theme.CoupleThemeType
import java.text.SimpleDateFormat
import java.util.*

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun IntimateRecordScreen(
    modifier: Modifier = Modifier,
    viewModel: IntimateRecordViewModel
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    val intimateRecords by viewModel.intimateRecords.collectAsStateWithLifecycle()
    val healthInsights by viewModel.healthInsights.collectAsStateWithLifecycle()
    
    // 处理消息显示
    LaunchedEffect(uiState.message) {
        uiState.message?.let {
            // 这里可以显示 Snackbar 或 Toast
            viewModel.clearMessage()
        }
    }
    
    Box(modifier = modifier.fillMaxSize()) {
        RomanticBackground(
            themeType = CoupleThemeType.MYSTERY_PURPLE
        ) {
            Column(modifier = Modifier.fillMaxSize()) {
                // 顶部标题栏
                IntimateTopBar(
                    onAddClick = { viewModel.showRecordForm() },
                    onStatisticsClick = { viewModel.showStatistics() }
                )
                
                // 标签栏
                IntimateTabBar(
                    selectedTab = uiState.selectedTab,
                    onTabSelected = viewModel::setSelectedTab
                )
                
                // 内容区域
                AnimatedContent(
                    targetState = uiState.selectedTab,
                    transitionSpec = {
                        slideInHorizontally(
                            initialOffsetX = { if (targetState.ordinal > initialState.ordinal) it else -it },
                            animationSpec = tween(300)
                        ) + fadeIn(tween(300)) togetherWith
                        slideOutHorizontally(
                            targetOffsetX = { if (targetState.ordinal > initialState.ordinal) -it else it },
                            animationSpec = tween(300)
                        ) + fadeOut(tween(300))
                    },
                    label = "intimate_tab_content"
                ) { tab ->
                    when (tab) {
                        IntimateTab.RECORDS -> {
                            IntimateRecordsList(
                                records = intimateRecords,
                                isLoading = uiState.isLoading,
                                onEditRecord = viewModel::editRecord,
                                onDeleteRecord = viewModel::deleteRecord,
                                modifier = Modifier.fillMaxSize()
                            )
                        }
                        IntimateTab.STATISTICS -> {
                            IntimateStatisticsContent(
                                healthInsights = healthInsights,
                                statisticsData = uiState.statisticsData,
                                onLoadStatistics = viewModel::loadStatistics,
                                modifier = Modifier.fillMaxSize()
                            )
                        }
                        IntimateTab.INSIGHTS -> {
                            IntimateInsightsContent(
                                healthInsights = healthInsights,
                                recentRecords = intimateRecords.take(5),
                                modifier = Modifier.fillMaxSize()
                            )
                        }
                    }
                }
            }
        }
        
        // 浮动操作按钮
        FloatingActionButton(
            onClick = { viewModel.showRecordForm() },
            modifier = Modifier
                .align(Alignment.BottomEnd)
                .padding(16.dp),
            containerColor = Color(0xFFFF6B9D),
            contentColor = Color.White
        ) {
            Icon(
                imageVector = Icons.Default.Add,
                contentDescription = "添加记录"
            )
        }
        
        // 错误提示
        uiState.error?.let { error ->
            Card(
                modifier = Modifier
                    .align(Alignment.TopCenter)
                    .padding(16.dp)
                    .fillMaxWidth(),
                colors = CardDefaults.cardColors(
                    containerColor = Color.Red.copy(alpha = 0.9f)
                )
            ) {
                Row(
                    modifier = Modifier.padding(16.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        imageVector = Icons.Default.Error,
                        contentDescription = null,
                        tint = Color.White
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = error,
                        color = Color.White,
                        style = MaterialTheme.typography.bodyMedium,
                        modifier = Modifier.weight(1f)
                    )
                    IconButton(onClick = viewModel::clearError) {
                        Icon(
                            imageVector = Icons.Default.Close,
                            contentDescription = "关闭",
                            tint = Color.White
                        )
                    }
                }
            }
        }
    }
    
    // 表单弹窗
    if (uiState.showRecordForm) {
        IntimateRecordFormDialog(
            form = uiState.recordForm,
            isEditing = uiState.isEditing,
            isSaving = uiState.isSaving,
            onFormUpdate = viewModel::updateRecordForm,
            onSave = viewModel::saveRecord,
            onDismiss = viewModel::hideRecordForm
        )
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun IntimateTopBar(
    onAddClick: () -> Unit,
    onStatisticsClick: () -> Unit
) {
    Surface(
        modifier = Modifier.fillMaxWidth(),
        color = Color.White.copy(alpha = 0.1f),
        shadowElevation = 4.dp
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = ImageVector.vectorResource(R.drawable.ic_heart_filled),
                    contentDescription = null,
                    tint = Color.White,
                    modifier = Modifier.size(24.dp)
                )
                
                Spacer(modifier = Modifier.width(8.dp))
                
                Text(
                    text = "亲密记录",
                    style = MaterialTheme.typography.headlineSmall,
                    fontWeight = FontWeight.Bold,
                    color = Color.White
                )
            }
            
            Row {
                IconButton(
                    onClick = onStatisticsClick,
                    modifier = Modifier
                        .size(40.dp)
                        .clip(CircleShape)
                        .background(Color.White.copy(alpha = 0.2f))
                ) {
                    Icon(
                        imageVector = Icons.Default.Analytics,
                        contentDescription = "统计",
                        tint = Color.White,
                        modifier = Modifier.size(20.dp)
                    )
                }
                
                Spacer(modifier = Modifier.width(8.dp))
                
                IconButton(
                    onClick = onAddClick,
                    modifier = Modifier
                        .size(40.dp)
                        .clip(CircleShape)
                        .background(Color.White.copy(alpha = 0.2f))
                ) {
                    Icon(
                        imageVector = Icons.Default.Add,
                        contentDescription = "添加",
                        tint = Color.White,
                        modifier = Modifier.size(20.dp)
                    )
                }
            }
        }
    }
}

@Composable
private fun IntimateTabBar(
    selectedTab: IntimateTab,
    onTabSelected: (IntimateTab) -> Unit
) {
    Surface(
        modifier = Modifier.fillMaxWidth(),
        color = Color.White.copy(alpha = 0.05f)
    ) {
        Row(
            modifier = Modifier.fillMaxWidth()
        ) {
            IntimateTab.values().forEach { tab ->
                val isSelected = selectedTab == tab
                Box(
                    modifier = Modifier
                        .weight(1f)
                        .clickable { onTabSelected(tab) }
                        .padding(vertical = 12.dp),
                    contentAlignment = Alignment.Center
                ) {
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Text(
                            text = when (tab) {
                                IntimateTab.RECORDS -> "记录"
                                IntimateTab.STATISTICS -> "统计"
                                IntimateTab.INSIGHTS -> "洞察"
                            },
                            style = if (isSelected) {
                                MaterialTheme.typography.titleSmall.copy(fontWeight = FontWeight.Bold)
                            } else {
                                MaterialTheme.typography.bodyMedium
                            },
                            color = if (isSelected) Color.White else Color.White.copy(alpha = 0.7f)
                        )
                        
                        if (isSelected) {
                            Box(
                                modifier = Modifier
                                    .padding(top = 4.dp)
                                    .width(24.dp)
                                    .height(2.dp)
                                    .background(
                                        Color.White,
                                        RoundedCornerShape(1.dp)
                                    )
                            )
                        }
                    }
                }
            }
        }
    }
}

@Composable
private fun IntimateRecordsList(
    records: List<IntimateRecordEntity>,
    isLoading: Boolean,
    onEditRecord: (IntimateRecordEntity) -> Unit,
    onDeleteRecord: (String) -> Unit,
    modifier: Modifier = Modifier
) {
    LazyColumn(
        modifier = modifier,
        contentPadding = PaddingValues(16.dp),
        verticalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        if (isLoading) {
            item {
                Box(
                    modifier = Modifier.fillMaxWidth(),
                    contentAlignment = Alignment.Center
                ) {
                    CircularProgressIndicator(color = Color.White)
                }
            }
        }
        
        if (records.isEmpty() && !isLoading) {
            item {
                EmptyRecordsPlaceholder()
            }
        }
        
        items(records, key = { it.id }) { record ->
            IntimateRecordCard(
                record = record,
                onEdit = { onEditRecord(record) },
                onDelete = { onDeleteRecord(record.id) }
            )
        }
    }
}

@Composable
private fun IntimateRecordCard(
    record: IntimateRecordEntity,
    onEdit: () -> Unit,
    onDelete: () -> Unit
) {
    val dateFormatter = remember { SimpleDateFormat("MM月dd日 HH:mm", Locale.getDefault()) }
    
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.White.copy(alpha = 0.95f)
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            // 头部信息
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column {
                    Text(
                        text = dateFormatter.format(Date(record.timestamp)),
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold,
                        color = Color(0xFF2D1B20)
                    )
                    if (record.location.isNotEmpty()) {
                        Text(
                            text = record.location,
                            style = MaterialTheme.typography.bodySmall,
                            color = Color(0xFF2D1B20).copy(alpha = 0.7f)
                        )
                    }
                }
                
                Row {
                    IconButton(onClick = onEdit) {
                        Icon(
                            imageVector = Icons.Default.Edit,
                            contentDescription = "编辑",
                            tint = Color(0xFF9C27B0)
                        )
                    }
                    IconButton(onClick = onDelete) {
                        Icon(
                            imageVector = Icons.Default.Delete,
                            contentDescription = "删除",
                            tint = Color(0xFFE91E63)
                        )
                    }
                }
            }
            
            Spacer(modifier = Modifier.height(12.dp))
            
            // 满意度显示
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                SatisfactionIndicator(
                    label = "我的满意度",
                    value = record.satisfaction,
                    color = Color(0xFF9C27B0)
                )
                
                SatisfactionIndicator(
                    label = "TA的满意度",
                    value = record.partnerSatisfaction,
                    color = Color(0xFFE91E63)
                )
            }
            
            // 持续时间
            if (record.duration > 0) {
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = "持续时间: ${record.duration}分钟",
                    style = MaterialTheme.typography.bodySmall,
                    color = Color(0xFF2D1B20).copy(alpha = 0.7f)
                )
            }
            
            // 心情
            if (record.mood.isNotEmpty()) {
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = "心情: ${record.mood}",
                    style = MaterialTheme.typography.bodySmall,
                    color = Color(0xFF2D1B20).copy(alpha = 0.7f)
                )
            }
            
            // 标签
            if (record.tags.isNotEmpty()) {
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = record.tags,
                    style = MaterialTheme.typography.bodySmall,
                    color = Color(0xFF2D1B20).copy(alpha = 0.5f)
                )
            }
        }
    }
}

@Composable
private fun SatisfactionIndicator(
    label: String,
    value: Int,
    color: Color
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = label,
            style = MaterialTheme.typography.bodySmall,
            color = Color(0xFF2D1B20).copy(alpha = 0.7f)
        )
        
        Row(
            verticalAlignment = Alignment.CenterVertically
        ) {
            repeat(5) { index ->
                Icon(
                    imageVector = if (index < value) Icons.Default.Favorite else Icons.Default.FavoriteBorder,
                    contentDescription = null,
                    tint = if (index < value) color else Color(0xFF2D1B20).copy(alpha = 0.3f),
                    modifier = Modifier.size(16.dp)
                )
            }
            
            Spacer(modifier = Modifier.width(4.dp))
            
            Text(
                text = "$value/10",
                style = MaterialTheme.typography.bodySmall,
                color = color,
                fontWeight = FontWeight.Bold
            )
        }
    }
}

@Composable
private fun EmptyRecordsPlaceholder() {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(32.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Icon(
            imageVector = ImageVector.vectorResource(R.drawable.ic_heart_filled),
            contentDescription = null,
            tint = Color.White.copy(alpha = 0.5f),
            modifier = Modifier.size(64.dp)
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        Text(
            text = "还没有记录",
            style = MaterialTheme.typography.headlineSmall,
            color = Color.White.copy(alpha = 0.7f),
            textAlign = TextAlign.Center
        )
        
        Spacer(modifier = Modifier.height(8.dp))
        
        Text(
            text = "开始记录你们的甜蜜时光吧",
            style = MaterialTheme.typography.bodyMedium,
            color = Color.White.copy(alpha = 0.5f),
            textAlign = TextAlign.Center
        )
    }
}