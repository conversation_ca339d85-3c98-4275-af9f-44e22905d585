package com.xue.love.ui.screens.intimate

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.xue.love.data.local.entity.IntimateRecordEntity
import com.xue.love.data.repository.IntimateRecordRepository
import com.xue.love.data.repository.HealthInsights
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import java.util.UUID

class IntimateRecordViewModel(
    private val repository: IntimateRecordRepository
) : ViewModel() {
    
    // UI 状态
    private val _uiState = MutableStateFlow(IntimateRecordUiState())
    val uiState: StateFlow<IntimateRecordUiState> = _uiState.asStateFlow()
    
    // 数据流
    private val _currentUserId = MutableStateFlow("user1") // 临时硬编码，后续从用户管理获取
    private val _currentPartnerId = MutableStateFlow("partner1")
    
    val intimateRecords = _currentUserId.flatMapLatest { userId ->
        repository.getRecordsByUser(userId)
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(5000),
        initialValue = emptyList()
    )
    
    val recentRecords = _currentUserId.flatMapLatest { userId ->
        flow { emit(repository.getRecentRecords(userId, 10)) }
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(5000),
        initialValue = emptyList()
    )
    
    val healthInsights = _currentUserId.flatMapLatest { userId ->
        flow { emit(repository.getHealthInsights(userId)) }
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(5000),
        initialValue = HealthInsights(0, 5f, 5f, 0f, 0f, null)
    )
    
    init {
        loadData()
    }
    
    private fun loadData() {
        viewModelScope.launch {
            try {
                _uiState.update { it.copy(isLoading = true, error = null) }
                
                val userId = _currentUserId.value
                val stats = repository.getHealthInsights(userId)
                val tags = repository.getAllTags(userId)
                val moods = repository.getAllMoods(userId)
                val locations = repository.getAllLocations(userId)
                
                _uiState.update { currentState ->
                    currentState.copy(
                        isLoading = false,
                        availableTags = tags,
                        availableMoods = moods,
                        availableLocations = locations
                    )
                }
            } catch (e: Exception) {
                _uiState.update { 
                    it.copy(isLoading = false, error = "加载数据失败: ${e.message}")
                }
            }
        }
    }
    
    // 表单状态管理
    fun updateRecordForm(update: (IntimateRecordForm) -> IntimateRecordForm) {
        _uiState.update { currentState ->
            currentState.copy(recordForm = update(currentState.recordForm))
        }
    }
    
    fun clearForm() {
        _uiState.update { it.copy(recordForm = IntimateRecordForm()) }
    }
    
    // 保存记录
    fun saveRecord() {
        viewModelScope.launch {
            try {
                _uiState.update { it.copy(isSaving = true, error = null) }
                
                val form = _uiState.value.recordForm
                val record = IntimateRecordEntity(
                    id = form.id.ifEmpty { UUID.randomUUID().toString() },
                    userId = _currentUserId.value,
                    partnerId = _currentPartnerId.value,
                    timestamp = form.timestamp,
                    location = form.location,
                    duration = form.duration,
                    initiator = form.initiator,
                    protectionUsed = form.protectionUsed,
                    protectionType = form.protectionType,
                    climaxAchieved = form.climaxAchieved,
                    positions = form.positions.joinToString(","),
                    foreplayDuration = form.foreplayDuration,
                    mood = form.mood,
                    satisfaction = form.satisfaction,
                    partnerSatisfaction = form.partnerSatisfaction,
                    energy = form.energy,
                    comfort = form.comfort,
                    clothing = form.clothing,
                    environment = form.environment,
                    specialElements = form.specialElements.joinToString(","),
                    physicalNotes = form.physicalNotes,
                    emotionalNotes = form.emotionalNotes,
                    highlights = form.highlights,
                    improvementNotes = form.improvementNotes,
                    menstrualPhase = form.menstrualPhase,
                    contraceptionMethod = form.contraceptionMethod,
                    healthNotes = form.healthNotes,
                    tags = form.tags.joinToString(","),
                    isSpecialOccasion = form.isSpecialOccasion,
                    occasionNote = form.occasionNote,
                    privacyLevel = form.privacyLevel
                )
                
                if (form.id.isEmpty()) {
                    repository.insertRecord(record)
                } else {
                    repository.updateRecord(record)
                }
                
                _uiState.update { 
                    it.copy(
                        isSaving = false, 
                        recordForm = IntimateRecordForm(),
                        showRecordForm = false
                    )
                }
                
                showMessage("记录保存成功")
                
            } catch (e: Exception) {
                _uiState.update { 
                    it.copy(isSaving = false, error = "保存失败: ${e.message}")
                }
            }
        }
    }
    
    // 删除记录
    fun deleteRecord(recordId: String) {
        viewModelScope.launch {
            try {
                repository.deleteRecordById(recordId)
                showMessage("记录已删除")
            } catch (e: Exception) {
                _uiState.update { 
                    it.copy(error = "删除失败: ${e.message}")
                }
            }
        }
    }
    
    // 编辑记录
    fun editRecord(record: IntimateRecordEntity) {
        val form = IntimateRecordForm(
            id = record.id,
            timestamp = record.timestamp,
            location = record.location,
            duration = record.duration,
            initiator = record.initiator,
            protectionUsed = record.protectionUsed,
            protectionType = record.protectionType,
            climaxAchieved = record.climaxAchieved,
            positions = record.positions.split(",").filter { it.isNotBlank() },
            foreplayDuration = record.foreplayDuration,
            mood = record.mood,
            satisfaction = record.satisfaction,
            partnerSatisfaction = record.partnerSatisfaction,
            energy = record.energy,
            comfort = record.comfort,
            clothing = record.clothing,
            environment = record.environment,
            specialElements = record.specialElements.split(",").filter { it.isNotBlank() },
            physicalNotes = record.physicalNotes,
            emotionalNotes = record.emotionalNotes,
            highlights = record.highlights,
            improvementNotes = record.improvementNotes,
            menstrualPhase = record.menstrualPhase,
            contraceptionMethod = record.contraceptionMethod,
            healthNotes = record.healthNotes,
            tags = record.tags.split(",").filter { it.isNotBlank() },
            isSpecialOccasion = record.isSpecialOccasion,
            occasionNote = record.occasionNote,
            privacyLevel = record.privacyLevel
        )
        
        _uiState.update { 
            it.copy(recordForm = form, showRecordForm = true, isEditing = true)
        }
    }
    
    // UI 操作
    fun showRecordForm() {
        _uiState.update { it.copy(showRecordForm = true, isEditing = false) }
    }
    
    fun hideRecordForm() {
        _uiState.update { it.copy(showRecordForm = false, recordForm = IntimateRecordForm()) }
    }
    
    fun showStatistics() {
        _uiState.update { it.copy(showStatistics = true) }
    }
    
    fun hideStatistics() {
        _uiState.update { it.copy(showStatistics = false) }
    }
    
    fun setSelectedTab(tab: IntimateTab) {
        _uiState.update { it.copy(selectedTab = tab) }
    }
    
    // 搜索和过滤
    fun searchRecords(query: String) {
        _uiState.update { it.copy(searchQuery = query) }
        
        if (query.isBlank()) {
            return
        }
        
        viewModelScope.launch {
            try {
                val results = repository.searchRecords(_currentUserId.value, query)
                    .first() // 获取一次性结果
                _uiState.update { it.copy(searchResults = results) }
            } catch (e: Exception) {
                _uiState.update { it.copy(error = "搜索失败: ${e.message}") }
            }
        }
    }
    
    fun clearSearch() {
        _uiState.update { it.copy(searchQuery = "", searchResults = emptyList()) }
    }
    
    // 统计数据
    fun loadStatistics() {
        viewModelScope.launch {
            try {
                val userId = _currentUserId.value
                val moodStats = repository.getMoodStatistics(userId)
                val locationStats = repository.getLocationStatistics(userId)
                val climaxStats = repository.getClimaxStatistics(userId)
                val monthlyStats = repository.getMonthlyStatistics(userId, 6)
                
                _uiState.update { currentState ->
                    currentState.copy(
                        statisticsData = StatisticsData(
                            moodStatistics = moodStats,
                            locationStatistics = locationStats,
                            climaxStatistics = climaxStats,
                            monthlyStatistics = monthlyStats
                        )
                    )
                }
            } catch (e: Exception) {
                _uiState.update { it.copy(error = "加载统计数据失败: ${e.message}") }
            }
        }
    }
    
    // 消息处理
    private fun showMessage(message: String) {
        _uiState.update { it.copy(message = message) }
    }
    
    fun clearMessage() {
        _uiState.update { it.copy(message = null) }
    }
    
    fun clearError() {
        _uiState.update { it.copy(error = null) }
    }
}

// UI 状态数据类
data class IntimateRecordUiState(
    val isLoading: Boolean = false,
    val isSaving: Boolean = false,
    val error: String? = null,
    val message: String? = null,
    val selectedTab: IntimateTab = IntimateTab.RECORDS,
    val showRecordForm: Boolean = false,
    val showStatistics: Boolean = false,
    val isEditing: Boolean = false,
    val recordForm: IntimateRecordForm = IntimateRecordForm(),
    val searchQuery: String = "",
    val searchResults: List<IntimateRecordEntity> = emptyList(),
    val availableTags: List<String> = emptyList(),
    val availableMoods: List<String> = emptyList(),
    val availableLocations: List<String> = emptyList(),
    val statisticsData: StatisticsData = StatisticsData()
)

// 表单数据类
data class IntimateRecordForm(
    val id: String = "",
    val timestamp: Long = System.currentTimeMillis(),
    val location: String = "",
    val duration: Int = 0,
    val initiator: String = "me",
    val protectionUsed: Boolean = false,
    val protectionType: String = "",
    val climaxAchieved: String = "",
    val positions: List<String> = emptyList(),
    val foreplayDuration: Int = 0,
    val mood: String = "",
    val satisfaction: Int = 5,
    val partnerSatisfaction: Int = 5,
    val energy: Int = 5,
    val comfort: Int = 5,
    val clothing: String = "",
    val environment: String = "",
    val specialElements: List<String> = emptyList(),
    val physicalNotes: String = "",
    val emotionalNotes: String = "",
    val highlights: String = "",
    val improvementNotes: String = "",
    val menstrualPhase: String = "",
    val contraceptionMethod: String = "",
    val healthNotes: String = "",
    val tags: List<String> = emptyList(),
    val isSpecialOccasion: Boolean = false,
    val occasionNote: String = "",
    val privacyLevel: Int = 3
)

// 标签页枚举
enum class IntimateTab {
    RECORDS, STATISTICS, INSIGHTS
}

// 统计数据
data class StatisticsData(
    val moodStatistics: List<com.xue.love.data.local.dao.MoodCount> = emptyList(),
    val locationStatistics: List<com.xue.love.data.local.dao.LocationCount> = emptyList(),
    val climaxStatistics: List<com.xue.love.data.local.dao.ClimaxCount> = emptyList(),
    val monthlyStatistics: List<com.xue.love.data.local.dao.IntimateMonthStatistics> = emptyList()
)