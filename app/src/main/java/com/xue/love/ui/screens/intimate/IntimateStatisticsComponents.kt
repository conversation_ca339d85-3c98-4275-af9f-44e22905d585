package com.xue.love.ui.screens.intimate

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.xue.love.data.local.entity.IntimateRecordEntity
import com.xue.love.data.repository.HealthInsights
import java.text.SimpleDateFormat
import java.util.*
import kotlin.math.roundToInt

@Composable
fun IntimateStatisticsContent(
    healthInsights: HealthInsights,
    statisticsData: StatisticsData,
    onLoadStatistics: () -> Unit,
    modifier: Modifier = Modifier
) {
    LaunchedEffect(Unit) {
        onLoadStatistics()
    }
    
    LazyColumn(
        modifier = modifier,
        contentPadding = PaddingValues(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // 健康洞察概览
        item {
            HealthOverviewCard(healthInsights = healthInsights)
        }
        
        // 心情统计
        if (statisticsData.moodStatistics.isNotEmpty()) {
            item {
                MoodStatisticsCard(moodStats = statisticsData.moodStatistics)
            }
        }
        
        // 地点统计
        if (statisticsData.locationStatistics.isNotEmpty()) {
            item {
                LocationStatisticsCard(locationStats = statisticsData.locationStatistics)
            }
        }
        
        // 高潮统计
        if (statisticsData.climaxStatistics.isNotEmpty()) {
            item {
                ClimaxStatisticsCard(climaxStats = statisticsData.climaxStatistics)
            }
        }
        
        // 月度趋势
        if (statisticsData.monthlyStatistics.isNotEmpty()) {
            item {
                MonthlyTrendCard(monthlyStats = statisticsData.monthlyStatistics)
            }
        }
    }
}

@Composable
fun IntimateInsightsContent(
    healthInsights: HealthInsights,
    recentRecords: List<IntimateRecordEntity>,
    modifier: Modifier = Modifier
) {
    LazyColumn(
        modifier = modifier,
        contentPadding = PaddingValues(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // 个人洞察
        item {
            PersonalInsightsCard(
                healthInsights = healthInsights,
                recentRecords = recentRecords
            )
        }
        
        // 关系健康指标
        item {
            RelationshipHealthCard(healthInsights = healthInsights)
        }
        
        // 改进建议
        item {
            ImprovementSuggestionsCard(
                healthInsights = healthInsights,
                recentRecords = recentRecords
            )
        }
        
        // 最近趋势
        item {
            RecentTrendsCard(recentRecords = recentRecords)
        }
    }
}

@Composable
private fun HealthOverviewCard(
    healthInsights: HealthInsights
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.White.copy(alpha = 0.95f)
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = Icons.Default.Analytics,
                    contentDescription = null,
                    tint = Color(0xFF9C27B0),
                    modifier = Modifier.size(24.dp)
                )
                
                Spacer(modifier = Modifier.width(8.dp))
                
                Text(
                    text = "健康概览",
                    style = MaterialTheme.typography.titleLarge,
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF2D1B20)
                )
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                StatisticItem(
                    label = "总记录",
                    value = "${healthInsights.totalRecords}",
                    icon = Icons.Default.EventNote,
                    color = Color(0xFF9C27B0)
                )
                
                StatisticItem(
                    label = "平均满意度",
                    value = "${healthInsights.averageSatisfaction.roundToInt()}/10",
                    icon = Icons.Default.Favorite,
                    color = Color(0xFFE91E63)
                )
                
                StatisticItem(
                    label = "每周频率",
                    value = "${healthInsights.weeklyFrequency.roundToInt()}次",
                    icon = Icons.Default.Schedule,
                    color = Color(0xFF673AB7)
                )
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // 保护措施使用率
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "保护措施使用率",
                    style = MaterialTheme.typography.bodyMedium,
                    color = Color(0xFF2D1B20),
                    modifier = Modifier.weight(1f)
                )
                
                Text(
                    text = "${healthInsights.protectionUsageRate.roundToInt()}%",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold,
                    color = if (healthInsights.protectionUsageRate >= 80) {
                        Color(0xFF4CAF50)
                    } else if (healthInsights.protectionUsageRate >= 50) {
                        Color(0xFFFF9800)
                    } else {
                        Color(0xFFE91E63)
                    }
                )
            }
            
            LinearProgressIndicator(
                progress = healthInsights.protectionUsageRate / 100f,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = 8.dp),
                color = if (healthInsights.protectionUsageRate >= 80) {
                    Color(0xFF4CAF50)
                } else if (healthInsights.protectionUsageRate >= 50) {
                    Color(0xFFFF9800)
                } else {
                    Color(0xFFE91E63)
                },
                trackColor = Color(0xFF2D1B20).copy(alpha = 0.1f)
            )
        }
    }
}

@Composable
private fun StatisticItem(
    label: String,
    value: String,
    icon: ImageVector,
    color: Color
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Box(
            modifier = Modifier
                .size(48.dp)
                .clip(RoundedCornerShape(12.dp))
                .background(color.copy(alpha = 0.1f)),
            contentAlignment = Alignment.Center
        ) {
            Icon(
                imageVector = icon,
                contentDescription = null,
                tint = color,
                modifier = Modifier.size(24.dp)
            )
        }
        
        Spacer(modifier = Modifier.height(8.dp))
        
        Text(
            text = value,
            style = MaterialTheme.typography.titleMedium,
            fontWeight = FontWeight.Bold,
            color = Color(0xFF2D1B20)
        )
        
        Text(
            text = label,
            style = MaterialTheme.typography.bodySmall,
            color = Color(0xFF2D1B20).copy(alpha = 0.7f),
            textAlign = TextAlign.Center
        )
    }
}

@Composable
private fun MoodStatisticsCard(
    moodStats: List<com.xue.love.data.local.dao.MoodCount>
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.White.copy(alpha = 0.95f)
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = Icons.Default.Mood,
                    contentDescription = null,
                    tint = Color(0xFFFF9800),
                    modifier = Modifier.size(24.dp)
                )
                
                Spacer(modifier = Modifier.width(8.dp))
                
                Text(
                    text = "心情分析",
                    style = MaterialTheme.typography.titleLarge,
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF2D1B20)
                )
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            moodStats.forEach { moodStat ->
                MoodStatRow(
                    mood = moodStat.mood,
                    count = moodStat.count,
                    total = moodStats.sumOf { it.count }
                )
                
                Spacer(modifier = Modifier.height(8.dp))
            }
        }
    }
}

@Composable
private fun MoodStatRow(
    mood: String,
    count: Int,
    total: Int
) {
    val percentage = if (total > 0) (count.toFloat() / total * 100).roundToInt() else 0
    
    Row(
        modifier = Modifier.fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = mood,
            style = MaterialTheme.typography.bodyMedium,
            color = Color(0xFF2D1B20),
            modifier = Modifier.weight(1f)
        )
        
        Text(
            text = "${count}次 (${percentage}%)",
            style = MaterialTheme.typography.bodySmall,
            color = Color(0xFF2D1B20).copy(alpha = 0.7f)
        )
    }
    
    LinearProgressIndicator(
        progress = count.toFloat() / total,
        modifier = Modifier
            .fillMaxWidth()
            .padding(top = 4.dp),
        color = getMoodColor(mood),
        trackColor = Color(0xFF2D1B20).copy(alpha = 0.1f)
    )
}

@Composable
private fun LocationStatisticsCard(
    locationStats: List<com.xue.love.data.local.dao.LocationCount>
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.White.copy(alpha = 0.95f)
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = Icons.Default.LocationOn,
                    contentDescription = null,
                    tint = Color(0xFF4CAF50),
                    modifier = Modifier.size(24.dp)
                )
                
                Spacer(modifier = Modifier.width(8.dp))
                
                Text(
                    text = "地点偏好",
                    style = MaterialTheme.typography.titleLarge,
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF2D1B20)
                )
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            LazyRow(
                horizontalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                items(locationStats) { locationStat ->
                    LocationChip(
                        location = locationStat.location,
                        count = locationStat.count
                    )
                }
            }
        }
    }
}

@Composable
private fun LocationChip(
    location: String,
    count: Int
) {
    Surface(
        shape = RoundedCornerShape(20.dp),
        color = Color(0xFF4CAF50).copy(alpha = 0.1f),
        modifier = Modifier.padding(4.dp)
    ) {
        Column(
            modifier = Modifier.padding(horizontal = 16.dp, vertical = 8.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = location,
                style = MaterialTheme.typography.bodyMedium,
                fontWeight = FontWeight.Medium,
                color = Color(0xFF2D1B20)
            )
            
            Text(
                text = "${count}次",
                style = MaterialTheme.typography.bodySmall,
                color = Color(0xFF4CAF50)
            )
        }
    }
}

@Composable
private fun ClimaxStatisticsCard(
    climaxStats: List<com.xue.love.data.local.dao.ClimaxCount>
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.White.copy(alpha = 0.95f)
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = Icons.Default.TrendingUp,
                    contentDescription = null,
                    tint = Color(0xFFE91E63),
                    modifier = Modifier.size(24.dp)
                )
                
                Spacer(modifier = Modifier.width(8.dp))
                
                Text(
                    text = "满足度分析",
                    style = MaterialTheme.typography.titleLarge,
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF2D1B20)
                )
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            climaxStats.forEach { climaxStat ->
                ClimaxStatRow(
                    type = climaxStat.climaxAchieved,
                    count = climaxStat.count,
                    total = climaxStats.sumOf { it.count }
                )
                
                Spacer(modifier = Modifier.height(8.dp))
            }
        }
    }
}

@Composable
private fun ClimaxStatRow(
    type: String,
    count: Int,
    total: Int
) {
    val percentage = if (total > 0) (count.toFloat() / total * 100).roundToInt() else 0
    val displayText = when (type) {
        "both" -> "双方满足"
        "me" -> "我满足"
        "partner" -> "TA满足"
        "none" -> "待改进"
        else -> type
    }
    
    Row(
        modifier = Modifier.fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = displayText,
            style = MaterialTheme.typography.bodyMedium,
            color = Color(0xFF2D1B20),
            modifier = Modifier.weight(1f)
        )
        
        Text(
            text = "${count}次 (${percentage}%)",
            style = MaterialTheme.typography.bodySmall,
            color = Color(0xFF2D1B20).copy(alpha = 0.7f)
        )
    }
    
    LinearProgressIndicator(
        progress = count.toFloat() / total,
        modifier = Modifier
            .fillMaxWidth()
            .padding(top = 4.dp),
        color = getClimaxColor(type),
        trackColor = Color(0xFF2D1B20).copy(alpha = 0.1f)
    )
}

@Composable
private fun MonthlyTrendCard(
    monthlyStats: List<com.xue.love.data.local.dao.IntimateMonthStatistics>
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.White.copy(alpha = 0.95f)
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = Icons.Default.Timeline,
                    contentDescription = null,
                    tint = Color(0xFF673AB7),
                    modifier = Modifier.size(24.dp)
                )
                
                Spacer(modifier = Modifier.width(8.dp))
                
                Text(
                    text = "月度趋势",
                    style = MaterialTheme.typography.titleLarge,
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF2D1B20)
                )
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            monthlyStats.forEach { monthlyStat ->
                MonthlyStatRow(
                    month = monthlyStat.month,
                    count = monthlyStat.count,
                    avgSatisfaction = monthlyStat.avgSatisfaction ?: 0f
                )
                
                Spacer(modifier = Modifier.height(12.dp))
            }
        }
    }
}

@Composable
private fun MonthlyStatRow(
    month: String,
    count: Int,
    avgSatisfaction: Float
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Column(
            modifier = Modifier.weight(1f)
        ) {
            Text(
                text = month,
                style = MaterialTheme.typography.bodyMedium,
                fontWeight = FontWeight.Medium,
                color = Color(0xFF2D1B20)
            )
            
            Text(
                text = "${count}次记录",
                style = MaterialTheme.typography.bodySmall,
                color = Color(0xFF2D1B20).copy(alpha = 0.7f)
            )
        }
        
        Column(
            horizontalAlignment = Alignment.End
        ) {
            Text(
                text = "满意度",
                style = MaterialTheme.typography.bodySmall,
                color = Color(0xFF2D1B20).copy(alpha = 0.7f)
            )
            
            Text(
                text = "${avgSatisfaction.roundToInt()}/10",
                style = MaterialTheme.typography.titleSmall,
                fontWeight = FontWeight.Bold,
                color = Color(0xFF673AB7)
            )
        }
    }
}

// 辅助函数
private fun getMoodColor(mood: String): Color {
    return when (mood.lowercase()) {
        "开心", "兴奋", "快乐" -> Color(0xFF4CAF50)
        "浪漫", "温柔", "甜蜜" -> Color(0xFFE91E63)
        "激情", "热情", "狂野" -> Color(0xFFFF5722)
        "放松", "舒适", "安静" -> Color(0xFF2196F3)
        "疲惫", "累了", "无力" -> Color(0xFF9E9E9E)
        else -> Color(0xFF9C27B0)
    }
}

private fun getClimaxColor(type: String): Color {
    return when (type) {
        "both" -> Color(0xFF4CAF50)
        "me", "partner" -> Color(0xFFFF9800)
        "none" -> Color(0xFFE91E63)
        else -> Color(0xFF9C27B0)
    }
}