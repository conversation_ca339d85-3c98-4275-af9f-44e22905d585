package com.xue.love.ui.screens.menstrual

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.xue.love.data.local.entity.MenstrualCycleEntity
import com.xue.love.data.model.CyclePhase
import com.xue.love.data.model.PeriodPrediction
import com.xue.love.data.model.OvulationPrediction
import java.text.SimpleDateFormat
import java.util.*
import kotlin.math.roundToInt

@Composable
fun MenstrualCalendarContent(
    currentCycle: MenstrualCycleEntity?,
    periodPrediction: PeriodPrediction,
    ovulationPrediction: OvulationPrediction,
    modifier: Modifier = Modifier
) {
    LazyColumn(
        modifier = modifier,
        contentPadding = PaddingValues(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // 当前周期概览
        item {
            CurrentCycleOverviewCard(
                currentCycle = currentCycle,
                periodPrediction = periodPrediction
            )
        }
        
        // 月历视图
        item {
            CalendarCard(
                currentCycle = currentCycle,
                periodPrediction = periodPrediction,
                ovulationPrediction = ovulationPrediction
            )
        }
        
        // 下次预测
        item {
            NextPredictionCard(
                periodPrediction = periodPrediction,
                ovulationPrediction = ovulationPrediction
            )
        }
    }
}

@Composable
fun MenstrualPredictionsContent(
    periodPrediction: PeriodPrediction,
    ovulationPrediction: OvulationPrediction,
    currentPhase: CyclePhase,
    modifier: Modifier = Modifier
) {
    LazyColumn(
        modifier = modifier,
        contentPadding = PaddingValues(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // 当前阶段
        item {
            CurrentPhaseCard(currentPhase = currentPhase)
        }
        
        // 月经预测
        item {
            PeriodPredictionCard(periodPrediction = periodPrediction)
        }
        
        // 排卵预测
        item {
            OvulationPredictionCard(ovulationPrediction = ovulationPrediction)
        }
        
        // 生育窗口
        item {
            FertilityWindowCard(ovulationPrediction = ovulationPrediction)
        }
        
        // 预测准确性
        item {
            PredictionAccuracyCard(
                periodConfidence = periodPrediction.confidence,
                ovulationConfidence = ovulationPrediction.confidence
            )
        }
    }
}

@Composable
private fun CurrentCycleOverviewCard(
    currentCycle: MenstrualCycleEntity?,
    periodPrediction: PeriodPrediction
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.White.copy(alpha = 0.95f)
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = Icons.Default.CalendarToday,
                    contentDescription = null,
                    tint = Color(0xFFE91E63),
                    modifier = Modifier.size(24.dp)
                )
                
                Spacer(modifier = Modifier.width(8.dp))
                
                Text(
                    text = "当前周期",
                    style = MaterialTheme.typography.titleLarge,
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF2D1B20)
                )
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            if (currentCycle != null) {
                val daysSinceStart = ((System.currentTimeMillis() - currentCycle.cycleStartDate) / (24 * 60 * 60 * 1000L)).toInt() + 1
                val daysLeft = currentCycle.cycleDuration - daysSinceStart + 1
                
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceEvenly
                ) {
                    CycleStatItem(
                        label = "周期第",
                        value = "${daysSinceStart}天",
                        icon = Icons.Default.Today,
                        color = Color(0xFFE91E63)
                    )
                    
                    CycleStatItem(
                        label = "还剩",
                        value = "${daysLeft}天",
                        icon = Icons.Default.Timer,
                        color = Color(0xFF9C27B0)
                    )
                    
                    CycleStatItem(
                        label = "周期长度",
                        value = "${currentCycle.cycleDuration}天",
                        icon = Icons.Default.Loop,
                        color = Color(0xFF673AB7)
                    )
                }
            } else {
                Text(
                    text = "暂无当前周期记录",
                    style = MaterialTheme.typography.bodyMedium,
                    color = Color(0xFF2D1B20).copy(alpha = 0.6f),
                    textAlign = TextAlign.Center,
                    modifier = Modifier.fillMaxWidth()
                )
            }
        }
    }
}

@Composable
private fun CalendarCard(
    currentCycle: MenstrualCycleEntity?,
    periodPrediction: PeriodPrediction,
    ovulationPrediction: OvulationPrediction
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.White.copy(alpha = 0.95f)
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = Icons.Default.DateRange,
                    contentDescription = null,
                    tint = Color(0xFF2196F3),
                    modifier = Modifier.size(24.dp)
                )
                
                Spacer(modifier = Modifier.width(8.dp))
                
                Text(
                    text = "本月日历",
                    style = MaterialTheme.typography.titleLarge,
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF2D1B20)
                )
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // 简化的月历显示
            CalendarGrid(
                currentCycle = currentCycle,
                periodPrediction = periodPrediction,
                ovulationPrediction = ovulationPrediction
            )
        }
    }
}

@Composable
private fun CalendarGrid(
    currentCycle: MenstrualCycleEntity?,
    periodPrediction: PeriodPrediction,
    ovulationPrediction: OvulationPrediction
) {
    // 简化的7天预览
    val calendar = Calendar.getInstance()
    val today = calendar.timeInMillis
    
    Column {
        // 星期标题
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceEvenly
        ) {
            listOf("日", "一", "二", "三", "四", "五", "六").forEach { day ->
                Text(
                    text = day,
                    style = MaterialTheme.typography.bodySmall,
                    color = Color(0xFF2D1B20).copy(alpha = 0.7f),
                    textAlign = TextAlign.Center,
                    modifier = Modifier.weight(1f)
                )
            }
        }
        
        Spacer(modifier = Modifier.height(8.dp))
        
        // 本周日期
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceEvenly
        ) {
            repeat(7) { dayOffset ->
                val dayCalendar = Calendar.getInstance().apply {
                    timeInMillis = today
                    add(Calendar.DAY_OF_YEAR, dayOffset - 3) // 前3天到后3天
                }
                
                val dayOfMonth = dayCalendar.get(Calendar.DAY_OF_MONTH)
                val dayTime = dayCalendar.timeInMillis
                
                val dayType = when {
                    dayTime >= ovulationPrediction.fertileWindowStart && dayTime <= ovulationPrediction.fertileWindowEnd -> "fertile"
                    dayTime >= periodPrediction.nextPeriodDate && dayTime <= periodPrediction.nextPeriodDate + (periodPrediction.estimatedPeriodLength * 24 * 60 * 60 * 1000L) -> "period"
                    dayTime == today -> "today"
                    else -> "normal"
                }
                
                CalendarDay(
                    day = dayOfMonth,
                    type = dayType,
                    modifier = Modifier.weight(1f)
                )
            }
        }
        
        Spacer(modifier = Modifier.height(12.dp))
        
        // 图例
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceEvenly
        ) {
            CalendarLegend(
                color = Color(0xFFE91E63),
                label = "月经期"
            )
            CalendarLegend(
                color = Color(0xFF4CAF50),
                label = "易孕期"
            )
            CalendarLegend(
                color = Color(0xFF2196F3),
                label = "今天"
            )
        }
    }
}

@Composable
private fun CalendarDay(
    day: Int,
    type: String,
    modifier: Modifier = Modifier
) {
    val backgroundColor = when (type) {
        "today" -> Color(0xFF2196F3)
        "period" -> Color(0xFFE91E63)
        "fertile" -> Color(0xFF4CAF50)
        else -> Color.Transparent
    }
    
    val textColor = when (type) {
        "today", "period", "fertile" -> Color.White
        else -> Color(0xFF2D1B20)
    }
    
    Box(
        modifier = modifier
            .aspectRatio(1f)
            .clip(CircleShape)
            .background(backgroundColor),
        contentAlignment = Alignment.Center
    ) {
        Text(
            text = day.toString(),
            style = MaterialTheme.typography.bodyMedium,
            color = textColor,
            fontWeight = if (type != "normal") FontWeight.Bold else FontWeight.Normal
        )
    }
}

@Composable
private fun CalendarLegend(
    color: Color,
    label: String
) {
    Row(
        verticalAlignment = Alignment.CenterVertically
    ) {
        Box(
            modifier = Modifier
                .size(12.dp)
                .clip(CircleShape)
                .background(color)
        )
        
        Spacer(modifier = Modifier.width(4.dp))
        
        Text(
            text = label,
            style = MaterialTheme.typography.bodySmall,
            color = Color(0xFF2D1B20).copy(alpha = 0.7f)
        )
    }
}

@Composable
private fun NextPredictionCard(
    periodPrediction: PeriodPrediction,
    ovulationPrediction: OvulationPrediction
) {
    val dateFormatter = remember { SimpleDateFormat("MM月dd日", Locale.getDefault()) }
    
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.White.copy(alpha = 0.95f)
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = Icons.Default.Analytics,
                    contentDescription = null,
                    tint = Color(0xFF673AB7),
                    modifier = Modifier.size(24.dp)
                )
                
                Spacer(modifier = Modifier.width(8.dp))
                
                Text(
                    text = "下次预测",
                    style = MaterialTheme.typography.titleLarge,
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF2D1B20)
                )
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                PredictionItem(
                    icon = Icons.Default.Circle,
                    label = "下次月经",
                    date = dateFormatter.format(Date(periodPrediction.nextPeriodDate)),
                    color = Color(0xFFE91E63)
                )
                
                PredictionItem(
                    icon = Icons.Default.FiberManualRecord,
                    label = "排卵日",
                    date = dateFormatter.format(Date(ovulationPrediction.ovulationDate)),
                    color = Color(0xFF4CAF50)
                )
            }
        }
    }
}

@Composable
private fun CycleStatItem(
    label: String,
    value: String,
    icon: ImageVector,
    color: Color
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Box(
            modifier = Modifier
                .size(48.dp)
                .clip(RoundedCornerShape(12.dp))
                .background(color.copy(alpha = 0.1f)),
            contentAlignment = Alignment.Center
        ) {
            Icon(
                imageVector = icon,
                contentDescription = null,
                tint = color,
                modifier = Modifier.size(24.dp)
            )
        }
        
        Spacer(modifier = Modifier.height(8.dp))
        
        Text(
            text = value,
            style = MaterialTheme.typography.titleMedium,
            fontWeight = FontWeight.Bold,
            color = Color(0xFF2D1B20)
        )
        
        Text(
            text = label,
            style = MaterialTheme.typography.bodySmall,
            color = Color(0xFF2D1B20).copy(alpha = 0.7f),
            textAlign = TextAlign.Center
        )
    }
}

@Composable
private fun PredictionItem(
    icon: ImageVector,
    label: String,
    date: String,
    color: Color
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Icon(
            imageVector = icon,
            contentDescription = null,
            tint = color,
            modifier = Modifier.size(32.dp)
        )
        
        Spacer(modifier = Modifier.height(8.dp))
        
        Text(
            text = date,
            style = MaterialTheme.typography.titleMedium,
            fontWeight = FontWeight.Bold,
            color = Color(0xFF2D1B20)
        )
        
        Text(
            text = label,
            style = MaterialTheme.typography.bodySmall,
            color = Color(0xFF2D1B20).copy(alpha = 0.7f),
            textAlign = TextAlign.Center
        )
    }
}

// 其余预测相关的卡片组件
@Composable
private fun CurrentPhaseCard(currentPhase: CyclePhase) {
    val (phaseText, phaseColor, phaseIcon, phaseDescription) = when (currentPhase) {
        CyclePhase.MENSTRUAL -> listOf("月经期", Color(0xFFE91E63), Icons.Default.Circle, "身体正在排出子宫内膜")
        CyclePhase.FOLLICULAR -> listOf("卵泡期", Color(0xFF2196F3), Icons.Default.TrendingUp, "卵巢中的卵泡开始发育")
        CyclePhase.OVULATION -> listOf("排卵期", Color(0xFF4CAF50), Icons.Default.FiberManualRecord, "最佳受孕时机")
        CyclePhase.LUTEAL -> listOf("黄体期", Color(0xFFFF9800), Icons.Default.TrendingDown, "等待下次月经或怀孕")
        CyclePhase.UNKNOWN -> listOf("未知阶段", Color(0xFF9E9E9E), Icons.Default.Help, "需要更多数据来确定阶段")
    }
    
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.White.copy(alpha = 0.95f)
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
    ) {
        Column(
            modifier = Modifier.padding(20.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Icon(
                imageVector = phaseIcon as ImageVector,
                contentDescription = null,
                tint = phaseColor as Color,
                modifier = Modifier.size(48.dp)
            )
            
            Spacer(modifier = Modifier.height(12.dp))
            
            Text(
                text = phaseText as String,
                style = MaterialTheme.typography.headlineSmall,
                fontWeight = FontWeight.Bold,
                color = phaseColor
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Text(
                text = phaseDescription as String,
                style = MaterialTheme.typography.bodyMedium,
                color = Color(0xFF2D1B20).copy(alpha = 0.7f),
                textAlign = TextAlign.Center
            )
        }
    }
}

// 更多预测卡片组件...
@Composable
private fun PeriodPredictionCard(periodPrediction: PeriodPrediction) {
    val dateFormatter = remember { SimpleDateFormat("MM月dd日", Locale.getDefault()) }
    val daysUntil = ((periodPrediction.nextPeriodDate - System.currentTimeMillis()) / (24 * 60 * 60 * 1000L)).toInt()
    
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.White.copy(alpha = 0.95f)
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = Icons.Default.Schedule,
                    contentDescription = null,
                    tint = Color(0xFFE91E63),
                    modifier = Modifier.size(24.dp)
                )
                
                Spacer(modifier = Modifier.width(8.dp))
                
                Text(
                    text = "月经预测",
                    style = MaterialTheme.typography.titleLarge,
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF2D1B20)
                )
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            Text(
                text = if (daysUntil > 0) "还有 $daysUntil 天" else "预计今天开始",
                style = MaterialTheme.typography.headlineMedium,
                fontWeight = FontWeight.Bold,
                color = Color(0xFFE91E63),
                textAlign = TextAlign.Center,
                modifier = Modifier.fillMaxWidth()
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Text(
                text = dateFormatter.format(Date(periodPrediction.nextPeriodDate)),
                style = MaterialTheme.typography.titleMedium,
                color = Color(0xFF2D1B20),
                textAlign = TextAlign.Center,
                modifier = Modifier.fillMaxWidth()
            )
            
            Spacer(modifier = Modifier.height(12.dp))
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                Text(
                    text = "预计持续 ${periodPrediction.estimatedPeriodLength} 天",
                    style = MaterialTheme.typography.bodyMedium,
                    color = Color(0xFF2D1B20).copy(alpha = 0.7f)
                )
                
                Text(
                    text = "置信度 ${(periodPrediction.confidence * 100).roundToInt()}%",
                    style = MaterialTheme.typography.bodyMedium,
                    color = Color(0xFF2D1B20).copy(alpha = 0.7f)
                )
            }
        }
    }
}

@Composable
private fun OvulationPredictionCard(ovulationPrediction: OvulationPrediction) {
    val dateFormatter = remember { SimpleDateFormat("MM月dd日", Locale.getDefault()) }
    val daysUntil = ((ovulationPrediction.ovulationDate - System.currentTimeMillis()) / (24 * 60 * 60 * 1000L)).toInt()
    
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.White.copy(alpha = 0.95f)
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = Icons.Default.FiberManualRecord,
                    contentDescription = null,
                    tint = Color(0xFF4CAF50),
                    modifier = Modifier.size(24.dp)
                )
                
                Spacer(modifier = Modifier.width(8.dp))
                
                Text(
                    text = "排卵预测",
                    style = MaterialTheme.typography.titleLarge,
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF2D1B20)
                )
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            Text(
                text = if (daysUntil > 0) "还有 $daysUntil 天" else if (daysUntil == 0) "预计今天排卵" else "已过排卵日",
                style = MaterialTheme.typography.headlineMedium,
                fontWeight = FontWeight.Bold,
                color = Color(0xFF4CAF50),
                textAlign = TextAlign.Center,
                modifier = Modifier.fillMaxWidth()
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Text(
                text = dateFormatter.format(Date(ovulationPrediction.ovulationDate)),
                style = MaterialTheme.typography.titleMedium,
                color = Color(0xFF2D1B20),
                textAlign = TextAlign.Center,
                modifier = Modifier.fillMaxWidth()
            )
        }
    }
}

@Composable
private fun FertilityWindowCard(ovulationPrediction: OvulationPrediction) {
    val dateFormatter = remember { SimpleDateFormat("MM月dd日", Locale.getDefault()) }
    
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.White.copy(alpha = 0.95f)
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = Icons.Default.Favorite,
                    contentDescription = null,
                    tint = Color(0xFFFF9800),
                    modifier = Modifier.size(24.dp)
                )
                
                Spacer(modifier = Modifier.width(8.dp))
                
                Text(
                    text = "生育窗口",
                    style = MaterialTheme.typography.titleLarge,
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF2D1B20)
                )
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            Text(
                text = "最佳受孕时机",
                style = MaterialTheme.typography.titleMedium,
                color = Color(0xFFFF9800),
                textAlign = TextAlign.Center,
                modifier = Modifier.fillMaxWidth()
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Text(
                text = "${dateFormatter.format(Date(ovulationPrediction.fertileWindowStart))} - ${dateFormatter.format(Date(ovulationPrediction.fertileWindowEnd))}",
                style = MaterialTheme.typography.bodyLarge,
                color = Color(0xFF2D1B20),
                textAlign = TextAlign.Center,
                modifier = Modifier.fillMaxWidth()
            )
        }
    }
}

@Composable
private fun PredictionAccuracyCard(
    periodConfidence: Float,
    ovulationConfidence: Float
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.White.copy(alpha = 0.95f)
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = Icons.Default.Assessment,
                    contentDescription = null,
                    tint = Color(0xFF673AB7),
                    modifier = Modifier.size(24.dp)
                )
                
                Spacer(modifier = Modifier.width(8.dp))
                
                Text(
                    text = "预测准确性",
                    style = MaterialTheme.typography.titleLarge,
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF2D1B20)
                )
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                AccuracyIndicator(
                    label = "月经预测",
                    accuracy = periodConfidence,
                    color = Color(0xFFE91E63)
                )
                
                AccuracyIndicator(
                    label = "排卵预测",
                    accuracy = ovulationConfidence,
                    color = Color(0xFF4CAF50)
                )
            }
        }
    }
}

@Composable
private fun AccuracyIndicator(
    label: String,
    accuracy: Float,
    color: Color
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Box(
            modifier = Modifier.size(60.dp),
            contentAlignment = Alignment.Center
        ) {
            CircularProgressIndicator(
                progress = accuracy,
                modifier = Modifier.size(60.dp),
                color = color,
                trackColor = Color(0xFF2D1B20).copy(alpha = 0.1f),
                strokeWidth = 6.dp
            )
            
            Text(
                text = "${(accuracy * 100).roundToInt()}%",
                style = MaterialTheme.typography.bodySmall,
                fontWeight = FontWeight.Bold,
                color = Color(0xFF2D1B20)
            )
        }
        
        Spacer(modifier = Modifier.height(8.dp))
        
        Text(
            text = label,
            style = MaterialTheme.typography.bodySmall,
            color = Color(0xFF2D1B20).copy(alpha = 0.7f),
            textAlign = TextAlign.Center
        )
    }
}