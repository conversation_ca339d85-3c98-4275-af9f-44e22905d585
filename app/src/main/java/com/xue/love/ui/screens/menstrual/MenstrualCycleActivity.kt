package com.xue.love.ui.screens.menstrual

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import com.xue.love.CoupleApplication
import com.xue.love.ui.theme.CoupleAppTheme

class MenstrualCycleActivity : ComponentActivity() {
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        setContent {
            CoupleAppTheme {
                Surface(
                    modifier = Modifier.fillMaxSize(),
                    color = MaterialTheme.colorScheme.background
                ) {
                    // 获取Application实例和依赖容器
                    val context = LocalContext.current
                    val application = context.applicationContext as CoupleApplication
                    
                    // 创建MenstrualCycleRepository实例
                    val menstrualCycleRepository = application.container.menstrualCycleRepository
                    
                    // 手动创建ViewModel实例（因为没有使用Hilt）
                    val viewModel = MenstrualCycleViewModel(menstrualCycleRepository)
                    
                    MenstrualCycleScreen(
                        modifier = Modifier.fillMaxSize(),
                        viewModel = viewModel
                    )
                }
            }
        }
    }
}