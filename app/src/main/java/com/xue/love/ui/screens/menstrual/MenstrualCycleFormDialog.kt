package com.xue.love.ui.screens.menstrual

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import java.text.SimpleDateFormat
import java.util.*

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun MenstrualCycleFormDialog(
    form: MenstrualCycleForm,
    isEditing: Boolean,
    isSaving: Boolean,
    onFormUpdate: (MenstrualCycleForm.() -> MenstrualCycleForm) -> Unit,
    onSave: () -> Unit,
    onDismiss: () -> Unit
) {
    var selectedSection by remember { mutableStateOf(MenstrualFormSection.BASIC) }
    
    Dialog(
        onDismissRequest = onDismiss,
        properties = DialogProperties(
            usePlatformDefaultWidth = false,
            dismissOnBackPress = true,
            dismissOnClickOutside = false
        )
    ) {
        Card(
            modifier = Modifier
                .fillMaxWidth(0.95f)
                .fillMaxHeight(0.9f),
            shape = RoundedCornerShape(20.dp),
            colors = CardDefaults.cardColors(
                containerColor = Color.White
            ),
            elevation = CardDefaults.cardElevation(defaultElevation = 16.dp)
        ) {
            Column(
                modifier = Modifier.fillMaxSize()
            ) {
                // 标题栏
                MenstrualFormHeader(
                    isEditing = isEditing,
                    onDismiss = onDismiss,
                    onSave = onSave,
                    isSaving = isSaving
                )
                
                // 分节导航
                MenstrualFormSectionTabs(
                    selectedSection = selectedSection,
                    onSectionSelected = { selectedSection = it }
                )
                
                // 表单内容
                LazyColumn(
                    modifier = Modifier
                        .weight(1f)
                        .padding(16.dp),
                    verticalArrangement = Arrangement.spacedBy(16.dp)
                ) {
                    when (selectedSection) {
                        MenstrualFormSection.BASIC -> {
                            item { BasicCycleInfoSection(form, onFormUpdate) }
                        }
                        MenstrualFormSection.SYMPTOMS -> {
                            item { SymptomsSection(form, onFormUpdate) }
                        }
                        MenstrualFormSection.HEALTH -> {
                            item { HealthMetricsSection(form, onFormUpdate) }
                        }
                        MenstrualFormSection.LIFESTYLE -> {
                            item { LifestyleSection(form, onFormUpdate) }
                        }
                        MenstrualFormSection.NOTES -> {
                            item { NotesAndTagsSection(form, onFormUpdate) }
                        }
                    }
                }
            }
        }
    }
}

@Composable
private fun MenstrualFormHeader(
    isEditing: Boolean,
    onDismiss: () -> Unit,
    onSave: () -> Unit,
    isSaving: Boolean
) {
    Surface(
        color = Color(0xFFE91E63),
        modifier = Modifier.fillMaxWidth()
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                IconButton(onClick = onDismiss) {
                    Icon(
                        imageVector = Icons.Default.Close,
                        contentDescription = "关闭",
                        tint = Color.White
                    )
                }
                
                Spacer(modifier = Modifier.width(8.dp))
                
                Text(
                    text = if (isEditing) "编辑周期" else "添加周期",
                    style = MaterialTheme.typography.titleLarge,
                    fontWeight = FontWeight.Bold,
                    color = Color.White
                )
            }
            
            Button(
                onClick = onSave,
                enabled = !isSaving,
                colors = ButtonDefaults.buttonColors(
                    containerColor = Color.White,
                    contentColor = Color(0xFFE91E63)
                )
            ) {
                if (isSaving) {
                    CircularProgressIndicator(
                        modifier = Modifier.size(16.dp),
                        color = Color(0xFFE91E63),
                        strokeWidth = 2.dp
                    )
                } else {
                    Text("保存")
                }
            }
        }
    }
}

@Composable
private fun MenstrualFormSectionTabs(
    selectedSection: MenstrualFormSection,
    onSectionSelected: (MenstrualFormSection) -> Unit
) {
    LazyRow(
        modifier = Modifier
            .fillMaxWidth()
            .background(Color(0xFFF5F5F5)),
        contentPadding = PaddingValues(horizontal = 16.dp),
        horizontalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        items(MenstrualFormSection.values()) { section ->
            val isSelected = selectedSection == section
            
            Surface(
                modifier = Modifier
                    .clickable { onSectionSelected(section) }
                    .padding(vertical = 8.dp),
                shape = RoundedCornerShape(20.dp),
                color = if (isSelected) Color(0xFFE91E63) else Color.Transparent
            ) {
                Text(
                    text = when (section) {
                        MenstrualFormSection.BASIC -> "基本信息"
                        MenstrualFormSection.SYMPTOMS -> "症状记录"
                        MenstrualFormSection.HEALTH -> "健康指标"
                        MenstrualFormSection.LIFESTYLE -> "生活方式"
                        MenstrualFormSection.NOTES -> "备注标签"
                    },
                    modifier = Modifier.padding(horizontal = 16.dp, vertical = 8.dp),
                    style = MaterialTheme.typography.bodyMedium,
                    color = if (isSelected) Color.White else Color(0xFF666666),
                    fontWeight = if (isSelected) FontWeight.Bold else FontWeight.Normal
                )
            }
        }
    }
}

@Composable
private fun BasicCycleInfoSection(
    form: MenstrualCycleForm,
    onFormUpdate: (MenstrualCycleForm.() -> MenstrualCycleForm) -> Unit
) {
    Column(
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        SectionTitle("基本信息")
        
        // 开始日期
        MenstrualDatePickerField(
            label = "周期开始日期",
            timestamp = form.startDate,
            onDateChanged = { date ->
                onFormUpdate { copy(startDate = date) }
            }
        )
        
        // 结束日期（可选）
        if (form.endDate != null) {
            MenstrualDatePickerField(
                label = "周期结束日期",
                timestamp = form.endDate!!,
                onDateChanged = { date ->
                    onFormUpdate { copy(endDate = date) }
                }
            )
        } else {
            TextButton(
                onClick = { onFormUpdate { copy(endDate = System.currentTimeMillis()) } }
            ) {
                Text("+ 添加结束日期")
            }
        }
        
        // 周期长度
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            OutlinedTextField(
                value = if (form.estimatedCycleLength > 0) form.estimatedCycleLength.toString() else "",
                onValueChange = { value ->
                    val length = value.toIntOrNull() ?: 0
                    onFormUpdate { copy(estimatedCycleLength = length) }
                },
                label = { Text("预计周期长度") },
                keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                modifier = Modifier.weight(1f),
                colors = OutlinedTextFieldDefaults.colors(
                    focusedBorderColor = Color(0xFFE91E63),
                    focusedLabelColor = Color(0xFFE91E63)
                ),
                suffix = { Text("天") }
            )
            
            OutlinedTextField(
                value = if (form.estimatedPeriodLength > 0) form.estimatedPeriodLength.toString() else "",
                onValueChange = { value ->
                    val length = value.toIntOrNull() ?: 0
                    onFormUpdate { copy(estimatedPeriodLength = length) }
                },
                label = { Text("预计经期长度") },
                keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                modifier = Modifier.weight(1f),
                colors = OutlinedTextFieldDefaults.colors(
                    focusedBorderColor = Color(0xFFE91E63),
                    focusedLabelColor = Color(0xFFE91E63)
                ),
                suffix = { Text("天") }
            )
        }
        
        // 当前阶段
        PhaseSelector(
            selected = form.currentPhase,
            onSelectionChanged = { onFormUpdate { copy(currentPhase = it) } }
        )
        
        // 流量强度
        FlowIntensitySelector(
            selected = form.flowIntensity,
            onSelectionChanged = { onFormUpdate { copy(flowIntensity = it) } }
        )
    }
}

@Composable
private fun SymptomsSection(
    form: MenstrualCycleForm,
    onFormUpdate: (MenstrualCycleForm.() -> MenstrualCycleForm) -> Unit
) {
    Column(
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        SectionTitle("症状记录")
        
        // 症状选择
        SymptomsSelector(
            selectedSymptoms = form.symptoms,
            onSymptomsChanged = { onFormUpdate { copy(symptoms = it) } }
        )
        
        // 心情
        MoodSelector(
            selected = form.mood,
            onSelectionChanged = { onFormUpdate { copy(mood = it) } }
        )
        
        // 能量水平
        RatingSection(
            title = "能量水平",
            value = form.energyLevel,
            onValueChanged = { onFormUpdate { copy(energyLevel = it) } }
        )
        
        // 睡眠质量
        RatingSection(
            title = "睡眠质量",
            value = form.sleepQuality,
            onValueChanged = { onFormUpdate { copy(sleepQuality = it) } }
        )
        
        // 压力水平
        RatingSection(
            title = "压力水平",
            value = form.stressLevel,
            onValueChanged = { onFormUpdate { copy(stressLevel = it) } }
        )
    }
}

@Composable
private fun HealthMetricsSection(
    form: MenstrualCycleForm,
    onFormUpdate: (MenstrualCycleForm.() -> MenstrualCycleForm) -> Unit
) {
    Column(
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        SectionTitle("健康指标")
        
        // 体重
        OutlinedTextField(
            value = if (form.weight > 0) form.weight.toString() else "",
            onValueChange = { value ->
                val weight = value.toFloatOrNull() ?: 0f
                onFormUpdate { copy(weight = weight) }
            },
            label = { Text("体重") },
            keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Decimal),
            modifier = Modifier.fillMaxWidth(),
            colors = OutlinedTextFieldDefaults.colors(
                focusedBorderColor = Color(0xFFE91E63),
                focusedLabelColor = Color(0xFFE91E63)
            ),
            suffix = { Text("kg") }
        )
        
        // 基础体温
        OutlinedTextField(
            value = if (form.basalBodyTemperature > 0) form.basalBodyTemperature.toString() else "",
            onValueChange = { value ->
                val temperature = value.toFloatOrNull() ?: 0f
                onFormUpdate { copy(basalBodyTemperature = temperature) }
            },
            label = { Text("基础体温") },
            keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Decimal),
            modifier = Modifier.fillMaxWidth(),
            colors = OutlinedTextFieldDefaults.colors(
                focusedBorderColor = Color(0xFFE91E63),
                focusedLabelColor = Color(0xFFE91E63)
            ),
            suffix = { Text("°C") }
        )
        
        // 宫颈粘液
        CervicalMucusSelector(
            selected = form.cervicalMucus,
            onSelectionChanged = { onFormUpdate { copy(cervicalMucus = it) } }
        )
        
        // 避孕措施
        ContraceptionSection(
            used = form.contraceptionUsed,
            type = form.contraceptionType,
            onChanged = { used, type ->
                onFormUpdate { copy(contraceptionUsed = used, contraceptionType = type) }
            }
        )
        
        // 药物和补充剂
        MedicationsSection(
            medications = form.medicationsUsed,
            supplements = form.supplementsUsed,
            onMedicationsChanged = { onFormUpdate { copy(medicationsUsed = it) } },
            onSupplementsChanged = { onFormUpdate { copy(supplementsUsed = it) } }
        )
    }
}

@Composable
private fun LifestyleSection(
    form: MenstrualCycleForm,
    onFormUpdate: (MenstrualCycleForm.() -> MenstrualCycleForm) -> Unit
) {
    Column(
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        SectionTitle("生活方式")
        
        // 运动时长
        OutlinedTextField(
            value = if (form.exerciseMinutes > 0) form.exerciseMinutes.toString() else "",
            onValueChange = { value ->
                val minutes = value.toIntOrNull() ?: 0
                onFormUpdate { copy(exerciseMinutes = minutes) }
            },
            label = { Text("运动时长") },
            keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
            modifier = Modifier.fillMaxWidth(),
            colors = OutlinedTextFieldDefaults.colors(
                focusedBorderColor = Color(0xFFE91E63),
                focusedLabelColor = Color(0xFFE91E63)
            ),
            suffix = { Text("分钟") }
        )
        
        // 水分摄入
        OutlinedTextField(
            value = if (form.waterIntake > 0) form.waterIntake.toString() else "",
            onValueChange = { value ->
                val intake = value.toFloatOrNull() ?: 0f
                onFormUpdate { copy(waterIntake = intake) }
            },
            label = { Text("水分摄入") },
            keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Decimal),
            modifier = Modifier.fillMaxWidth(),
            colors = OutlinedTextFieldDefaults.colors(
                focusedBorderColor = Color(0xFFE91E63),
                focusedLabelColor = Color(0xFFE91E63)
            ),
            suffix = { Text("升") }
        )
        
        // 怀孕尝试
        Row(
            verticalAlignment = Alignment.CenterVertically
        ) {
            Checkbox(
                checked = form.isPregnancyAttempt,
                onCheckedChange = { onFormUpdate { copy(isPregnancyAttempt = it) } },
                colors = CheckboxDefaults.colors(
                    checkedColor = Color(0xFFE91E63)
                )
            )
            Text(
                text = "正在尝试怀孕",
                style = MaterialTheme.typography.bodyMedium
            )
        }
        
        // 排卵测试结果
        if (form.isPregnancyAttempt) {
            OvulationTestSelector(
                result = form.ovulationTestResult,
                onResultChanged = { onFormUpdate { copy(ovulationTestResult = it) } }
            )
            
            PregnancyTestSelector(
                result = form.pregnancyTestResult,
                onResultChanged = { onFormUpdate { copy(pregnancyTestResult = it) } }
            )
        }
    }
}

@Composable
private fun NotesAndTagsSection(
    form: MenstrualCycleForm,
    onFormUpdate: (MenstrualCycleForm.() -> MenstrualCycleForm) -> Unit
) {
    Column(
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        SectionTitle("备注标签")
        
        // 备注
        OutlinedTextField(
            value = form.notes,
            onValueChange = { onFormUpdate { copy(notes = it) } },
            label = { Text("备注") },
            placeholder = { Text("记录今天的特殊情况...") },
            modifier = Modifier.fillMaxWidth(),
            minLines = 3,
            colors = OutlinedTextFieldDefaults.colors(
                focusedBorderColor = Color(0xFFE91E63),
                focusedLabelColor = Color(0xFFE91E63)
            )
        )
        
        // 标签选择
        MenstrualTagSelector(
            selectedTags = form.tags,
            onTagsChanged = { onFormUpdate { copy(tags = it) } }
        )
        
        // 医生就诊
        Row(
            verticalAlignment = Alignment.CenterVertically
        ) {
            Checkbox(
                checked = form.doctorVisit,
                onCheckedChange = { onFormUpdate { copy(doctorVisit = it) } },
                colors = CheckboxDefaults.colors(
                    checkedColor = Color(0xFFE91E63)
                )
            )
            Text(
                text = "今日看医生",
                style = MaterialTheme.typography.bodyMedium
            )
        }
        
        if (form.doctorVisit) {
            OutlinedTextField(
                value = form.doctorNotes,
                onValueChange = { onFormUpdate { copy(doctorNotes = it) } },
                label = { Text("医生备注") },
                placeholder = { Text("记录医生的建议或诊断...") },
                modifier = Modifier.fillMaxWidth(),
                minLines = 2,
                colors = OutlinedTextFieldDefaults.colors(
                    focusedBorderColor = Color(0xFFE91E63),
                    focusedLabelColor = Color(0xFFE91E63)
                )
            )
        }
    }
}

// 辅助组件
@Composable
private fun SectionTitle(title: String) {
    Text(
        text = title,
        style = MaterialTheme.typography.titleMedium,
        fontWeight = FontWeight.Bold,
        color = Color(0xFFE91E63)
    )
}

@Composable
private fun MenstrualDatePickerField(
    label: String,
    timestamp: Long,
    onDateChanged: (Long) -> Unit
) {
    val dateFormatter = remember { SimpleDateFormat("yyyy年MM月dd日", Locale.getDefault()) }
    
    OutlinedTextField(
        value = dateFormatter.format(Date(timestamp)),
        onValueChange = { /* 只读 */ },
        label = { Text(label) },
        readOnly = true,
        modifier = Modifier.fillMaxWidth(),
        trailingIcon = {
            IconButton(onClick = { /* 打开日期选择器 */ }) {
                Icon(
                    imageVector = Icons.Default.DateRange,
                    contentDescription = "选择日期",
                    tint = Color(0xFFE91E63)
                )
            }
        },
        colors = OutlinedTextFieldDefaults.colors(
            focusedBorderColor = Color(0xFFE91E63),
            focusedLabelColor = Color(0xFFE91E63)
        )
    )
}

// 其他选择器组件暂时省略，需要时再补充
enum class MenstrualFormSection {
    BASIC, SYMPTOMS, HEALTH, LIFESTYLE, NOTES
}

// 占位符组件，实际实现时需要补充
@Composable private fun PhaseSelector(selected: String, onSelectionChanged: (String) -> Unit) { /* 实现阶段选择器 */ }
@Composable private fun FlowIntensitySelector(selected: String, onSelectionChanged: (String) -> Unit) { /* 实现流量选择器 */ }
@Composable private fun SymptomsSelector(selectedSymptoms: List<String>, onSymptomsChanged: (List<String>) -> Unit) { /* 实现症状选择器 */ }
@Composable private fun MoodSelector(selected: String, onSelectionChanged: (String) -> Unit) { /* 实现心情选择器 */ }
@Composable private fun RatingSection(title: String, value: Int, onValueChanged: (Int) -> Unit) { /* 实现评分组件 */ }
@Composable private fun CervicalMucusSelector(selected: String, onSelectionChanged: (String) -> Unit) { /* 实现宫颈粘液选择器 */ }
@Composable private fun ContraceptionSection(used: Boolean, type: String, onChanged: (Boolean, String) -> Unit) { /* 实现避孕措施组件 */ }
@Composable private fun MedicationsSection(medications: List<String>, supplements: List<String>, onMedicationsChanged: (List<String>) -> Unit, onSupplementsChanged: (List<String>) -> Unit) { /* 实现药物组件 */ }
@Composable private fun OvulationTestSelector(result: String, onResultChanged: (String) -> Unit) { /* 实现排卵测试选择器 */ }
@Composable private fun PregnancyTestSelector(result: String, onResultChanged: (String) -> Unit) { /* 实现怀孕测试选择器 */ }
@Composable private fun MenstrualTagSelector(selectedTags: List<String>, onTagsChanged: (List<String>) -> Unit) { /* 实现标签选择器 */ }