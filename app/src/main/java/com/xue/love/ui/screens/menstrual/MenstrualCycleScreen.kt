package com.xue.love.ui.screens.menstrual

import androidx.compose.animation.*
import androidx.compose.animation.core.tween
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.xue.love.data.local.entity.MenstrualCycleEntity
import com.xue.love.data.model.CyclePhase
import com.xue.love.ui.components.RomanticBackground
import com.xue.love.ui.theme.CoupleThemeType
import java.text.SimpleDateFormat
import java.util.*

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun MenstrualCycleScreen(
    modifier: Modifier = Modifier,
    viewModel: MenstrualCycleViewModel
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    val menstrualCycles by viewModel.menstrualCycles.collectAsStateWithLifecycle()
    val healthInsights by viewModel.healthInsights.collectAsStateWithLifecycle()
    val periodPrediction by viewModel.periodPrediction.collectAsStateWithLifecycle()
    val ovulationPrediction by viewModel.ovulationPrediction.collectAsStateWithLifecycle()
    
    // 处理消息显示
    LaunchedEffect(uiState.message) {
        uiState.message?.let {
            // 这里可以显示 Snackbar 或 Toast
            viewModel.clearMessage()
        }
    }
    
    Box(modifier = modifier.fillMaxSize()) {
        RomanticBackground(
            themeType = CoupleThemeType.PASSION_RED
        ) {
            Column(modifier = Modifier.fillMaxSize()) {
                // 顶部标题栏
                MenstrualTopBar(
                    currentPhase = uiState.currentPhase,
                    onAddClick = { viewModel.showCycleForm() },
                    onStartNewCycle = { viewModel.startNewCycle() }
                )
                
                // 标签栏
                MenstrualTabBar(
                    selectedTab = uiState.selectedTab,
                    onTabSelected = viewModel::setSelectedTab
                )
                
                // 内容区域
                AnimatedContent(
                    targetState = uiState.selectedTab,
                    transitionSpec = {
                        slideInHorizontally(
                            initialOffsetX = { if (targetState.ordinal > initialState.ordinal) it else -it },
                            animationSpec = tween(300)
                        ) + fadeIn(tween(300)) togetherWith
                        slideOutHorizontally(
                            targetOffsetX = { if (targetState.ordinal > initialState.ordinal) -it else it },
                            animationSpec = tween(300)
                        ) + fadeOut(tween(300))
                    },
                    label = "menstrual_tab_content"
                ) { tab ->
                    when (tab) {
                        MenstrualTab.CALENDAR -> {
                            MenstrualCalendarContent(
                                currentCycle = uiState.currentCycle,
                                periodPrediction = periodPrediction,
                                ovulationPrediction = ovulationPrediction,
                                modifier = Modifier.fillMaxSize()
                            )
                        }
                        MenstrualTab.RECORDS -> {
                            MenstrualRecordsList(
                                cycles = menstrualCycles,
                                isLoading = uiState.isLoading,
                                onEditCycle = viewModel::editCycle,
                                onDeleteCycle = viewModel::deleteCycle,
                                modifier = Modifier.fillMaxSize()
                            )
                        }
                        MenstrualTab.PREDICTIONS -> {
                            MenstrualPredictionsContent(
                                periodPrediction = periodPrediction,
                                ovulationPrediction = ovulationPrediction,
                                currentPhase = uiState.currentPhase,
                                modifier = Modifier.fillMaxSize()
                            )
                        }
                        MenstrualTab.STATISTICS -> {
                            // 暂时占位符，后续实现统计内容
                            Box(
                                modifier = Modifier.fillMaxSize(),
                                contentAlignment = Alignment.Center
                            ) {
                                Text(
                                    text = "统计功能开发中...",
                                    color = Color.White.copy(alpha = 0.7f),
                                    style = MaterialTheme.typography.bodyLarge
                                )
                            }
                        }
                        MenstrualTab.HEALTH -> {
                            // 暂时占位符，后续实现健康内容
                            Box(
                                modifier = Modifier.fillMaxSize(),
                                contentAlignment = Alignment.Center
                            ) {
                                Text(
                                    text = "健康洞察功能开发中...",
                                    color = Color.White.copy(alpha = 0.7f),
                                    style = MaterialTheme.typography.bodyLarge
                                )
                            }
                        }
                    }
                }
            }
        }
        
        // 浮动操作按钮
        FloatingActionButton(
            onClick = { viewModel.showCycleForm() },
            modifier = Modifier
                .align(Alignment.BottomEnd)
                .padding(16.dp),
            containerColor = Color(0xFFE91E63),
            contentColor = Color.White
        ) {
            Icon(
                imageVector = Icons.Default.Add,
                contentDescription = "添加记录"
            )
        }
        
        // 错误提示
        uiState.error?.let { error ->
            Card(
                modifier = Modifier
                    .align(Alignment.TopCenter)
                    .padding(16.dp)
                    .fillMaxWidth(),
                colors = CardDefaults.cardColors(
                    containerColor = Color.Red.copy(alpha = 0.9f)
                )
            ) {
                Row(
                    modifier = Modifier.padding(16.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        imageVector = Icons.Default.Error,
                        contentDescription = null,
                        tint = Color.White
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = error,
                        color = Color.White,
                        style = MaterialTheme.typography.bodyMedium,
                        modifier = Modifier.weight(1f)
                    )
                    IconButton(onClick = viewModel::clearError) {
                        Icon(
                            imageVector = Icons.Default.Close,
                            contentDescription = "关闭",
                            tint = Color.White
                        )
                    }
                }
            }
        }
    }
    
    // 表单弹窗
    if (uiState.showCycleForm) {
        MenstrualCycleFormDialog(
            form = uiState.cycleForm,
            isEditing = uiState.isEditing,
            isSaving = uiState.isSaving,
            onFormUpdate = viewModel::updateCycleForm,
            onSave = viewModel::saveCycle,
            onDismiss = viewModel::hideCycleForm
        )
    }
}

@Composable
private fun MenstrualTopBar(
    currentPhase: CyclePhase,
    onAddClick: () -> Unit,
    onStartNewCycle: () -> Unit
) {
    Surface(
        modifier = Modifier.fillMaxWidth(),
        color = Color.White.copy(alpha = 0.1f),
        shadowElevation = 4.dp
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = Icons.Default.FavoriteBorder,
                    contentDescription = null,
                    tint = Color.White,
                    modifier = Modifier.size(24.dp)
                )
                
                Spacer(modifier = Modifier.width(8.dp))
                
                Column {
                    Text(
                        text = "生理周期",
                        style = MaterialTheme.typography.headlineSmall,
                        fontWeight = FontWeight.Bold,
                        color = Color.White
                    )
                    
                    Text(
                        text = when (currentPhase) {
                            CyclePhase.MENSTRUAL -> "月经期"
                            CyclePhase.FOLLICULAR -> "卵泡期"
                            CyclePhase.OVULATION -> "排卵期"
                            CyclePhase.LUTEAL -> "黄体期"
                            CyclePhase.UNKNOWN -> "未知阶段"
                        },
                        style = MaterialTheme.typography.bodySmall,
                        color = Color.White.copy(alpha = 0.8f)
                    )
                }
            }
            
            Row {
                IconButton(
                    onClick = onStartNewCycle,
                    modifier = Modifier
                        .size(40.dp)
                        .clip(CircleShape)
                        .background(Color.White.copy(alpha = 0.2f))
                ) {
                    Icon(
                        imageVector = Icons.Default.PlayArrow,
                        contentDescription = "开始新周期",
                        tint = Color.White,
                        modifier = Modifier.size(20.dp)
                    )
                }
                
                Spacer(modifier = Modifier.width(8.dp))
                
                IconButton(
                    onClick = onAddClick,
                    modifier = Modifier
                        .size(40.dp)
                        .clip(CircleShape)
                        .background(Color.White.copy(alpha = 0.2f))
                ) {
                    Icon(
                        imageVector = Icons.Default.Add,
                        contentDescription = "添加记录",
                        tint = Color.White,
                        modifier = Modifier.size(20.dp)
                    )
                }
            }
        }
    }
}

@Composable
private fun MenstrualTabBar(
    selectedTab: MenstrualTab,
    onTabSelected: (MenstrualTab) -> Unit
) {
    Surface(
        modifier = Modifier.fillMaxWidth(),
        color = Color.White.copy(alpha = 0.05f)
    ) {
        Row(
            modifier = Modifier.fillMaxWidth()
        ) {
            MenstrualTab.values().forEach { tab ->
                val isSelected = selectedTab == tab
                Box(
                    modifier = Modifier
                        .weight(1f)
                        .clickable { onTabSelected(tab) }
                        .padding(vertical = 12.dp),
                    contentAlignment = Alignment.Center
                ) {
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Text(
                            text = when (tab) {
                                MenstrualTab.CALENDAR -> "日历"
                                MenstrualTab.RECORDS -> "记录"
                                MenstrualTab.PREDICTIONS -> "预测"
                                MenstrualTab.STATISTICS -> "统计"
                                MenstrualTab.HEALTH -> "健康"
                            },
                            style = if (isSelected) {
                                MaterialTheme.typography.titleSmall.copy(fontWeight = FontWeight.Bold)
                            } else {
                                MaterialTheme.typography.bodyMedium
                            },
                            color = if (isSelected) Color.White else Color.White.copy(alpha = 0.7f)
                        )
                        
                        if (isSelected) {
                            Box(
                                modifier = Modifier
                                    .padding(top = 4.dp)
                                    .width(24.dp)
                                    .height(2.dp)
                                    .background(
                                        Color.White,
                                        RoundedCornerShape(1.dp)
                                    )
                            )
                        }
                    }
                }
            }
        }
    }
}

@Composable
private fun MenstrualRecordsList(
    cycles: List<MenstrualCycleEntity>,
    isLoading: Boolean,
    onEditCycle: (MenstrualCycleEntity) -> Unit,
    onDeleteCycle: (String) -> Unit,
    modifier: Modifier = Modifier
) {
    LazyColumn(
        modifier = modifier,
        contentPadding = PaddingValues(16.dp),
        verticalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        if (isLoading) {
            item {
                Box(
                    modifier = Modifier.fillMaxWidth(),
                    contentAlignment = Alignment.Center
                ) {
                    CircularProgressIndicator(color = Color.White)
                }
            }
        }
        
        if (cycles.isEmpty() && !isLoading) {
            item {
                EmptyRecordsPlaceholder()
            }
        }
        
        items(cycles, key = { it.id }) { cycle ->
            MenstrualCycleCard(
                cycle = cycle,
                onEdit = { onEditCycle(cycle) },
                onDelete = { onDeleteCycle(cycle.id) }
            )
        }
    }
}

@Composable
private fun MenstrualCycleCard(
    cycle: MenstrualCycleEntity,
    onEdit: () -> Unit,
    onDelete: () -> Unit
) {
    val dateFormatter = remember { SimpleDateFormat("MM月dd日", Locale.getDefault()) }
    
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.White.copy(alpha = 0.95f)
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            // 头部信息
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column {
                    Text(
                        text = dateFormatter.format(Date(cycle.cycleStartDate)),
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold,
                        color = Color(0xFF2D1B20)
                    )
                    Text(
                        text = "周期 ${cycle.cycleDuration} 天",
                        style = MaterialTheme.typography.bodySmall,
                        color = Color(0xFF2D1B20).copy(alpha = 0.7f)
                    )
                }
                
                Row {
                    IconButton(onClick = onEdit) {
                        Icon(
                            imageVector = Icons.Default.Edit,
                            contentDescription = "编辑",
                            tint = Color(0xFFE91E63)
                        )
                    }
                    IconButton(onClick = onDelete) {
                        Icon(
                            imageVector = Icons.Default.Delete,
                            contentDescription = "删除",
                            tint = Color(0xFFE91E63)
                        )
                    }
                }
            }
            
            Spacer(modifier = Modifier.height(12.dp))
            
            // 周期阶段
            PhaseIndicator(
                phase = "menstruation", // 暂时硬编码，后续计算实际阶段
                flowIntensity = cycle.flowLevel
            )
            
            // 症状和心情
            if (cycle.cramps > 0 || cycle.headache > 0 || cycle.mood.isNotEmpty()) {
                Spacer(modifier = Modifier.height(8.dp))
                
                if (cycle.cramps > 0 || cycle.headache > 0) {
                    val symptoms = mutableListOf<String>()
                    if (cycle.cramps > 0) symptoms.add("痛经(${cycle.cramps}/10)")
                    if (cycle.headache > 0) symptoms.add("头痛(${cycle.headache}/10)")
                    
                    if (symptoms.isNotEmpty()) {
                        Text(
                            text = "症状: ${symptoms.joinToString(", ")}",
                            style = MaterialTheme.typography.bodySmall,
                            color = Color(0xFF2D1B20).copy(alpha = 0.7f)
                        )
                    }
                }
                
                if (cycle.mood.isNotEmpty()) {
                    Text(
                        text = "心情: ${cycle.mood}",
                        style = MaterialTheme.typography.bodySmall,
                        color = Color(0xFF2D1B20).copy(alpha = 0.7f)
                    )
                }
            }
        }
    }
}

@Composable
private fun PhaseIndicator(
    phase: String,
    flowIntensity: String
) {
    val (phaseText, phaseColor) = when (phase) {
        "menstruation" -> "月经期" to Color(0xFFE91E63)
        "follicular" -> "卵泡期" to Color(0xFF2196F3)
        "ovulation" -> "排卵期" to Color(0xFF4CAF50)
        "luteal" -> "黄体期" to Color(0xFFFF9800)
        else -> "未知" to Color(0xFF9E9E9E)
    }
    
    Row(
        verticalAlignment = Alignment.CenterVertically
    ) {
        Surface(
            shape = RoundedCornerShape(12.dp),
            color = phaseColor.copy(alpha = 0.1f)
        ) {
            Text(
                text = phaseText,
                modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp),
                style = MaterialTheme.typography.bodySmall,
                color = phaseColor,
                fontWeight = FontWeight.Medium
            )
        }
        
        if (flowIntensity.isNotEmpty()) {
            Spacer(modifier = Modifier.width(8.dp))
            Surface(
                shape = RoundedCornerShape(12.dp),
                color = Color(0xFF9C27B0).copy(alpha = 0.1f)
            ) {
                Text(
                    text = "强度: $flowIntensity",
                    modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp),
                    style = MaterialTheme.typography.bodySmall,
                    color = Color(0xFF9C27B0),
                    fontWeight = FontWeight.Medium
                )
            }
        }
    }
}

@Composable
private fun EmptyRecordsPlaceholder() {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(32.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Icon(
            imageVector = Icons.Default.FavoriteBorder,
            contentDescription = null,
            tint = Color.White.copy(alpha = 0.5f),
            modifier = Modifier.size(64.dp)
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        Text(
            text = "还没有记录",
            style = MaterialTheme.typography.headlineSmall,
            color = Color.White.copy(alpha = 0.7f),
            textAlign = TextAlign.Center
        )
        
        Spacer(modifier = Modifier.height(8.dp))
        
        Text(
            text = "开始记录你的生理周期吧",
            style = MaterialTheme.typography.bodyMedium,
            color = Color.White.copy(alpha = 0.5f),
            textAlign = TextAlign.Center
        )
    }
}