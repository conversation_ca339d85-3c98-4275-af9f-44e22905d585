package com.xue.love.ui.screens.menstrual

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.xue.love.data.local.entity.MenstrualCycleEntity
import com.xue.love.data.repository.MenstrualCycleRepository
import com.xue.love.data.model.MenstrualHealthInsights
import com.xue.love.data.model.PeriodPrediction
import com.xue.love.data.model.OvulationPrediction
import com.xue.love.data.model.CyclePhase
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import java.util.UUID

class MenstrualCycleViewModel(
    private val repository: MenstrualCycleRepository
) : ViewModel() {
    
    // UI 状态
    private val _uiState = MutableStateFlow(MenstrualCycleUiState())
    val uiState: StateFlow<MenstrualCycleUiState> = _uiState.asStateFlow()
    
    // 数据流
    private val _currentUserId = MutableStateFlow("user1") // 临时硬编码，后续从用户管理获取
    
    val menstrualCycles = _currentUserId.flatMapLatest { userId ->
        repository.getCyclesByUser(userId)
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(5000),
        initialValue = emptyList()
    )
    
    val recentCycles = _currentUserId.flatMapLatest { userId ->
        flow { emit(repository.getRecentCycles(userId, 6)) }
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(5000),
        initialValue = emptyList()
    )
    
    val healthInsights = _currentUserId.flatMapLatest { userId ->
        flow { 
            // 暂时返回默认值，后续实现getHealthInsights方法
            emit(MenstrualHealthInsights(0, 28f, 0.5f, CyclePhase.UNKNOWN, 0.5f, System.currentTimeMillis()))
        }
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(5000),
        initialValue = MenstrualHealthInsights(0, 28f, 0.5f, CyclePhase.UNKNOWN, 0.5f, null)
    )
    
    val periodPrediction = _currentUserId.flatMapLatest { userId ->
        flow { 
            // 暂时返回默认值，后续实现predictNextPeriod方法
            emit(PeriodPrediction(System.currentTimeMillis() + 7 * 24 * 60 * 60 * 1000L, 0.5f, 28, 5))
        }
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(5000),
        initialValue = PeriodPrediction(System.currentTimeMillis(), 0.5f, 28, 5)
    )
    
    val ovulationPrediction = _currentUserId.flatMapLatest { userId ->
        flow { 
            // 暂时返回默认值，后续实现predictOvulation方法
            val now = System.currentTimeMillis()
            emit(OvulationPrediction(now + 14 * 24 * 60 * 60 * 1000L, now + 12 * 24 * 60 * 60 * 1000L, now + 16 * 24 * 60 * 60 * 1000L, 0.5f))
        }
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(5000),
        initialValue = OvulationPrediction(System.currentTimeMillis(), System.currentTimeMillis(), System.currentTimeMillis(), 0.5f)
    )
    
    init {
        loadData()
    }
    
    private fun loadData() {
        viewModelScope.launch {
            try {
                _uiState.update { it.copy(isLoading = true, error = null) }
                
                val userId = _currentUserId.value
                val currentCycle = repository.getCurrentCycle(userId)
                // 暂时硬编码当前阶段，后续实现getCurrentPhase方法
                val currentPhase = CyclePhase.UNKNOWN
                
                _uiState.update { currentState ->
                    currentState.copy(
                        isLoading = false,
                        currentCycle = currentCycle,
                        currentPhase = currentPhase
                    )
                }
            } catch (e: Exception) {
                _uiState.update { 
                    it.copy(isLoading = false, error = "加载数据失败: ${e.message}")
                }
            }
        }
    }
    
    // 表单状态管理
    fun updateCycleForm(update: (MenstrualCycleForm) -> MenstrualCycleForm) {
        _uiState.update { currentState ->
            currentState.copy(cycleForm = update(currentState.cycleForm))
        }
    }
    
    fun clearForm() {
        _uiState.update { it.copy(cycleForm = MenstrualCycleForm()) }
    }
    
    // 保存周期记录
    fun saveCycle() {
        viewModelScope.launch {
            try {
                _uiState.update { it.copy(isSaving = true, error = null) }
                
                val form = _uiState.value.cycleForm
                val cycle = MenstrualCycleEntity(
                    id = form.id.ifEmpty { UUID.randomUUID().toString() },
                    userId = _currentUserId.value,
                    cycleStartDate = form.startDate,
                    cycleEndDate = form.endDate,
                    periodStartDate = form.startDate, // 暂时使用startDate作为periodStartDate
                    periodEndDate = form.endDate,
                    periodDuration = form.actualPeriodLength,
                    cycleDuration = form.estimatedCycleLength,
                    flowLevel = form.flowIntensity,
                    mood = form.mood,
                    energyLevel = form.energyLevel,
                    sleepQuality = form.sleepQuality,
                    stressLevel = form.stressLevel
                    // 其他字段使用默认值
                )
                
                if (form.id.isEmpty()) {
                    repository.insertCycle(cycle)
                } else {
                    repository.updateCycle(cycle)
                }
                
                _uiState.update { 
                    it.copy(
                        isSaving = false, 
                        cycleForm = MenstrualCycleForm(),
                        showCycleForm = false
                    )
                }
                
                showMessage("周期记录保存成功")
                
            } catch (e: Exception) {
                _uiState.update { 
                    it.copy(isSaving = false, error = "保存失败: ${e.message}")
                }
            }
        }
    }
    
    // 删除记录
    fun deleteCycle(cycleId: String) {
        viewModelScope.launch {
            try {
                repository.deleteCycleById(cycleId)
                showMessage("记录已删除")
            } catch (e: Exception) {
                _uiState.update { 
                    it.copy(error = "删除失败: ${e.message}")
                }
            }
        }
    }
    
    // 编辑记录
    fun editCycle(cycle: MenstrualCycleEntity) {
        val form = MenstrualCycleForm(
            id = cycle.id,
            startDate = cycle.cycleStartDate,
            endDate = cycle.cycleEndDate,
            estimatedCycleLength = cycle.cycleDuration,
            actualCycleLength = cycle.cycleDuration,
            estimatedPeriodLength = cycle.periodDuration,
            actualPeriodLength = cycle.periodDuration,
            currentPhase = "menstruation", // 暂时硬编码
            flowIntensity = cycle.flowLevel,
            symptoms = emptyList(), // 暂时为空，后续需要实现症状解析
            mood = cycle.mood,
            energyLevel = cycle.energyLevel,
            sleepQuality = cycle.sleepQuality,
            exerciseMinutes = 0, // 暂时使用默认值
            waterIntake = 0f,
            weight = 0f,
            basalBodyTemperature = cycle.basalBodyTemperature,
            cervicalMucus = cycle.cervicalMucus,
            cervicalPosition = "",
            contraceptionUsed = false,
            contraceptionType = "",
            medicationsUsed = emptyList(),
            supplementsUsed = emptyList(),
            stressLevel = cycle.stressLevel,
            notes = "",
            tags = emptyList(),
            isPregnancyAttempt = false,
            ovulationTestResult = "",
            pregnancyTestResult = "",
            doctorVisit = false,
            doctorNotes = ""
        )
        
        _uiState.update { 
            it.copy(cycleForm = form, showCycleForm = true, isEditing = true)
        }
    }
    
    // UI 操作
    fun showCycleForm() {
        _uiState.update { it.copy(showCycleForm = true, isEditing = false) }
    }
    
    fun hideCycleForm() {
        _uiState.update { it.copy(showCycleForm = false, cycleForm = MenstrualCycleForm()) }
    }
    
    fun setSelectedTab(tab: MenstrualTab) {
        _uiState.update { it.copy(selectedTab = tab) }
    }
    
    // 搜索和过滤
    fun searchCycles(query: String) {
        _uiState.update { it.copy(searchQuery = query) }
        
        if (query.isBlank()) {
            return
        }
        
        viewModelScope.launch {
            try {
                // 暂时注释掉搜索功能，因为repository.searchCycles方法不存在
                // val results = repository.searchCycles(_currentUserId.value, query).first()
                // _uiState.update { it.copy(searchResults = results) }
                _uiState.update { it.copy(searchResults = emptyList()) }
            } catch (e: Exception) {
                _uiState.update { it.copy(error = "搜索失败: ${e.message}") }
            }
        }
    }
    
    fun clearSearch() {
        _uiState.update { it.copy(searchQuery = "", searchResults = emptyList()) }
    }
    
    // 统计数据
    fun loadStatistics() {
        viewModelScope.launch {
            try {
                val userId = _currentUserId.value
                // 暂时使用空数据，因为相关repository方法不存在
                // val symptomStats = repository.getSymptomStatistics(userId)
                // val moodStats = repository.getMoodStatistics(userId)
                // val flowStats = repository.getFlowIntensityStatistics(userId)
                // val monthlyStats = repository.getMonthlyStatistics(userId, 12)
                
                _uiState.update { currentState ->
                    currentState.copy(
                        statisticsData = MenstrualStatisticsData(
                            symptomStatistics = emptyList(),
                            moodStatistics = emptyList(),
                            flowStatistics = emptyList(),
                            monthlyStatistics = emptyList()
                        )
                    )
                }
            } catch (e: Exception) {
                _uiState.update { it.copy(error = "加载统计数据失败: ${e.message}") }
            }
        }
    }
    
    // 开始新周期
    fun startNewCycle() {
        viewModelScope.launch {
            try {
                val userId = _currentUserId.value
                // 暂时注释掉，因为repository.createNewCycle方法不存在
                // val newCycle = repository.createNewCycle(
                //     userId = userId,
                //     startDate = System.currentTimeMillis()
                // )
                
                // 创建一个简单的新周期
                val newCycle = MenstrualCycleEntity(
                    id = UUID.randomUUID().toString(),
                    userId = userId,
                    cycleStartDate = System.currentTimeMillis(),
                    periodStartDate = System.currentTimeMillis()
                )
                
                _uiState.update { it.copy(currentCycle = newCycle) }
                showMessage("新周期已开始")
                
            } catch (e: Exception) {
                _uiState.update { it.copy(error = "开始新周期失败: ${e.message}") }
            }
        }
    }
    
    // 结束当前周期
    fun endCurrentCycle() {
        viewModelScope.launch {
            try {
                val currentCycle = _uiState.value.currentCycle
                if (currentCycle != null) {
                    val endedCycle = currentCycle.copy(
                        cycleEndDate = System.currentTimeMillis(),
                        cycleDuration = ((System.currentTimeMillis() - currentCycle.cycleStartDate) / (24 * 60 * 60 * 1000L)).toInt()
                    )
                    repository.updateCycle(endedCycle)
                    _uiState.update { it.copy(currentCycle = null) }
                    showMessage("当前周期已结束")
                }
            } catch (e: Exception) {
                _uiState.update { it.copy(error = "结束周期失败: ${e.message}") }
            }
        }
    }
    
    // 消息处理
    private fun showMessage(message: String) {
        _uiState.update { it.copy(message = message) }
    }
    
    fun clearMessage() {
        _uiState.update { it.copy(message = null) }
    }
    
    fun clearError() {
        _uiState.update { it.copy(error = null) }
    }
}

// UI 状态数据类
data class MenstrualCycleUiState(
    val isLoading: Boolean = false,
    val isSaving: Boolean = false,
    val error: String? = null,
    val message: String? = null,
    val selectedTab: MenstrualTab = MenstrualTab.CALENDAR,
    val showCycleForm: Boolean = false,
    val isEditing: Boolean = false,
    val cycleForm: MenstrualCycleForm = MenstrualCycleForm(),
    val searchQuery: String = "",
    val searchResults: List<MenstrualCycleEntity> = emptyList(),
    val statisticsData: MenstrualStatisticsData = MenstrualStatisticsData(),
    val currentCycle: MenstrualCycleEntity? = null,
    val currentPhase: CyclePhase = CyclePhase.UNKNOWN
)

// 表单数据类
data class MenstrualCycleForm(
    val id: String = "",
    val startDate: Long = System.currentTimeMillis(),
    val endDate: Long? = null,
    val estimatedCycleLength: Int = 28,
    val actualCycleLength: Int = 0,
    val estimatedPeriodLength: Int = 5,
    val actualPeriodLength: Int = 0,
    val currentPhase: String = "menstruation",
    val flowIntensity: String = "",
    val symptoms: List<String> = emptyList(),
    val mood: String = "",
    val energyLevel: Int = 5,
    val sleepQuality: Int = 5,
    val exerciseMinutes: Int = 0,
    val waterIntake: Float = 0f,
    val weight: Float = 0f,
    val basalBodyTemperature: Float = 0f,
    val cervicalMucus: String = "",
    val cervicalPosition: String = "",
    val contraceptionUsed: Boolean = false,
    val contraceptionType: String = "",
    val medicationsUsed: List<String> = emptyList(),
    val supplementsUsed: List<String> = emptyList(),
    val stressLevel: Int = 5,
    val notes: String = "",
    val tags: List<String> = emptyList(),
    val isPregnancyAttempt: Boolean = false,
    val ovulationTestResult: String = "",
    val pregnancyTestResult: String = "",
    val doctorVisit: Boolean = false,
    val doctorNotes: String = ""
)

// 标签页枚举
enum class MenstrualTab {
    CALENDAR,     // 日历视图
    RECORDS,      // 记录列表
    PREDICTIONS,  // 预测分析
    STATISTICS,   // 统计数据
    HEALTH        // 健康洞察
}

// 统计数据
data class MenstrualStatisticsData(
    val symptomStatistics: List<com.xue.love.data.local.dao.SymptomCount> = emptyList(),
    val moodStatistics: List<com.xue.love.data.local.dao.MoodCount> = emptyList(),
    val flowStatistics: List<com.xue.love.data.local.dao.FlowIntensityCount> = emptyList(),
    val monthlyStatistics: List<com.xue.love.data.local.dao.MonthStatistics> = emptyList()
)