package com.xue.love.ui.screens.menstrual

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.xue.love.data.local.entity.MenstrualCycleEntity
import com.xue.love.data.model.MenstrualHealthInsights
import com.xue.love.data.model.CyclePhase
import com.xue.love.data.common.SymptomCount
import com.xue.love.data.common.MoodCount
import kotlin.math.roundToInt

/**
 * 生理健康分析组件
 * 包含周期分析、症状统计、心情追踪等功能
 */

@Composable
fun MenstrualHealthAnalysis(
    cycles: List<MenstrualCycleEntity>,
    modifier: Modifier = Modifier
) {
    val healthInsights = calculateHealthInsights(cycles)
    
    Column(
        modifier = modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // 健康概览卡片
        HealthOverviewCard(healthInsights)
        
        // 周期规律性分析
        CycleRegularityCard(healthInsights)
        
        // 症状统计
        if (cycles.isNotEmpty()) {
            val symptomStats = calculateSymptomStatistics(cycles)
            SymptomStatisticsCard(symptomStats)
        }
        
        // 心情分析
        if (cycles.isNotEmpty()) {
            val moodStats = calculateMoodStatistics(cycles)
            MoodStatisticsCard(moodStats)
        }
        
        // 健康建议
        HealthRecommendationsCard(healthInsights, cycles)
    }
}

@Composable
private fun HealthOverviewCard(
    healthInsights: MenstrualHealthInsights
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.White.copy(alpha = 0.95f)
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
    ) {
        Column(
            modifier = Modifier.padding(20.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = Icons.Default.HealthAndSafety,
                    contentDescription = null,
                    tint = Color(0xFFE91E63),
                    modifier = Modifier.size(24.dp)
                )
                
                Spacer(modifier = Modifier.width(8.dp))
                
                Text(
                    text = "健康概览",
                    style = MaterialTheme.typography.titleLarge,
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF2D1B20)
                )
            }
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                HealthMetricItem(
                    label = "平均周期",
                    value = "${healthInsights.averageCycleLength.roundToInt()}天",
                    color = Color(0xFF4CAF50)
                )
                
                HealthMetricItem(
                    label = "规律性",
                    value = "${(healthInsights.cycleRegularity * 100).roundToInt()}%",
                    color = getRegularityColor(healthInsights.cycleRegularity)
                )
                
                HealthMetricItem(
                    label = "记录周期",
                    value = "${healthInsights.totalCycles}个",
                    color = Color(0xFF2196F3)
                )
            }
        }
    }
}

@Composable
private fun HealthMetricItem(
    label: String,
    value: String,
    color: Color
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = value,
            style = MaterialTheme.typography.headlineSmall,
            fontWeight = FontWeight.Bold,
            color = color
        )
        
        Text(
            text = label,
            style = MaterialTheme.typography.bodySmall,
            color = Color(0xFF2D1B20).copy(alpha = 0.7f)
        )
    }
}

@Composable
private fun CycleRegularityCard(
    healthInsights: MenstrualHealthInsights
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.White.copy(alpha = 0.95f)
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = Icons.Default.DateRange,
                    contentDescription = null,
                    tint = getRegularityColor(healthInsights.cycleRegularity),
                    modifier = Modifier.size(24.dp)
                )
                
                Spacer(modifier = Modifier.width(8.dp))
                
                Text(
                    text = "周期规律性",
                    style = MaterialTheme.typography.titleLarge,
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF2D1B20)
                )
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            LinearProgressIndicator(
                progress = healthInsights.cycleRegularity,
                modifier = Modifier.fillMaxWidth(),
                color = getRegularityColor(healthInsights.cycleRegularity),
                trackColor = Color(0xFF2D1B20).copy(alpha = 0.1f)
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Text(
                text = getRegularityDescription(healthInsights.cycleRegularity),
                style = MaterialTheme.typography.bodyMedium,
                color = Color(0xFF2D1B20).copy(alpha = 0.8f)
            )
        }
    }
}

@Composable
private fun SymptomStatisticsCard(
    symptomStats: List<SymptomCount>
) {
    if (symptomStats.isEmpty()) return
    
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.White.copy(alpha = 0.95f)
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = Icons.Default.LocalHospital,
                    contentDescription = null,
                    tint = Color(0xFFE91E63),
                    modifier = Modifier.size(24.dp)
                )
                
                Spacer(modifier = Modifier.width(8.dp))
                
                Text(
                    text = "症状统计",
                    style = MaterialTheme.typography.titleLarge,
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF2D1B20)
                )
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            val totalSymptoms = symptomStats.sumOf { it.count }
            symptomStats.forEach { symptom ->
                SymptomStatRow(
                    symptom = symptom.symptom,
                    count = symptom.count,
                    total = totalSymptoms
                )
                
                Spacer(modifier = Modifier.height(8.dp))
            }
        }
    }
}

@Composable
private fun SymptomStatRow(
    symptom: String,
    count: Int,
    total: Int
) {
    val percentage = if (total > 0) (count.toFloat() / total * 100).roundToInt() else 0
    
    Column {
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = symptom,
                style = MaterialTheme.typography.bodyMedium,
                color = Color(0xFF2D1B20),
                modifier = Modifier.weight(1f)
            )
            
            Text(
                text = "${count}次 (${percentage}%)",
                style = MaterialTheme.typography.bodySmall,
                color = Color(0xFF2D1B20).copy(alpha = 0.7f)
            )
        }
        
        LinearProgressIndicator(
            progress = count.toFloat() / total,
            modifier = Modifier
                .fillMaxWidth()
                .padding(top = 4.dp),
            color = getSymptomColor(symptom),
            trackColor = Color(0xFF2D1B20).copy(alpha = 0.1f)
        )
    }
}

@Composable
private fun MoodStatisticsCard(
    moodStats: List<MoodCount>
) {
    if (moodStats.isEmpty()) return
    
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.White.copy(alpha = 0.95f)
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = Icons.Default.Mood,
                    contentDescription = null,
                    tint = Color(0xFFFF9800),
                    modifier = Modifier.size(24.dp)
                )
                
                Spacer(modifier = Modifier.width(8.dp))
                
                Text(
                    text = "心情分析",
                    style = MaterialTheme.typography.titleLarge,
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF2D1B20)
                )
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            LazyRow(
                horizontalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                items(moodStats) { moodStat ->
                    MoodChip(
                        mood = moodStat.mood,
                        count = moodStat.count
                    )
                }
            }
        }
    }
}

@Composable
private fun MoodChip(
    mood: String,
    count: Int
) {
    Box(
        modifier = Modifier
            .background(
                color = getMoodColor(mood).copy(alpha = 0.1f),
                shape = RoundedCornerShape(16.dp)
            )
            .padding(horizontal = 12.dp, vertical = 6.dp)
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = getMoodEmoji(mood),
                fontSize = 20.sp
            )
            
            Text(
                text = mood,
                style = MaterialTheme.typography.bodySmall,
                color = getMoodColor(mood),
                fontWeight = FontWeight.Medium
            )
            
            Text(
                text = "${count}次",
                style = MaterialTheme.typography.labelSmall,
                color = Color(0xFF2D1B20).copy(alpha = 0.6f)
            )
        }
    }
}

@Composable
private fun HealthRecommendationsCard(
    healthInsights: MenstrualHealthInsights,
    recentCycles: List<MenstrualCycleEntity>
) {
    val recommendations = generateHealthRecommendations(healthInsights, recentCycles)
    
    if (recommendations.isEmpty()) return
    
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.White.copy(alpha = 0.95f)
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = Icons.Default.Lightbulb,
                    contentDescription = null,
                    tint = Color(0xFF2196F3),
                    modifier = Modifier.size(24.dp)
                )
                
                Spacer(modifier = Modifier.width(8.dp))
                
                Text(
                    text = "健康建议",
                    style = MaterialTheme.typography.titleLarge,
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF2D1B20)
                )
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            recommendations.forEach { recommendation ->
                RecommendationItem(recommendation)
                Spacer(modifier = Modifier.height(12.dp))
            }
        }
    }
}

@Composable
private fun RecommendationItem(
    recommendation: HealthRecommendation
) {
    Row(
        verticalAlignment = Alignment.Top,
        horizontalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        Icon(
            imageVector = recommendation.icon,
            contentDescription = null,
            tint = recommendation.color,
            modifier = Modifier.size(20.dp)
        )
        
        Column(
            modifier = Modifier.weight(1f)
        ) {
            Text(
                text = recommendation.title,
                style = MaterialTheme.typography.bodyMedium,
                fontWeight = FontWeight.Medium,
                color = Color(0xFF2D1B20)
            )
            
            Text(
                text = recommendation.description,
                style = MaterialTheme.typography.bodySmall,
                color = Color(0xFF2D1B20).copy(alpha = 0.7f)
            )
        }
    }
}

// 数据计算函数
private fun calculateHealthInsights(cycles: List<MenstrualCycleEntity>): MenstrualHealthInsights {
    if (cycles.isEmpty()) {
        return MenstrualHealthInsights(
            totalCycles = 0,
            averageCycleLength = 28f,
            cycleRegularity = 0f,
            currentPhase = CyclePhase.UNKNOWN,
            healthScore = 0f,
            lastUpdated = null
        )
    }
    
    val cycleLengths = cycles.map { it.cycleDuration.toFloat() }
    val averageLength = cycleLengths.average().toFloat()
    
    // 计算规律性（基于标准差）
    val variance = cycleLengths.map { (it - averageLength) * (it - averageLength) }.average().toFloat()
    val standardDeviation = kotlin.math.sqrt(variance.toDouble()).toFloat()
    val regularity = (1f - (standardDeviation / averageLength).coerceAtMost(1f)).coerceAtLeast(0f)
    
    return MenstrualHealthInsights(
        totalCycles = cycles.size,
        averageCycleLength = averageLength,
        cycleRegularity = regularity,
        currentPhase = com.xue.love.data.model.CyclePhase.UNKNOWN,
        healthScore = regularity,
        lastUpdated = System.currentTimeMillis()
    )
}

private fun calculateSymptomStatistics(cycles: List<MenstrualCycleEntity>): List<SymptomCount> {
    val symptomCounts = mutableMapOf<String, Int>()
    
    cycles.forEach { cycle ->
        // 收集各种症状信息
        if (cycle.cramps > 0) symptomCounts["痛经"] = symptomCounts.getOrDefault("痛经", 0) + 1
        if (cycle.headache > 0) symptomCounts["头痛"] = symptomCounts.getOrDefault("头痛", 0) + 1
        if (cycle.moodSwings > 0) symptomCounts["情绪波动"] = symptomCounts.getOrDefault("情绪波动", 0) + 1
        if (cycle.bloating > 0) symptomCounts["腹胀"] = symptomCounts.getOrDefault("腹胀", 0) + 1
        if (cycle.breastTenderness > 0) symptomCounts["乳房胀痛"] = symptomCounts.getOrDefault("乳房胀痛", 0) + 1
        if (cycle.fatigue > 0) symptomCounts["疲劳"] = symptomCounts.getOrDefault("疲劳", 0) + 1
        if (cycle.acne) symptomCounts["痤疮"] = symptomCounts.getOrDefault("痤疮", 0) + 1
    }
    
    return symptomCounts.map { SymptomCount(it.key, it.value) }
        .sortedByDescending { it.count }
}

private fun calculateMoodStatistics(cycles: List<MenstrualCycleEntity>): List<MoodCount> {
    return cycles.mapNotNull { cycle ->
        cycle.mood.takeIf { it.isNotEmpty() }
    }.groupingBy { it }.eachCount()
        .map { MoodCount(it.key, it.value) }
        .sortedByDescending { it.count }
}

private fun generateHealthRecommendations(
    healthInsights: MenstrualHealthInsights,
    recentCycles: List<MenstrualCycleEntity>
): List<HealthRecommendation> {
    val recommendations = mutableListOf<HealthRecommendation>()
    
    // 基于规律性的建议
    if (healthInsights.cycleRegularity < 0.7f) {
        recommendations.add(
            HealthRecommendation(
                icon = Icons.Default.DateRange,
                title = "改善周期规律性",
                description = "保持规律作息，适度运动，减少压力",
                color = Color(0xFFFF9800)
            )
        )
    }
    
    // 基于周期长度的建议
    if (healthInsights.averageCycleLength < 21f || healthInsights.averageCycleLength > 35f) {
        recommendations.add(
            HealthRecommendation(
                icon = Icons.Default.Warning,
                title = "关注周期长度",
                description = "周期长度异常，建议咨询妇科医生",
                color = Color(0xFFE91E63)
            )
        )
    }
    
    // 基于数据完整性的建议
    if (healthInsights.totalCycles < 3) {
        recommendations.add(
            HealthRecommendation(
                icon = Icons.Default.Event,
                title = "持续记录",
                description = "坚持记录有助于更好地了解你的身体",
                color = Color(0xFF2196F3)
            )
        )
    }
    
    return recommendations
}

// 辅助函数
private fun getRegularityColor(regularity: Float): Color {
    return when {
        regularity >= 0.8f -> Color(0xFF4CAF50)
        regularity >= 0.6f -> Color(0xFFFF9800)
        else -> Color(0xFFE91E63)
    }
}

private fun getRegularityDescription(regularity: Float): String {
    return when {
        regularity >= 0.8f -> "周期非常规律，身体状态良好"
        regularity >= 0.6f -> "周期较为规律，继续保持"
        regularity >= 0.4f -> "周期有些不规律，注意调节生活作息"
        else -> "周期不规律，建议咨询医生"
    }
}

private fun getSymptomColor(symptom: String): Color {
    return when (symptom.lowercase()) {
        "头痛", "腹痛", "腰痛" -> Color(0xFFE91E63)
        "疲劳", "乏力" -> Color(0xFFFF9800)
        "情绪波动", "易怒" -> Color(0xFF9C27B0)
        else -> Color(0xFF2196F3)
    }
}

private fun getMoodColor(mood: String): Color {
    return when (mood.lowercase()) {
        "开心", "愉快", "平静" -> Color(0xFF4CAF50)
        "焦虑", "紧张", "烦躁" -> Color(0xFFE91E63)
        "疲惫", "低落" -> Color(0xFF9E9E9E)
        else -> Color(0xFF2196F3)
    }
}

private fun getMoodEmoji(mood: String): String {
    return when (mood.lowercase()) {
        "开心", "愉快" -> "😊"
        "平静" -> "😌"
        "焦虑", "紧张" -> "😰"
        "烦躁", "易怒" -> "😠"
        "疲惫" -> "😴"
        "低落" -> "😔"
        else -> "😐"
    }
}


data class HealthRecommendation(
    val icon: ImageVector,
    val title: String,
    val description: String,
    val color: Color
)