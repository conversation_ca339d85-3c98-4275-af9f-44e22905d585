package com.xue.love.ui.screens.profile

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Switch
import androidx.compose.material3.SwitchDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
// 暂时移除Hilt依赖
// import androidx.hilt.navigation.compose.hiltViewModel
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.xue.love.R
import com.xue.love.ui.components.CoupleStatsCard
import com.xue.love.ui.components.RomanticBackground
import com.xue.love.ui.components.RomanticCard
import com.xue.love.ui.components.ThemeSelector
import com.xue.love.ui.theme.CoupleAppTheme
import com.xue.love.ui.theme.CoupleThemeType

/**
 * 成就数据类
 */
data class Achievement(
    val id: String,
    val title: String,
    val description: String,
    val icon: Int,
    val isUnlocked: Boolean,
    val color: Color
)

/**
 * 设置项数据类
 */
data class SettingItem(
    val title: String,
    val subtitle: String,
    val icon: Int,
    val hasSwitch: Boolean = false,
    val switchState: Boolean = false,
    val onClick: () -> Unit = {}
)

/**
 * 个性化个人中心界面
 * 包含用户信息、统计数据、成就系统、设置等
 */
@Composable
fun ProfileScreen(
    modifier: Modifier = Modifier,
    viewModel: ProfileViewModel, // 移除默认值，需要外部传入
    onThemeChanged: (CoupleThemeType) -> Unit = {},
    onSettingClick: (String) -> Unit = {}
) {
    val uiState by viewModel.uiState.collectAsState()
    
    Box(modifier = modifier.fillMaxSize()) {
        RomanticBackground(
            themeType = CoupleThemeType.SWEET_PINK
        ) {
            LazyColumn(
                modifier = Modifier.fillMaxSize(),
                contentPadding = PaddingValues(16.dp),
                verticalArrangement = Arrangement.spacedBy(20.dp)
            ) {
                item {
                    // 个人信息卡片
                    RomanticCard {
                        Column(
                            horizontalAlignment = Alignment.CenterHorizontally,
                            verticalArrangement = Arrangement.spacedBy(16.dp)
                        ) {
                            // 头像
                            Box(
                                modifier = Modifier
                                    .size(100.dp)
                                    .clip(CircleShape)
                                    .background(
                                        brush = Brush.radialGradient(
                                            colors = listOf(
                                                Color(0xFFFF6B9D),
                                                Color(0xFFC44569)
                                            )
                                        )
                                    )
                                    .border(
                                        width = 3.dp,
                                        brush = Brush.radialGradient(
                                            colors = listOf(
                                                Color.White.copy(alpha = 0.8f),
                                                Color.White.copy(alpha = 0.4f)
                                            )
                                        ),
                                        shape = CircleShape
                                    )
                                    .clickable { /* 更换头像 */ },
                                contentAlignment = Alignment.Center
                            ) {
                                Icon(
                                    imageVector = ImageVector.vectorResource(R.drawable.ic_couple_love),
                                    contentDescription = "头像",
                                    modifier = Modifier.size(50.dp),
                                    tint = Color.White
                                )
                            }
                            
                            Column(
                                horizontalAlignment = Alignment.CenterHorizontally,
                                verticalArrangement = Arrangement.spacedBy(4.dp)
                            ) {
                                Text(
                                    text = uiState.userProfile.nickname,
                                    style = MaterialTheme.typography.headlineMedium,
                                    fontWeight = FontWeight.Bold,
                                    color = Color(0xFF2D1B20)
                                )
                                
                                Text(
                                    text = "与 TA 在一起 ${uiState.userProfile.daysInRelationship} 天",
                                    style = MaterialTheme.typography.bodyMedium,
                                    color = Color(0xFF2D1B20).copy(alpha = 0.7f)
                                )
                                
                                Text(
                                    text = "💕 爱情等级：${uiState.userProfile.loveLevel} 💕",
                                    style = MaterialTheme.typography.bodySmall,
                                    color = Color(0xFFE91E63),
                                    fontWeight = FontWeight.Medium
                                )
                            }
                        }
                    }
                }
                
                item {
                    // 统计数据
                    Column(
                        verticalArrangement = Arrangement.spacedBy(12.dp)
                    ) {
                        Text(
                            text = "我们的数据",
                            style = MaterialTheme.typography.headlineSmall,
                            fontWeight = FontWeight.Bold,
                            color = Color.White,
                            modifier = Modifier.padding(horizontal = 4.dp)
                        )
                        
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.spacedBy(12.dp)
                        ) {
                            CoupleStatsCard(
                                title = "消息数",
                                value = uiState.userStats.messageCount,
                                subtitle = "条甜蜜对话",
                                icon = ImageVector.vectorResource(R.drawable.ic_chat_filled),
                                modifier = Modifier.weight(1f),
                                colors = listOf(Color(0xFF9C27B0), Color(0xFF673AB7))
                            )
                            
                            CoupleStatsCard(
                                title = "爱心数",
                                value = uiState.userStats.heartCount,
                                subtitle = "个爱的表达",
                                icon = ImageVector.vectorResource(R.drawable.ic_heart_filled),
                                modifier = Modifier.weight(1f),
                                colors = listOf(Color(0xFFE91E63), Color(0xFFFF5722))
                            )
                        }
                        
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.spacedBy(12.dp)
                        ) {
                            CoupleStatsCard(
                                title = "互动次数",
                                value = uiState.userStats.interactionCount,
                                subtitle = "次亲密互动",
                                icon = ImageVector.vectorResource(R.drawable.ic_vibration),
                                modifier = Modifier.weight(1f),
                                colors = listOf(Color(0xFFFF6B9D), Color(0xFFFF8FA3))
                            )
                            
                            CoupleStatsCard(
                                title = "共同回忆",
                                value = uiState.userStats.photoCount,
                                subtitle = "张珍贵照片",
                                icon = ImageVector.vectorResource(R.drawable.ic_intimate_message),
                                modifier = Modifier.weight(1f),
                                colors = listOf(Color(0xFFFF5722), Color(0xFFFF8A65))
                            )
                        }
                    }
                }
                
                item {
                    // 成就系统
                    RomanticCard {
                        Column(
                            verticalArrangement = Arrangement.spacedBy(16.dp)
                        ) {
                            Text(
                                text = "成就徽章",
                                style = MaterialTheme.typography.headlineSmall,
                                fontWeight = FontWeight.Bold,
                                color = Color(0xFF2D1B20)
                            )
                            
                            LazyRow(
                                horizontalArrangement = Arrangement.spacedBy(12.dp)
                            ) {
                                items(uiState.achievements) { achievement ->
                                    AchievementBadge(
                                        achievement = achievement,
                                        onClick = { viewModel.onAchievementClick(achievement.id) }
                                    )
                                }
                            }
                        }
                    }
                }
                
                item {
                    // 主题选择
                    ThemeSelector()
                }
                
                item {
                    // 设置选项
                    RomanticCard {
                        Column(
                            verticalArrangement = Arrangement.spacedBy(16.dp)
                        ) {
                            Text(
                                text = "设置",
                                style = MaterialTheme.typography.headlineSmall,
                                fontWeight = FontWeight.Bold,
                                color = Color(0xFF2D1B20)
                            )
                            
                            SettingItemRow(
                                title = "夜间模式",
                                subtitle = "保护眼睛，营造浪漫氛围",
                                icon = R.drawable.ic_heart_filled,
                                hasSwitch = true,
                                switchState = uiState.isDarkMode,
                                onSwitchChanged = viewModel::toggleDarkMode
                            )
                            
                            SettingItemRow(
                                title = "隐私模式",
                                subtitle = "隐藏通知内容预览",
                                icon = R.drawable.ic_intimate_message,
                                hasSwitch = true,
                                switchState = uiState.isPrivacyMode,
                                onSwitchChanged = viewModel::togglePrivacyMode
                            )
                            
                            SettingItemRow(
                                title = "推送通知",
                                subtitle = "接收爱人的消息提醒",
                                icon = R.drawable.ic_vibration,
                                hasSwitch = true,
                                switchState = uiState.isNotificationEnabled,
                                onSwitchChanged = viewModel::toggleNotification
                            )
                            
                            SettingItemRow(
                                title = "私密相册",
                                subtitle = "管理我们的甜蜜回忆",
                                icon = R.drawable.ic_couple_love,
                                onClick = { 
                                    viewModel.onSettingClick("private_album")
                                    onSettingClick("private_album") 
                                }
                            )
                            
                            SettingItemRow(
                                title = "数据备份",
                                subtitle = "备份聊天记录和照片",
                                icon = R.drawable.ic_chat_filled,
                                onClick = { 
                                    viewModel.onSettingClick("backup")
                                    onSettingClick("backup") 
                                }
                            )
                            
                            SettingItemRow(
                                title = "关于我们",
                                subtitle = "应用信息和帮助",
                                icon = R.drawable.ic_heart_filled,
                                onClick = { 
                                    viewModel.onSettingClick("about")
                                    onSettingClick("about") 
                                }
                            )
                        }
                    }
                }
                
                item {
                    Spacer(modifier = Modifier.height(20.dp))
                }
            }
        }
    }
}

/**
 * 成就徽章组件
 */
@Composable
private fun AchievementBadge(
    achievement: Achievement,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier
            .clickable { onClick() }
            .padding(8.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        Box(
            modifier = Modifier
                .size(60.dp)
                .clip(CircleShape)
                .background(
                    if (achievement.isUnlocked) {
                        Brush.radialGradient(
                            colors = listOf(
                                achievement.color.copy(alpha = 0.3f),
                                achievement.color.copy(alpha = 0.1f)
                            )
                        )
                    } else {
                        Brush.radialGradient(
                            colors = listOf(
                                Color.Gray.copy(alpha = 0.2f),
                                Color.Gray.copy(alpha = 0.1f)
                            )
                        )
                    }
                )
                .border(
                    width = 2.dp,
                    color = if (achievement.isUnlocked) achievement.color else Color.Gray.copy(alpha = 0.5f),
                    shape = CircleShape
                ),
            contentAlignment = Alignment.Center
        ) {
            Icon(
                imageVector = ImageVector.vectorResource(achievement.icon),
                contentDescription = achievement.title,
                modifier = Modifier.size(30.dp),
                tint = if (achievement.isUnlocked) achievement.color else Color.Gray.copy(alpha = 0.5f)
            )
        }
        
        Text(
            text = achievement.title,
            style = MaterialTheme.typography.bodySmall,
            color = if (achievement.isUnlocked) Color(0xFF2D1B20) else Color(0xFF2D1B20).copy(alpha = 0.5f),
            textAlign = TextAlign.Center,
            fontWeight = if (achievement.isUnlocked) FontWeight.Medium else FontWeight.Normal
        )
    }
}

/**
 * 设置项行组件
 */
@Composable
private fun SettingItemRow(
    title: String,
    subtitle: String,
    icon: Int,
    hasSwitch: Boolean = false,
    switchState: Boolean = false,
    onSwitchChanged: (Boolean) -> Unit = {},
    onClick: () -> Unit = {},
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .clickable { if (!hasSwitch) onClick() }
            .padding(vertical = 8.dp),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Icon(
            imageVector = ImageVector.vectorResource(icon),
            contentDescription = title,
            modifier = Modifier.size(24.dp),
            tint = Color(0xFFFF6B9D)
        )
        
        Column(
            modifier = Modifier.weight(1f),
            verticalArrangement = Arrangement.spacedBy(2.dp)
        ) {
            Text(
                text = title,
                style = MaterialTheme.typography.bodyLarge,
                fontWeight = FontWeight.Medium,
                color = Color(0xFF2D1B20)
            )
            
            Text(
                text = subtitle,
                style = MaterialTheme.typography.bodySmall,
                color = Color(0xFF2D1B20).copy(alpha = 0.7f)
            )
        }
        
        if (hasSwitch) {
            Switch(
                checked = switchState,
                onCheckedChange = onSwitchChanged,
                colors = SwitchDefaults.colors(
                    checkedThumbColor = Color.White,
                    checkedTrackColor = Color(0xFFFF6B9D),
                    uncheckedThumbColor = Color.White,
                    uncheckedTrackColor = Color.Gray.copy(alpha = 0.3f)
                )
            )
        }
    }
}

@Preview(showBackground = true)
@Composable
fun ProfileScreenPreview() {
    CoupleAppTheme {
        // Preview暂时隐藏，需要ViewModel实例
        // ProfileScreen()
        Text("Profile Screen Preview")
    }
}