package com.xue.love.ui.screens.profile

import androidx.compose.ui.graphics.Color
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.xue.love.R
import com.xue.love.data.local.entity.AchievementEntity
import com.xue.love.data.local.entity.UserEntity
import com.xue.love.data.local.entity.UserStatsEntity
import com.xue.love.data.local.entity.CoupleProfileEntity
import com.xue.love.data.repository.CoupleRepository
import com.xue.love.ui.theme.CoupleThemeType
// 暂时禁用Hilt
// import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
// 暂时禁用Hilt
// import javax.inject.Inject

data class UserProfile(
    val nickname: String = "我的爱人",
    val daysInRelationship: Int = 365,
    val loveLevel: String = "甜蜜恋人",
    val avatarUrl: String? = null
)

data class UserStats(
    val messageCount: String = "2,468",
    val heartCount: String = "1,234", 
    val interactionCount: String = "5,678",
    val photoCount: String = "89"
)

data class ProfileUiState(
    val userProfile: UserProfile = UserProfile(),
    val userStats: UserStats = UserStats(),
    val achievements: List<Achievement> = emptyList(),
    val selectedTheme: CoupleThemeType = CoupleThemeType.SWEET_PINK,
    val isDarkMode: Boolean = false,
    val isPrivacyMode: Boolean = false,
    val isNotificationEnabled: Boolean = true,
    val isLoading: Boolean = false,
    val unlockedAchievementCount: Int = 0,
    val totalAchievementCount: Int = 0
)

// 暂时禁用Hilt
// @HiltViewModel
class ProfileViewModel(
    private val repository: CoupleRepository
) : ViewModel() {
    
    private val currentUserId = "current_user" // TODO: 从登录状态获取
    private val coupleId = "couple_id" // TODO: 从登录状态获取
    
    private val _uiState = MutableStateFlow(ProfileUiState())
    val uiState: StateFlow<ProfileUiState> = _uiState.asStateFlow()
    
    init {
        loadProfileData()
        initializeAchievements()
    }
    
    private fun loadProfileData() {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(isLoading = true)
                
                // 观察用户数据
                repository.getCurrentUserFlow().collect { user ->
                    loadUserStats(user)
                }
                
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(isLoading = false)
                // TODO: 处理错误
            }
        }
    }
    
    private fun loadUserStats(user: UserEntity?) {
        viewModelScope.launch {
            try {
                repository.getUserStatsFlow(currentUserId).collect { stats ->
                    loadCoupleProfile(user, stats)
                }
            } catch (e: Exception) {
                // TODO: 处理错误
            }
        }
    }
    
    private fun loadCoupleProfile(user: UserEntity?, stats: UserStatsEntity?) {
        viewModelScope.launch {
            try {
                repository.getCoupleProfileFlow(currentUserId).collect { profile ->
                    loadAchievements(user, stats, profile)
                }
            } catch (e: Exception) {
                // TODO: 处理错误
            }
        }
    }
    
    private fun loadAchievements(user: UserEntity?, stats: UserStatsEntity?, profile: CoupleProfileEntity?) {
        viewModelScope.launch {
            try {
                repository.getAllAchievementsFlow().collect { achievements ->
                    loadPhotoCount(user, stats, profile, achievements)
                }
            } catch (e: Exception) {
                // TODO: 处理错误
            }
        }
    }
    
    private fun loadPhotoCount(
        user: UserEntity?, 
        stats: UserStatsEntity?, 
        profile: CoupleProfileEntity?, 
        achievements: List<AchievementEntity>
    ) {
        viewModelScope.launch {
            try {
                repository.getPhotoCountFlow(coupleId).collect { photoCount ->
                    updateUiState(user, stats, profile, achievements, photoCount)
                }
            } catch (e: Exception) {
                // TODO: 处理错误
            }
        }
    }
    
    private fun updateUiState(
        user: UserEntity?, 
        stats: UserStatsEntity?, 
        profile: CoupleProfileEntity?, 
        achievements: List<AchievementEntity>,
        photoCount: Int?
    ) {
        val userProfile = UserProfile(
            nickname = user?.nickname ?: "我的爱人",
            daysInRelationship = stats?.daysInRelationship ?: 365,
            loveLevel = profile?.loveLevel ?: "甜蜜恋人",
            avatarUrl = user?.avatar
        )
        
        val userStats = UserStats(
            messageCount = stats?.messageCount?.toString() ?: "2,468",
            heartCount = stats?.heartCount?.toString() ?: "1,234",
            interactionCount = stats?.interactionCount?.toString() ?: "5,678",
            photoCount = photoCount?.toString() ?: "89"
        )
        
        val achievementsList = achievements.map { entity ->
            Achievement(
                id = entity.id,
                title = entity.title,
                description = entity.description,
                icon = getIconResourceByName(entity.iconRes),
                isUnlocked = entity.isUnlocked,
                color = Color(entity.colorCode.toLongOrNull(16) ?: 0xFFE91E63)
            )
        }
        
        _uiState.value = _uiState.value.copy(
            userProfile = userProfile,
            userStats = userStats,
            achievements = achievementsList,
            selectedTheme = CoupleThemeType.valueOf(profile?.theme ?: "SWEET_PINK"),
            isDarkMode = profile?.isDarkMode ?: false,
            isPrivacyMode = profile?.isPrivacyMode ?: false,
            isNotificationEnabled = profile?.isNotificationEnabled ?: true,
            unlockedAchievementCount = achievements.count { it.isUnlocked },
            totalAchievementCount = achievements.size,
            isLoading = false
        )
    }
    
    private fun initializeAchievements() {
        viewModelScope.launch {
            try {
                val existingAchievements = repository.getAllAchievementsFlow()
                // 如果没有成就数据，初始化默认成就
                // TODO: 检查是否需要初始化
                val defaultAchievements = getSampleAchievementEntities()
                repository.insertAchievements(defaultAchievements)
            } catch (e: Exception) {
                // TODO: 处理错误
            }
        }
    }
    
    fun updateTheme(theme: CoupleThemeType) {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(selectedTheme = theme)
                repository.updateTheme(currentUserId, theme.name)
            } catch (e: Exception) {
                // TODO: 处理错误
            }
        }
    }
    
    fun toggleDarkMode(enabled: Boolean) {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(isDarkMode = enabled)
                repository.updateDarkMode(currentUserId, enabled)
            } catch (e: Exception) {
                // TODO: 处理错误
            }
        }
    }
    
    fun togglePrivacyMode(enabled: Boolean) {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(isPrivacyMode = enabled)
                repository.updatePrivacyMode(currentUserId, enabled)
            } catch (e: Exception) {
                // TODO: 处理错误
            }
        }
    }
    
    fun toggleNotification(enabled: Boolean) {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(isNotificationEnabled = enabled)
                repository.updateNotificationEnabled(currentUserId, enabled)
            } catch (e: Exception) {
                // TODO: 处理错误
            }
        }
    }
    
    fun updateNickname(nickname: String) {
        viewModelScope.launch {
            try {
                repository.updateUserNickname(currentUserId, nickname)
                repository.updateCoupleNickname(currentUserId, nickname)
            } catch (e: Exception) {
                // TODO: 处理错误
            }
        }
    }
    
    fun updateAvatar(avatarUrl: String) {
        viewModelScope.launch {
            try {
                repository.updateUserAvatar(currentUserId, avatarUrl)
            } catch (e: Exception) {
                // TODO: 处理错误
            }
        }
    }
    
    fun onSettingClick(settingKey: String) {
        when (settingKey) {
            "private_album" -> {
                // TODO: 导航到私密相册
            }
            "backup" -> {
                startDataBackup()
            }
            "about" -> {
                // TODO: 导航到关于页面
            }
        }
    }
    
    fun onAchievementClick(achievementId: String) {
        // TODO: 显示成就详情
    }
    
    private fun startDataBackup() {
        viewModelScope.launch {
            try {
                // TODO: 实现数据备份逻辑
            } catch (e: Exception) {
                // TODO: 处理错误
            }
        }
    }
    
    private fun getIconResourceByName(iconName: String): Int {
        return when (iconName) {
            "ic_heart_filled" -> R.drawable.ic_heart_filled
            "ic_chat_filled" -> R.drawable.ic_chat_filled
            "ic_couple_love" -> R.drawable.ic_couple_love
            "ic_vibration" -> R.drawable.ic_vibration
            else -> R.drawable.ic_heart_filled
        }
    }
    
    private fun getSampleAchievementEntities(): List<AchievementEntity> {
        return listOf(
            AchievementEntity(
                id = "1",
                title = "初次相遇",
                description = "完成第一次配对",
                iconRes = "ic_heart_filled",
                colorCode = "FFE91E63",
                category = "milestone",
                isUnlocked = true,
                unlockedAt = System.currentTimeMillis()
            ),
            AchievementEntity(
                id = "2",
                title = "甜蜜聊天",
                description = "发送100条消息",
                iconRes = "ic_chat_filled",
                colorCode = "FF9C27B0",
                category = "chat",
                targetValue = 100,
                currentValue = 100,
                isUnlocked = true,
                unlockedAt = System.currentTimeMillis()
            ),
            AchievementEntity(
                id = "3",
                title = "爱心达人",
                description = "发送50个爱心",
                iconRes = "ic_heart_filled",
                colorCode = "FFFF6B9D",
                category = "interaction",
                targetValue = 50,
                currentValue = 50,
                isUnlocked = true,
                unlockedAt = System.currentTimeMillis()
            ),
            AchievementEntity(
                id = "4",
                title = "亲密无间",
                description = "亲密度达到90",
                iconRes = "ic_couple_love",
                colorCode = "FFFF5722",
                category = "intimacy",
                targetValue = 90,
                currentValue = 75,
                isUnlocked = false
            ),
            AchievementEntity(
                id = "5",
                title = "长久陪伴",
                description = "在一起100天",
                iconRes = "ic_heart_filled",
                colorCode = "FF673AB7",
                category = "milestone",
                targetValue = 100,
                currentValue = 365,
                isUnlocked = false
            )
        )
    }
}