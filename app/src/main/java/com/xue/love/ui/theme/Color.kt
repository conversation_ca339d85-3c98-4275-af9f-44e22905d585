package com.xue.love.ui.theme

import androidx.compose.ui.graphics.Color

// 情侣APP专用配色方案

// ========== 甜蜜粉主题色彩 ==========
// 主色调 - 浪漫粉色系
val SweetPink80 = Color(0xFFFFB3D1)
val SweetPink60 = Color(0xFFFF6B9D)
val SweetPink40 = Color(0xFFFF5E8A)
val SweetPink20 = Color(0xFFC44569)

// 辅助色 - 紫色系
val SweetPurple80 = Color(0xFFD1C4E9)
val SweetPurple60 = Color(0xFF9C88DA)
val SweetPurple40 = Color(0xFF7B68EE)
val SweetPurple20 = Color(0xFF5E4BA3)

// ========== 激情红主题色彩 ==========
val PassionRed80 = Color(0xFFFFCDD2)
val PassionRed60 = Color(0xFFE91E63)
val PassionRed40 = Color(0xFFAD1457)
val PassionRed20 = Color(0xFF880E4F)

val PassionOrange80 = Color(0xFFFFE0B2)
val PassionOrange60 = Color(0xFFFF5722)
val PassionOrange40 = Color(0xFFE64A19)
val PassionOrange20 = Color(0xFFBF360C)

// ========== 神秘紫主题色彩 ==========
val MysteryPurple80 = Color(0xFFEDE7F6)
val MysteryPurple60 = Color(0xFF673AB7)
val MysteryPurple40 = Color(0xFF512DA8)
val MysteryPurple20 = Color(0xFF311B92)

val MysteryViolet80 = Color(0xFFF3E5F5)
val MysteryViolet60 = Color(0xFF9C27B0)
val MysteryViolet40 = Color(0xFF7B1FA2)
val MysteryViolet20 = Color(0xFF4A148C)

// ========== 优雅黑主题色彩 ==========
val ElegantGrey80 = Color(0xFFE0E0E0)
val ElegantGrey60 = Color(0xFF757575)
val ElegantGrey40 = Color(0xFF424242)
val ElegantGrey20 = Color(0xFF212121)

val ElegantSilver80 = Color(0xFFEEEEEE)
val ElegantSilver60 = Color(0xFFBDBDBD)
val ElegantSilver40 = Color(0xFF9E9E9E)
val ElegantSilver20 = Color(0xFF616161)

// ========== 通用中性色 ==========
val Grey90 = Color(0xFFF5F5F5)
val Grey80 = Color(0xFFE0E0E0)
val Grey60 = Color(0xFF9E9E9E)
val Grey40 = Color(0xFF616161)
val Grey20 = Color(0xFF212121)

// ========== 功能色 ==========
val Success = Color(0xFF4CAF50)
val Warning = Color(0xFFFF9800)
val Error = Color(0xFFF44336)
val Info = Color(0xFF2196F3)

// ========== 渐变色定义 ==========
// 甜蜜粉渐变
val SweetPinkGradientStart = Color(0xFFFF6B9D)
val SweetPinkGradientEnd = Color(0xFFC44569)
val SweetPinkGradientLight = Color(0xFFFFB3D1)

// 激情红渐变
val PassionRedGradientStart = Color(0xFFE91E63)
val PassionRedGradientEnd = Color(0xFFFF5722)
val PassionRedGradientLight = Color(0xFFFFCDD2)

// 神秘紫渐变
val MysteryPurpleGradientStart = Color(0xFF673AB7)
val MysteryPurpleGradientEnd = Color(0xFF9C27B0)
val MysteryPurpleGradientLight = Color(0xFFEDE7F6)

// 优雅黑渐变
val ElegantBlackGradientStart = Color(0xFF424242)
val ElegantBlackGradientEnd = Color(0xFF757575)
val ElegantBlackGradientLight = Color(0xFFE0E0E0)

// ========== 夜间模式色彩 ==========
val DarkSweetPink = Color(0xFF8E4162)
val DarkSweetPurple = Color(0xFF4A3B6B)
val DarkPassionRed = Color(0xFF6D1B3B)
val DarkMysteryPurple = Color(0xFF3E2A5C)
val DarkElegantGrey = Color(0xFF2C2C2C)

val DarkBackground = Color(0xFF121212)
val DarkSurface = Color(0xFF1E1E1E)
val DarkSurfaceVariant = Color(0xFF2A2A2A)

// ========== 透明度变体 ==========
// 甜蜜粉透明度
val SweetPink60Alpha80 = Color(0xCCFF6B9D)
val SweetPink60Alpha50 = Color(0x80FF6B9D)
val SweetPink60Alpha30 = Color(0x4DFF6B9D)
val SweetPink60Alpha10 = Color(0x1AFF6B9D)

// 激情红透明度
val PassionRed60Alpha80 = Color(0xCCE91E63)
val PassionRed60Alpha50 = Color(0x80E91E63)
val PassionRed60Alpha30 = Color(0x4DE91E63)
val PassionRed60Alpha10 = Color(0x1AE91E63)

// 神秘紫透明度
val MysteryPurple60Alpha80 = Color(0xCC673AB7)
val MysteryPurple60Alpha50 = Color(0x80673AB7)
val MysteryPurple60Alpha30 = Color(0x4D673AB7)
val MysteryPurple60Alpha10 = Color(0x1A673AB7)

// 优雅黑透明度
val ElegantGrey60Alpha80 = Color(0xCC757575)
val ElegantGrey60Alpha50 = Color(0x80757575)
val ElegantGrey60Alpha30 = Color(0x4D757575)
val ElegantGrey60Alpha10 = Color(0x1A757575)

// ========== 兼容性别名 ==========
@Deprecated("使用 SweetPink80 替代", ReplaceWith("SweetPink80"))
val Pink80 = SweetPink80
@Deprecated("使用 SweetPink60 替代", ReplaceWith("SweetPink60"))
val Pink60 = SweetPink60
@Deprecated("使用 SweetPink40 替代", ReplaceWith("SweetPink40"))
val Pink40 = SweetPink40
@Deprecated("使用 SweetPink20 替代", ReplaceWith("SweetPink20"))
val Pink20 = SweetPink20

@Deprecated("使用 SweetPurple80 替代", ReplaceWith("SweetPurple80"))
val Purple80 = SweetPurple80
@Deprecated("使用 SweetPurple60 替代", ReplaceWith("SweetPurple60"))
val Purple60 = SweetPurple60
@Deprecated("使用 SweetPurple40 替代", ReplaceWith("SweetPurple40"))
val Purple40 = SweetPurple40
@Deprecated("使用 SweetPurple20 替代", ReplaceWith("SweetPurple20"))
val Purple20 = SweetPurple20

@Deprecated("使用 SweetPinkGradientStart 替代", ReplaceWith("SweetPinkGradientStart"))
val PinkGradientStart = SweetPinkGradientStart
@Deprecated("使用 SweetPinkGradientEnd 替代", ReplaceWith("SweetPinkGradientEnd"))
val PinkGradientEnd = SweetPinkGradientEnd
@Deprecated("使用 SweetPinkGradientStart 替代", ReplaceWith("SweetPinkGradientStart"))
val PurpleGradientStart = SweetPinkGradientStart
@Deprecated("使用 SweetPinkGradientEnd 替代", ReplaceWith("SweetPinkGradientEnd"))
val PurpleGradientEnd = SweetPinkGradientEnd

@Deprecated("使用 DarkSweetPink 替代", ReplaceWith("DarkSweetPink"))
val DarkPink = DarkSweetPink
@Deprecated("使用 DarkSweetPurple 替代", ReplaceWith("DarkSweetPurple"))
val DarkPurple = DarkSweetPurple

@Deprecated("使用 SweetPink60Alpha50 替代", ReplaceWith("SweetPink60Alpha50"))
val Pink60Alpha50 = SweetPink60Alpha50
@Deprecated("使用 SweetPink60Alpha30 替代", ReplaceWith("SweetPink60Alpha30"))
val Pink60Alpha30 = SweetPink60Alpha30
@Deprecated("使用 SweetPink60Alpha10 替代", ReplaceWith("SweetPink60Alpha10"))
val Pink60Alpha10 = SweetPink60Alpha10