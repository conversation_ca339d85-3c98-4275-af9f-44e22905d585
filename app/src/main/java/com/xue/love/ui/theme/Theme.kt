package com.xue.love.ui.theme

import android.app.Activity
import android.os.Build
import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.material3.ColorScheme
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.darkColorScheme
import androidx.compose.material3.dynamicDarkColorScheme
import androidx.compose.material3.dynamicLightColorScheme
import androidx.compose.material3.lightColorScheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.SideEffect
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalView
import androidx.core.view.WindowCompat

/**
 * 情侣APP主题类型枚举
 * 提供多种浪漫主题选择
 */
enum class CoupleThemeType {
    SWEET_PINK,      // 甜蜜粉
    PASSION_RED,     // 激情红
    MYSTERY_PURPLE,  // 神秘紫
    ELEGANT_BLACK    // 优雅黑
}

// ========== 甜蜜粉主题配色 ==========
private val SweetPinkLightColorScheme = lightColorScheme(
    primary = SweetPink60,
    onPrimary = androidx.compose.ui.graphics.Color.White,
    primaryContainer = SweetPink80,
    onPrimaryContainer = SweetPink20,
    
    secondary = SweetPurple60,
    onSecondary = androidx.compose.ui.graphics.Color.White,
    secondaryContainer = SweetPurple80,
    onSecondaryContainer = SweetPurple20,
    
    tertiary = SweetPink40,
    onTertiary = androidx.compose.ui.graphics.Color.White,
    tertiaryContainer = SweetPink80,
    onTertiaryContainer = SweetPink20,
    
    background = androidx.compose.ui.graphics.Color.White,
    onBackground = Grey20,
    surface = androidx.compose.ui.graphics.Color.White,
    onSurface = Grey20,
    
    surfaceVariant = Grey90,
    onSurfaceVariant = Grey40,
    
    error = Error,
    onError = androidx.compose.ui.graphics.Color.White,
    errorContainer = androidx.compose.ui.graphics.Color(0xFFFFDAD6),
    onErrorContainer = androidx.compose.ui.graphics.Color(0xFF410002),
    
    outline = Grey60,
    outlineVariant = Grey80
)

private val SweetPinkDarkColorScheme = darkColorScheme(
    primary = SweetPink80,
    onPrimary = SweetPink20,
    primaryContainer = DarkSweetPink,
    onPrimaryContainer = SweetPink80,
    
    secondary = SweetPurple80,
    onSecondary = SweetPurple20,
    secondaryContainer = DarkSweetPurple,
    onSecondaryContainer = SweetPurple80,
    
    tertiary = SweetPink80,
    onTertiary = SweetPink20,
    tertiaryContainer = DarkSweetPink,
    onTertiaryContainer = SweetPink80,
    
    background = DarkBackground,
    onBackground = Grey90,
    surface = DarkSurface,
    onSurface = Grey90,
    
    surfaceVariant = DarkSurfaceVariant,
    onSurfaceVariant = Grey80,
    
    error = androidx.compose.ui.graphics.Color(0xFFFFB4AB),
    onError = androidx.compose.ui.graphics.Color(0xFF690005),
    errorContainer = androidx.compose.ui.graphics.Color(0xFF93000A),
    onErrorContainer = androidx.compose.ui.graphics.Color(0xFFFFDAD6),
    
    outline = Grey60,
    outlineVariant = Grey40
)

// ========== 激情红主题配色 ==========
private val PassionRedLightColorScheme = lightColorScheme(
    primary = PassionRed60,
    onPrimary = androidx.compose.ui.graphics.Color.White,
    primaryContainer = PassionRed80,
    onPrimaryContainer = PassionRed20,
    
    secondary = PassionOrange60,
    onSecondary = androidx.compose.ui.graphics.Color.White,
    secondaryContainer = PassionOrange80,
    onSecondaryContainer = PassionOrange20,
    
    tertiary = PassionRed40,
    onTertiary = androidx.compose.ui.graphics.Color.White,
    tertiaryContainer = PassionRed80,
    onTertiaryContainer = PassionRed20,
    
    background = androidx.compose.ui.graphics.Color.White,
    onBackground = Grey20,
    surface = androidx.compose.ui.graphics.Color.White,
    onSurface = Grey20,
    
    surfaceVariant = Grey90,
    onSurfaceVariant = Grey40,
    
    error = Error,
    onError = androidx.compose.ui.graphics.Color.White,
    errorContainer = androidx.compose.ui.graphics.Color(0xFFFFDAD6),
    onErrorContainer = androidx.compose.ui.graphics.Color(0xFF410002),
    
    outline = Grey60,
    outlineVariant = Grey80
)

private val PassionRedDarkColorScheme = darkColorScheme(
    primary = PassionRed80,
    onPrimary = PassionRed20,
    primaryContainer = DarkPassionRed,
    onPrimaryContainer = PassionRed80,
    
    secondary = PassionOrange80,
    onSecondary = PassionOrange20,
    secondaryContainer = PassionOrange40,
    onSecondaryContainer = PassionOrange80,
    
    tertiary = PassionRed80,
    onTertiary = PassionRed20,
    tertiaryContainer = DarkPassionRed,
    onTertiaryContainer = PassionRed80,
    
    background = DarkBackground,
    onBackground = Grey90,
    surface = DarkSurface,
    onSurface = Grey90,
    
    surfaceVariant = DarkSurfaceVariant,
    onSurfaceVariant = Grey80,
    
    error = androidx.compose.ui.graphics.Color(0xFFFFB4AB),
    onError = androidx.compose.ui.graphics.Color(0xFF690005),
    errorContainer = androidx.compose.ui.graphics.Color(0xFF93000A),
    onErrorContainer = androidx.compose.ui.graphics.Color(0xFFFFDAD6),
    
    outline = Grey60,
    outlineVariant = Grey40
)

// ========== 神秘紫主题配色 ==========
private val MysteryPurpleLightColorScheme = lightColorScheme(
    primary = MysteryPurple60,
    onPrimary = androidx.compose.ui.graphics.Color.White,
    primaryContainer = MysteryPurple80,
    onPrimaryContainer = MysteryPurple20,
    
    secondary = MysteryViolet60,
    onSecondary = androidx.compose.ui.graphics.Color.White,
    secondaryContainer = MysteryViolet80,
    onSecondaryContainer = MysteryViolet20,
    
    tertiary = MysteryPurple40,
    onTertiary = androidx.compose.ui.graphics.Color.White,
    tertiaryContainer = MysteryPurple80,
    onTertiaryContainer = MysteryPurple20,
    
    background = androidx.compose.ui.graphics.Color.White,
    onBackground = Grey20,
    surface = androidx.compose.ui.graphics.Color.White,
    onSurface = Grey20,
    
    surfaceVariant = Grey90,
    onSurfaceVariant = Grey40,
    
    error = Error,
    onError = androidx.compose.ui.graphics.Color.White,
    errorContainer = androidx.compose.ui.graphics.Color(0xFFFFDAD6),
    onErrorContainer = androidx.compose.ui.graphics.Color(0xFF410002),
    
    outline = Grey60,
    outlineVariant = Grey80
)

private val MysteryPurpleDarkColorScheme = darkColorScheme(
    primary = MysteryPurple80,
    onPrimary = MysteryPurple20,
    primaryContainer = DarkMysteryPurple,
    onPrimaryContainer = MysteryPurple80,
    
    secondary = MysteryViolet80,
    onSecondary = MysteryViolet20,
    secondaryContainer = MysteryViolet40,
    onSecondaryContainer = MysteryViolet80,
    
    tertiary = MysteryPurple80,
    onTertiary = MysteryPurple20,
    tertiaryContainer = DarkMysteryPurple,
    onTertiaryContainer = MysteryPurple80,
    
    background = DarkBackground,
    onBackground = Grey90,
    surface = DarkSurface,
    onSurface = Grey90,
    
    surfaceVariant = DarkSurfaceVariant,
    onSurfaceVariant = Grey80,
    
    error = androidx.compose.ui.graphics.Color(0xFFFFB4AB),
    onError = androidx.compose.ui.graphics.Color(0xFF690005),
    errorContainer = androidx.compose.ui.graphics.Color(0xFF93000A),
    onErrorContainer = androidx.compose.ui.graphics.Color(0xFFFFDAD6),
    
    outline = Grey60,
    outlineVariant = Grey40
)

// ========== 优雅黑主题配色 ==========
private val ElegantBlackLightColorScheme = lightColorScheme(
    primary = ElegantGrey40,
    onPrimary = androidx.compose.ui.graphics.Color.White,
    primaryContainer = ElegantGrey80,
    onPrimaryContainer = ElegantGrey20,
    
    secondary = ElegantSilver60,
    onSecondary = androidx.compose.ui.graphics.Color.White,
    secondaryContainer = ElegantSilver80,
    onSecondaryContainer = ElegantSilver20,
    
    tertiary = ElegantGrey60,
    onTertiary = androidx.compose.ui.graphics.Color.White,
    tertiaryContainer = ElegantGrey80,
    onTertiaryContainer = ElegantGrey20,
    
    background = androidx.compose.ui.graphics.Color.White,
    onBackground = Grey20,
    surface = androidx.compose.ui.graphics.Color.White,
    onSurface = Grey20,
    
    surfaceVariant = Grey90,
    onSurfaceVariant = Grey40,
    
    error = Error,
    onError = androidx.compose.ui.graphics.Color.White,
    errorContainer = androidx.compose.ui.graphics.Color(0xFFFFDAD6),
    onErrorContainer = androidx.compose.ui.graphics.Color(0xFF410002),
    
    outline = Grey60,
    outlineVariant = Grey80
)

private val ElegantBlackDarkColorScheme = darkColorScheme(
    primary = ElegantSilver60,
    onPrimary = ElegantGrey20,
    primaryContainer = DarkElegantGrey,
    onPrimaryContainer = ElegantGrey80,
    
    secondary = ElegantSilver40,
    onSecondary = ElegantGrey20,
    secondaryContainer = ElegantSilver20,
    onSecondaryContainer = ElegantSilver80,
    
    tertiary = ElegantSilver60,
    onTertiary = ElegantGrey20,
    tertiaryContainer = DarkElegantGrey,
    onTertiaryContainer = ElegantGrey80,
    
    background = DarkBackground,
    onBackground = Grey90,
    surface = DarkSurface,
    onSurface = Grey90,
    
    surfaceVariant = DarkSurfaceVariant,
    onSurfaceVariant = Grey80,
    
    error = androidx.compose.ui.graphics.Color(0xFFFFB4AB),
    onError = androidx.compose.ui.graphics.Color(0xFF690005),
    errorContainer = androidx.compose.ui.graphics.Color(0xFF93000A),
    onErrorContainer = androidx.compose.ui.graphics.Color(0xFFFFDAD6),
    
    outline = Grey60,
    outlineVariant = Grey40
)

// ========== 兼容性别名 ==========
@Deprecated("使用 SweetPinkLightColorScheme 替代", ReplaceWith("SweetPinkLightColorScheme"))
private val LightColorScheme = SweetPinkLightColorScheme

@Deprecated("使用 SweetPinkDarkColorScheme 替代", ReplaceWith("SweetPinkDarkColorScheme"))
private val DarkColorScheme = SweetPinkDarkColorScheme

/**
 * 根据主题类型获取对应的颜色方案
 */
private fun getThemeColorScheme(themeType: CoupleThemeType, isDark: Boolean): ColorScheme {
    return when (themeType) {
        CoupleThemeType.SWEET_PINK -> if (isDark) SweetPinkDarkColorScheme else SweetPinkLightColorScheme
        CoupleThemeType.PASSION_RED -> if (isDark) PassionRedDarkColorScheme else PassionRedLightColorScheme
        CoupleThemeType.MYSTERY_PURPLE -> if (isDark) MysteryPurpleDarkColorScheme else MysteryPurpleLightColorScheme
        CoupleThemeType.ELEGANT_BLACK -> if (isDark) ElegantBlackDarkColorScheme else ElegantBlackLightColorScheme
    }
}

/**
 * 情侣APP主题
 * 支持浅色/深色模式、动态颜色和多套情趣主题
 */
@Composable
fun CoupleAppTheme(
    darkTheme: Boolean = isSystemInDarkTheme(),
    // 动态颜色在Android 12+上可用
    dynamicColor: Boolean = false, // 默认关闭动态颜色，使用自定义主题
    themeType: CoupleThemeType = CoupleThemeType.SWEET_PINK,
    content: @Composable () -> Unit
) {
    val colorScheme = when {
        dynamicColor && Build.VERSION.SDK_INT >= Build.VERSION_CODES.S -> {
            val context = LocalContext.current
            if (darkTheme) dynamicDarkColorScheme(context) else dynamicLightColorScheme(context)
        }
        else -> getThemeColorScheme(themeType, darkTheme)
    }
    
    val view = LocalView.current
    if (!view.isInEditMode) {
        SideEffect {
            val window = (view.context as Activity).window
            window.statusBarColor = colorScheme.primary.toArgb()
            WindowCompat.getInsetsController(window, view).isAppearanceLightStatusBars = !darkTheme
        }
    }

    MaterialTheme(
        colorScheme = colorScheme,
        typography = Typography,
        content = content
    )
}

/**
 * 带主题选择的情侣APP主题
 */
@Composable
fun CoupleAppThemeWithSelection(
    themeType: CoupleThemeType = CoupleThemeType.SWEET_PINK,
    darkTheme: Boolean = isSystemInDarkTheme(),
    content: @Composable () -> Unit
) {
    CoupleAppTheme(
        darkTheme = darkTheme,
        dynamicColor = false,
        themeType = themeType,
        content = content
    )
}