package com.xue.love.ui.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import com.xue.love.data.repository.CoupleRepository
import com.xue.love.data.repository.IntimateRecordRepository
import com.xue.love.data.repository.MenstrualCycleRepository
import com.xue.love.data.repository.IntimateAppointmentRepository
import com.xue.love.ui.screens.home.HomeViewModel
import com.xue.love.ui.screens.chat.ChatViewModel
import com.xue.love.ui.screens.profile.ProfileViewModel
import com.xue.love.ui.screens.intimate.IntimateRecordViewModel
import com.xue.love.ui.screens.menstrual.MenstrualCycleViewModel
import com.xue.love.ui.screens.appointment.IntimateAppointmentViewModel

/**
 * ViewModel工厂类
 * 替代Hilt的临时解决方案
 */
class ViewModelFactory(
    private val repository: CoupleRepository,
    private val intimateRecordRepository: IntimateRecordRepository,
    private val menstrualCycleRepository: MenstrualCycleRepository,
    private val intimateAppointmentRepository: IntimateAppointmentRepository
) : ViewModelProvider.Factory {
    
    @Suppress("UNCHECKED_CAST")
    override fun <T : ViewModel> create(modelClass: Class<T>): T {
        return when (modelClass) {
            HomeViewModel::class.java -> HomeViewModel(repository) as T
            ChatViewModel::class.java -> ChatViewModel(repository) as T
            ProfileViewModel::class.java -> ProfileViewModel(repository) as T
            IntimateRecordViewModel::class.java -> IntimateRecordViewModel(intimateRecordRepository) as T
            MenstrualCycleViewModel::class.java -> MenstrualCycleViewModel(menstrualCycleRepository) as T
            IntimateAppointmentViewModel::class.java -> IntimateAppointmentViewModel(intimateAppointmentRepository) as T
            else -> throw IllegalArgumentException("Unknown ViewModel class: ${modelClass.name}")
        }
    }
}