<?xml version="1.0" encoding="utf-8"?>
<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="108dp"
    android:height="108dp"
    android:viewportWidth="108"
    android:viewportHeight="108">
    
    <!-- 渐变背景 -->
    <path android:pathData="M0,0h108v108h-108z">
        <aapt:attr name="android:fillColor" xmlns:aapt="http://schemas.android.com/aapt">
            <gradient 
                android:startColor="#FF6B9D"
                android:endColor="#C44569"
                android:centerColor="#FF5E8A"
                android:type="radial"
                android:centerX="54"
                android:centerY="54"
                android:gradientRadius="60"/>
        </aapt:attr>
    </path>
    
    <!-- 主爱心图标 -->
    <path
        android:fillColor="#FFFFFF"
        android:pathData="M54,75.6c-1.2,0 -2.4,-0.48 -3.24,-1.32C42.12,66.84 24,48.72 24,36c0,-9.96 8.04,-18 18,-18c4.8,0 9.24,1.92 12.48,5.4L54,24l-0.48,-0.6C56.76,19.92 61.2,18 66,18c9.96,0 18,8.04 18,18c0,12.72 -18.12,30.84 -26.76,38.28C56.4,75.12 55.2,75.6 54,75.6z"/>
    
    <!-- 小爱心装饰 -->
    <path
        android:fillColor="#FFE0E6"
        android:pathData="M72,30c-1.32,0 -2.4,-1.08 -2.4,-2.4c0,-3.96 3.24,-7.2 7.2,-7.2s7.2,3.24 7.2,7.2c0,5.04 -7.2,12 -7.2,12S72,35.04 72,30z"/>
    
    <path
        android:fillColor="#FFE0E6"
        android:pathData="M30,84c-1.32,0 -2.4,-1.08 -2.4,-2.4c0,-3.96 3.24,-7.2 7.2,-7.2s7.2,3.24 7.2,7.2c0,5.04 -7.2,12 -7.2,12S30,89.04 30,84z"/>
</vector>